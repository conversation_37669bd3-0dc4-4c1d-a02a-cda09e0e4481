import { memo, useMemo } from "react";
import Container from "../../container";

function ListHeader() {
  const { columns, headerHeight } = Container.useContainer();

  const headerSyleValue: React.CSSProperties = useMemo(() => {
    return {
      height: headerHeight,
      backgroundColor: "rgba(22,46,71,.85)",
    };
  }, [headerHeight]);

  return (
    <div
      className="flex px-3 items-center w-full rounded-[2px]"
      style={headerSyleValue}
    >
      {columns?.map((val, index) => (
        <div
          key={val.key ?? index}
          className={`truncate color-secondary px-2 ${val.className}`}
          style={{
            flex: val.width ? `${val.width}px` : 1,
            maxWidth: val.width,
            textAlign: val.align,
          }}
        >
          {val.title?.toString()}
        </div>
      ))}
    </div>
  );
}
export default memo(ListHeader);
