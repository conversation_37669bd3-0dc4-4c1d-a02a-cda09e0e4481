import React from "react";

interface IProps
  extends React.DetailedHTMLProps<
    React.HTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  children?: React.ReactNode;
}
function Title(props: IProps) {
  const { children, ...rest } = props;
  return (
    <div
      style={{
        backgroundImage:
          "linear-gradient(0deg, #A6DDEF 0%, #F3FEFF 54%,#9EB7FF 100%)",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        fontFamily: "Alibaba-PuHuiTi-Bold",
      }}
      {...rest}
    >
      {children}
    </div>
  );
}
export default Title;
