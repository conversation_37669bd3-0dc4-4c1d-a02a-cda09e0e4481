import { useThree } from "@react-three/fiber";
import { useDeepCompareLayoutEffect } from "ahooks";
import { useState } from "react";

interface IProps {
  width: number;
  height: number;
}
function ScaleCanvas(props: IProps) {
  const { width, height } = props;

  const state = useThree();

  const set = useThree((state) => state.set);

  const [setSize] = useState(() => state.setSize);

  useDeepCompareLayoutEffect(() => {
    setSize(width, height);
    set({ setSize: () => null });
    return () => set({ setSize });
  }, [width, height]);

  return null;
}
export default ScaleCanvas;
