import type { LinearConfig } from "@/typings";
import { nanoid } from "nanoid";
import { useRef, useState } from "react";
import type { Props as LengendProps } from "recharts/types/component/Legend";
import type { ContentType } from "recharts/types/component/Tooltip";
import type {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import {
  Area,
  AreaChart,
  CartesianGrid,
  Legend,
  Tooltip,
  Brush,
  XAxis,
  YAxis,
} from "recharts";
import lodash from "lodash-es";
import SvgLinearGradient from "../Comp/SvgLinearGradient";
import lineIcon from "@/assets/lineIcon.png";
import { useMemoizedFn } from "ahooks";
import type { DataKey } from "recharts/types/util/types";

interface IProps {
  data: any[];

  width: number;

  height: number;

  coloumns: Coloumns;

  xDataKey: string;

  yAxisWidth?: number;
}

interface ColoumnType {
  color: string;

  area: LinearConfig;

  dataKey: string;

  name: string;
}
export type Coloumns = ColoumnType[];

function BasicLine(props: IProps) {
  const { data, width, height, coloumns, xDataKey, yAxisWidth = 20 } = props;
  const customXTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;
    return (
      <g>
        <foreignObject
          style={{
            overflow: "visible",
          }}
          width="1"
          height="1"
          x={x}
          y={y + 5}
        >
          <div className="color-chart">
            <div
              style={{
                width: "fit-content",
                transform: `translate(-50%, 0px) rotate(0deg)`,
                textAlign: "center",
              }}
              className="truncate"
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const customYTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;

    return (
      <g>
        <foreignObject
          width="1"
          height="1"
          x={x}
          y={y}
          style={{
            overflow: "visible",
          }}
        >
          <div className="color-chart">
            <div
              style={{
                width: "max-content",
                transform: `translate(-100%, -50%)`,
                textAlign: "right",
              }}
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const filterIdPrefix = useRef(nanoid());

  // dataKey的隐藏列表
  const [hideDataKeyList, setHideDataKeyList] = useState<DataKey<any>[]>([]);

  // 轴单位
  // const customizedLabel = useCallback((props) => {
  //   return (
  //     <g>
  //       <foreignObject
  //         width="1"
  //         height="1"
  //         x={props.viewBox.x}
  //         y={props.viewBox.y}
  //         style={{
  //           overflow: "visible",
  //         }}
  //       >
  //         <div
  //           style={{
  //             width: "max-content",
  //             transform: "translate(-20px, -40px)",
  //           }}
  //           className="color-chart"
  //         >
  //           (次数)
  //         </div>
  //       </foreignObject>
  //     </g>
  //   );
  // }, []);

  const onLegendToggle = useMemoizedFn((dataKey?: DataKey<any>) => {
    if (dataKey) {
      const isHide = hideDataKeyList.includes(dataKey);
      setHideDataKeyList((val) => {
        if (isHide) {
          return val.filter((item) => item !== dataKey);
        }
        return [...val, dataKey];
      });
    }
  });

  const legendContent = useMemoizedFn((e: LengendProps) => {
    const { payload = [] } = e;
    return (
      <div className="absolute right-[-20px] top-[4px] flex justify-end gap-x-4">
        {payload.map((val, index) => (
          <div
            className="flex items-center cursor-pointer"
            key={`legend${index}`}
            onClick={() => onLegendToggle(val.dataKey)}
          >
            <div
              className="w-[16px] h-[4px]"
              style={{
                backgroundColor: val.color,
              }}
            ></div>
            <span className="ml-2 text-[rgba(255,255,255,0.65)] text-[14px]">
              {val.value}
            </span>
          </div>
        ))}
      </div>
    );
  });

  const CustomTooltip: ContentType<ValueType, NameType> = ({
    active,
    payload,
  }) => {
    if (active && payload && payload.length) {
      return (
        <div
          className="w-fit justify-center color-text bg-[rgba(50,91,174,0.4)] rounded-[4px] px-5 py-2"
          style={{
            boxShadow: "inset 0px 0 20px 0px #5FC1FF",
            backdropFilter: "blur(12px)",
          }}
        >
          {payload.map((val) => (
            <div className="flex items-center gap-x-3" key={val.dataKey}>
              <span className="text-[16px] w-auto">{val.name}</span>
              <span
                className="text-[22px]"
                style={{
                  fontFamily: "DINCond-Bold",
                  color: val.color,
                }}
              >
                {val.value}
              </span>
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  const renderTraveller = (props: {
    x: number;
    y: number;
    width: number;
    height: number;
  }) => {
    const { x, y, height } = props;
    const isRight = x > width / 2;
    const xValue = isRight ? x - 18 : x - 12;
    return (
      <image
        href={lineIcon}
        x={xValue}
        y={y + (height - 32) / 2 + 1}
        width="32"
        height="32"
        transform="scaleX(-1) translate(-18, 0)"
      />
    );
  };

  return (
    <AreaChart
      width={width}
      height={height}
      data={data}
      className="text-[14px]"
      margin={{
        top: 36,
        right: 20,
        left: 20,
        bottom: 10,
      }}
    >
      <CartesianGrid vertical={false} stroke="rgba(168, 168, 168, 0.2)" />
      <XAxis
        dataKey={xDataKey}
        tick={customXTick}
        height={46}
        tickLine={false}
        axisLine={{
          stroke: "rgba(201, 201, 201, 0.55)",
        }}
        interval="equidistantPreserveStart"
        minTickGap={0}
      />
      <YAxis
        tickLine={false}
        axisLine={false}
        tick={customYTick}
        width={yAxisWidth}
      />
      {coloumns.map((val) => {
        const idPrefix = `${filterIdPrefix.current}-${val.dataKey}`;
        return (
          <defs key={idPrefix}>
            <SvgLinearGradient id={`${idPrefix}`} config={val.area} />
          </defs>
        );
      })}
      {coloumns.map((val) => (
        <Area
          key={val.dataKey}
          dataKey={val.dataKey}
          type="monotone"
          dot={false}
          strokeWidth={2}
          stroke={val.color}
          fill={`url(#${filterIdPrefix.current}-${val.dataKey})`}
          label={false}
          name={val.name}
          hide={hideDataKeyList.includes(val.dataKey)}
        />
      ))}
      <Legend
        wrapperStyle={{
          position: "absolute",
          left: 0,
          top: 0,
          height: 0,
        }}
        content={legendContent}
      />
      <Tooltip
        content={<CustomTooltip />}
        wrapperStyle={{
          width: "max-content",
        }}
      />
      <Brush
        alwaysShowText
        height={16}
        traveller={renderTraveller}
        className="custom-recharts-brush"
        fill="rgba(40,97,184,.12)"
        stroke="#005DAA"
        strokeOpacity={1}
        tickFormatter={(_, index) => {
          const item = data[index];
          return lodash.get(item, xDataKey) ?? "";
        }}
        travellerWidth={0}
      >
      </Brush>
    </AreaChart>
  );
}
export default BasicLine;
