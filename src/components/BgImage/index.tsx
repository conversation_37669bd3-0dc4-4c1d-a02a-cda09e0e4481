interface IProps
  extends React.DetailedHTMLProps<
    React.HTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  /**
   * 背景图片地址
   */
  url: string;
}
function BgImage(props: React.PropsWithChildren<IProps>) {
  const { url, children, style, ...rest } = props;
  return (
    <div
      style={{
        // backgroundSize: "cover",
        // backgroundImage: `url(${url})`,
        background: `url(${url}) no-repeat left bottom / cover`,
        ...style,
      }}
      {...rest}
    >
      {children}
    </div>
  );
}
export default BgImage;
