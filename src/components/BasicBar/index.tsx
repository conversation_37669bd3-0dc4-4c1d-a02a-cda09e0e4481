import type { LinearConfig } from "@/typings";
import { nanoid } from "nanoid";
import { useCallback, useRef, useState } from "react";
import type { Props as LengendProps } from "recharts/types/component/Legend";
import type { ContentType } from "recharts/types/component/Tooltip";
import type {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Legend,
  Tooltip,
  //   Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import SvgLinearGradient from "../Comp/SvgLinearGradient";
import { useMemoizedFn } from "ahooks";
import type { DataKey } from "recharts/types/util/types";
import { formatLinear } from "@/utils";

interface IProps {
  data: any[];

  width: number;

  height: number;

  columns: BarColumns;

  xDataKey: string;

  yAxisWidth?: number;

  // 单位标题
  title?: string;
}

interface ColumnType {
  color: string;

  area: LinearConfig;

  dataKey: string;

  name: string;
}
export type BarColumns = ColumnType[];

function BasicBar(props: IProps) {
  const {
    data,
    width,
    height,
    columns,
    xDataKey,
    yAxisWidth = 20,
    title,
  } = props;
  const customXTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;
    return (
      <g>
        <foreignObject
          style={{
            overflow: "visible",
          }}
          width="1"
          height="1"
          x={x}
          y={y + 5}
        >
          <div className="color-chart">
            <div
              style={{
                width: "fit-content",
                transform: `translate(-50%, 0px) rotate(0deg)`,
                textAlign: "center",
              }}
              className="truncate"
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const customYTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;

    return (
      <g>
        <foreignObject
          width="1"
          height="1"
          x={x}
          y={y}
          style={{
            overflow: "visible",
          }}
        >
          <div className="color-chart">
            <div
              style={{
                width: "max-content",
                transform: `translate(-100%, -50%)`,
                textAlign: "right",
              }}
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const filterIdPrefix = useRef(nanoid());

  // dataKey的隐藏列表
  const [hideDataKeyList, setHideDataKeyList] = useState<DataKey<any>[]>([]);

  // 轴单位
  const CustomizedLabel = useCallback(
    (props) => {
      return (
        <g>
          <foreignObject
            width="1"
            height="1"
            x={props.viewBox.x}
            y={props.viewBox.y}
            style={{
              overflow: "visible",
            }}
          >
            <div
              style={{
                width: "max-content",
                transform: "translate(-20px, -40px)",
              }}
              className="color-chart"
            >
              {title}
            </div>
          </foreignObject>
        </g>
      );
    },
    [title]
  );

  const onLegendToggle = useMemoizedFn((dataKey?: DataKey<any>) => {
    if (dataKey) {
      const isHide = hideDataKeyList.includes(dataKey);
      setHideDataKeyList((val) => {
        if (isHide) {
          return val.filter((item) => item !== dataKey);
        }
        return [...val, dataKey];
      });
    }
  });

  const legendContent = useMemoizedFn((e: LengendProps) => {
    const { payload = [] } = e;
    return (
      <div className="absolute right-[-20px] top-[4px] flex justify-end gap-x-4">
        {payload.map((val, index) => (
          <div
            className="flex items-center cursor-pointer"
            key={`legend${index}`}
            onClick={() => onLegendToggle(val.dataKey)}
          >
            <div
              className="w-[16px] h-[4px]"
              style={{
                backgroundColor: val.color,
              }}
            ></div>
            <span className="ml-2 text-[rgba(255,255,255,0.65)] text-[14px]">
              {val.value}
            </span>
          </div>
        ))}
      </div>
    );
  });

  const CustomTooltip: ContentType<ValueType, NameType> = ({
    active,
    payload,
  }) => {
    if (active && payload && payload.length) {
      return (
        <div
          className="w-fit justify-center color-text bg-[rgba(50,91,174,0.4)] rounded-[4px] px-5 py-2"
          style={{
            boxShadow: "inset 0px 0 20px 0px #5FC1FF",
            backdropFilter: "blur(12px)",
          }}
        >
          {payload.map((val) => (
            <div className="flex items-center gap-x-3" key={val.dataKey}>
              <span className="text-[16px] w-auto">{val?.payload?.name}</span>
              <span
                className="text-[22px]"
                style={{
                  fontFamily: "DINCond-Bold",
                  color: val.color,
                }}
              >
                {val.value}
              </span>
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  const CustomBarShape = useCallback(
    (props: any) => {
      const shapeProps = props.shapeProps;
      const { x, y, width, height, dataKey } = shapeProps;
      const itemConfig = columns.find((val) => val.dataKey === dataKey)!;
      const borderRadius = `${width / 2}px`;

      return (
        <foreignObject
          x={x}
          y={y}
          width={width}
          height={height}
          style={{
            overflow: "visible",
            position: "relative",
          }}
        >
          <div
            style={{
              width: "100%",
              height: "100%",
              position: "fixed",
              clipPath: "none",
              background: formatLinear(itemConfig.area),
              borderRadius: `${borderRadius} ${borderRadius} 0 0`,
            }}
          ></div>
        </foreignObject>
      );
    },
    [columns]
  );

  return (
    <BarChart
      width={width}
      height={height}
      data={data}
      className="text-[14px]"
      margin={{
        top: 40,
        right: 20,
        left: 20,
        bottom: 2,
      }}
    >
      <CartesianGrid vertical={false} stroke="#35434F" />
      <XAxis
        dataKey={xDataKey}
        tick={customXTick}
        tickLine={false}
        axisLine={{
          stroke: "#3C4550",
        }}
        interval="preserveStartEnd"
        minTickGap={0}
      />
      <YAxis
        tickLine={false}
        axisLine={false}
        tick={customYTick}
        width={yAxisWidth}
        label={<CustomizedLabel />}
      />
      {columns.map((val) => {
        const idPrefix = `${filterIdPrefix.current}-${val.dataKey}`;
        return (
          <defs key={idPrefix}>
            <SvgLinearGradient id={`${idPrefix}`} config={val.area} />
          </defs>
        );
      })}
      {columns.map((val) => (
        <Bar
          key={val.dataKey}
          dataKey={val.dataKey}
          barSize={10}
          shape={(props: any) => {
            return <CustomBarShape shapeProps={props} />;
          }}
          label={false}
        />
      ))}
      {/* <Legend
        wrapperStyle={{
          position: "absolute",
          left: 0,
          top: 0,
          height: 0,
        }}
        content={legendContent}
      /> */}
      <Tooltip
        content={<CustomTooltip />}
        cursor={false}
        wrapperStyle={{
          width: "max-content",
        }}
      />
    </BarChart>
  );
}
export default BasicBar;
