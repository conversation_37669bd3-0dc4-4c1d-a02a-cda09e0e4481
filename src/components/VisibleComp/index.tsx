import React from "react";

interface IProps {
  visible: boolean;
}

function VisibleComp(props: React.PropsWithChildren<IProps>) {
  const { children, visible } = props;

  // 确保 children 是一个 React 元素
  if (React.isValidElement<{ className?: string }>(children)) {
    return React.cloneElement(children, {
      className:
        `${children.props.className || ""} ${visible ? "block" : "hidden"}`.trim(),
    });
  }

  // 如果 children 不是 React 元素，直接返回
  return <>{children}</>;
}

export default VisibleComp;
