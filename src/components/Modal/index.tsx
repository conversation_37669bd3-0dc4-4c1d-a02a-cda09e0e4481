import type { ModalProps } from "antd";
import { ConfigProvider, Modal } from "antd";
import { useMemo } from "react";
import bg1 from "@/assets/popBg1.webp";
import bg2 from "@/assets/popBg2.webp";
import bg3 from "@/assets/popBg3.webp";
import { Scrollbars } from "react-custom-scrollbars";
import iconClose from "@/assets/popClose.png";
import BgImage from "../BgImage";

export interface CustomModalProps {
  open: boolean;

  onCancel: () => void;
}

interface IProps extends ModalProps {
  /**
   * 不同类型对应不同的大小背景及宽度
   */
  type?: 1 | 2 | 3;

  children?: React.ReactNode;
}
function ProModal(props: IProps & CustomModalProps) {
  const { type = 1, title, open, onCancel, children, ...rest } = props;

  const widthValue = useMemo(() => {
    switch (type) {
      case 1:
        return 1150;

      case 2:
        return 1414;

      case 3:
        return 1421;

      default:
        return 1150;
    }
  }, [type]);

  const heightValue = useMemo(() => {
    switch (type) {
      case 1:
        return 674;

      case 2:
        return 834;

      case 3:
        return 675;

      default:
        return 674;
    }
  }, [type]);

  const bgUrl = useMemo(() => {
    switch (type) {
      case 1:
        return bg1;

      case 2:
        return bg2;

      case 3:
        return bg3;

      default:
        return bg1;
    }
  }, [type]);

  return (
    <ConfigProvider
      theme={{
        components: {
          Modal: {
            contentBg: "rgba(0,0,0,0)",
            colorBgMask: "rgba(3,16,31,0.88)",
            boxShadow: "none",
          },
        },
      }}
    >
      <Modal
        width={widthValue}
        height={heightValue}
        centered
        footer={false}
        title={false}
        style={{
          padding: 0,
          backgroundImage: `url(${bgUrl})`,
          backgroundSize: "cover",
        }}
        open={open}
        onCancel={onCancel}
        styles={{
          content: {
            padding: 0,
          },
          body: {
            height: heightValue,
            padding: "0 10px",
            display: "flex",
            flexDirection: "column",
          },
        }}
        closable={false}
        {...rest}
      >
        <header className="pb-[20px] pt-[54px] flex justify-center relative">
          <div
            className="text-[32px]"
            style={{
              backgroundImage:
                "linear-gradient(180deg, #A6DDEF 0%, #F3FEFF 54%,#9EB7FF 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              fontFamily: "YouSheBiaoTiHei-2",
            }}
          >
            {title}
          </div>
          <BgImage
            className="size-[31px] absolute right-[-2px] top-[54px] cursor-pointer"
            url={iconClose}
            onClick={onCancel}
          />
        </header>
        <div className="flex-1 px-[24px] pb-[60px] overflow-hidden">
          <Scrollbars>{children}</Scrollbars>
        </div>
      </Modal>
    </ConfigProvider>
  );
}
export default ProModal;
