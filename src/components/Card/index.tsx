import React, { useMemo } from "react";
import subTitleBg1 from "@/assets/subTitle1.png";
import subTitleBg2 from "@/assets/subTitle2.png";
import subTitleBg3 from "@/assets/subTitle3.png";
import subTitleBg4 from "@/assets/subTitle4.png";

import { Spin, type TabsProps } from "antd";
import Title from "../Title";

interface IProps {
  /**
   * 卡片右上角的操作区域
   */
  extra?: React.ReactNode;

  /**
   * 标题
   */
  title: string | React.ReactNode;

  /**
   * 根据类型选择不同尺寸的背景图片
   */
  headerType: 1 | 2 | 3 | 4;

  /**
   * 切换的props
   */
  tabsProps?: TabsProps;

  /**
   * 查看更多
   */
  onMore?: () => void;

  /**
   * 加载中
   */
  loading?: boolean;

  bodyClass?: string;
}
function Card(props: React.PropsWithChildren<IProps>) {
  const {
    title,
    children,
    headerType,
    extra,
    tabsProps,
    onMore,
    loading = false,
    bodyClass,
  } = props;

  const bgUrl = useMemo(() => {
    const config = {
      1: subTitleBg1,
      2: subTitleBg2,
      3: subTitleBg3,
      4: subTitleBg4,
    };
    return config[headerType];
  }, [headerType]);

  const minWidthValue = useMemo(() => {
    const config = {
      1: 496,
      2: 840,
      3: 446,
      4: 890,
    };
    return config[headerType];
  }, [headerType]);

  return (
    <div
      className="w-full h-full rounded-[12px] relative z-10"
      style={{
        width: minWidthValue,
      }}
    >
      <div className="relative h-[44px] left-[-10px] flex justify-between">
        <div
          className="absolute top-[-1px] left-0 w-full h-[46px] bg-cover z-[-1]"
          style={{
            backgroundImage: `url(${bgUrl})`,
          }}
        ></div>
        <div
          className="text-[20px] pl-[58px] font-[Alibaba-PuHuiTi-Bold]"
          style={{
            color: "#d8e1f8",
            backgroundImage:
              "linear-gradient(180deg, #a6d7fb 0%, #effcfe 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            // textShadow: "0px 0px 10px red",
            filter: "drop-shadow(0 0 5px rgba(75,180,229,0.8))", // 模拟外发光
          }}
        >
          {title}
        </div>
        <div className="flex items-center mr-[20px] mt-[-18px] gap-x-4 pointer-events-none">
          {onMore && (
            <Title
              className="text-[16px] text-[#D8E1F8] relative top-[6px] pl-[50px] cursor-pointer font-[Alibaba-PuHuiTi-Bold] pointer-events-auto"
              onClick={onMore}
            >
              more
            </Title>
          )}
          {extra && <div className="pointer-events-auto">{extra}</div>}
          {tabsProps && (
            <div className="flex items-center mr-2 pointer-events-auto">
              {tabsProps.items?.map((val) => (
                <div
                  key={val.key}
                  onClick={() => tabsProps.onChange?.(val.key)}
                  className={`${tabsProps.activeKey === val.key ? "bg-[#165184] color-text" : "bg-[#27476A] color-secondary"} border border-[#3678BC] w-[54px] h-[26px] flex items-center justify-center cursor-pointer hover:bg-[#165184] color-text`}
                >
                  {val.label}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      <div
        className={`py-3 px-4 ${bodyClass} mt-[6px]`}
        style={{
          width: `calc(100% - 24px)`,
          background:
            "linear-gradient(180deg,rgba(0,42,72,0.5) 0%,rgba(0,42,72,0.4) 50%,rgba(0,42,72,0.1) 100%)",
        }}
      >
        <Spin spinning={loading}>{children}</Spin>
      </div>
    </div>
  );
}
export default Card;
