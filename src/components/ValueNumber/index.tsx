import type { JumpValueProps } from "@/hooks/useJumpValue";
import useJumpValue from "@/hooks/useJumpValue";
import { chunk as _chunk } from "lodash-es";
import React, { useMemo } from "react";

interface IProps extends JumpValueProps {
  splitCount?: number;
  splitStr?: string;
}

function ValueNumber(props: IProps) {
  const { splitCount = 3, splitStr = "," } = props;
  const value = useJumpValue(props);

  const { integerPart, decimalPart } = useMemo(() => {
    const [integer, decimal] = value.toString().split(".");
    return {
      integerPart: integer.split("").reverse(),
      decimalPart: decimal ? `.${decimal}` : "",
    };
  }, [value]);

  const valueList = useMemo(() => {
    return _chunk(integerPart, splitCount)
      .map((val) => val.reverse())
      .reverse();
  }, [splitCount, integerPart]);

  const valueTotal = valueList.length ?? 0;

  return (
    <React.Fragment>
      {valueList.map((val, index) => (
        <React.Fragment key={`value${index}`}>
          {val.join("")}
          {valueTotal - 1 !== index ? splitStr : ""}
        </React.Fragment>
      ))}
      {decimalPart}
    </React.Fragment>
  );
}

export default ValueNumber;
