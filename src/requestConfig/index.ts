import useStore from "@/store";
import type { RequestConfig, RequestOptions } from "umi";

declare global {
  interface Window {
    /**
     * 是否显示登录失效弹窗
     */
    invalidModal: boolean;
  }
}

const requestConfig: RequestConfig = {
  baseURL: process.env.baseURL,
  headers: {
    "Content-Type": "application/json",
  },
  errorConfig: {
    errorHandler(e: any) {
      const { messageApi } = useStore.getState();
      if (e?.code === "3002") {
        return;
      }
      messageApi?.warning(e?.msg ?? "系统繁忙，请稍后再试");
    },
  },
  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      console.log(
        `%c ${config.name}  ${config.url}-参数:`,
        "color:#FF0099;",
        JSON.stringify(config.params) || JSON.stringify(config.data)
      );
      const { token } = useStore.getState();
      return {
        ...config,
        headers: {
          Authorization: JSON.stringify({
            token,
          }),
        },
      };
    },
  ],
  transformRequest: [(data) => data],
  // 响应拦截器
  responseInterceptors: [
    (response) => {
      let resData = response.data as API.Response;
      try {
        resData = typeof resData === "string" ? JSON.parse(resData) : resData;
      } catch (error) {}

      const { code } = resData;
      console.log(
        `%c ${(response.config as any).name} 结果 `,
        "color:#FF0099;",
        resData
      );
      if (code === "3002") {
        const { modalApi } = useStore.getState();
        if (!window.invalidModal) {
          window.invalidModal = true;
          modalApi.warning({
            title: "登录信息失效",
            content: "请重新登录!",
            okText: "确定",
            keyboard: false,
            onOk() {
              window.invalidModal = false;
              window.location.href = process.env.loginUrl!;
            },
          });
        }
        return Promise.reject(resData);
      }
      if (code !== "0000" && code !== 200 && code) {
        return Promise.reject(resData);
      }
      return {
        data: resData,
      } as any;
    },
  ],
};
export default requestConfig;
