/**
 * table动画类型
 */
export enum TableAnimationTypeEnum {
  /**
   * 全部
   */
  All = "all",

  /**
   * 逐条
   */
  Single = "single",
}

/**
 * table衔接方式
 */
export enum TableAnimationConnectModeEnum {
  /**
   * 连续
   */
  Continuous = "continuous",

  /**
   * 从头开始
   */
  FromStart = "fromStart",
}

/**
 * table动画形式
 */
export enum TableAnimationModeEnum {
  /**
   * 从上而下
   */
  TopToBottom = "topToBottom",

  /**
   * 翻牌
   */
  Flip = "flip",
}

/**
 * 日期类型
 */
export enum DateTypeEnum {
  /**
   * 24H
   */
  Day = "day",

  /**
   * 7天
   */
  Week = "week",

  /**
   * 30天
   */
  Month = "month",

  /**
   * 自定义
   */
  Custom = "custom",
}

/**
 * 地图类型
 */
export enum MapTypeEnum {
  /**
   * 世界地图
   */
  World = "world",

  /**
   * 中国地图
   */
  China = "china",

  /**
   * 广东地图
   */
  Guangdong = "guangdong",
}

export enum ProjectionTypeEnum {
  /**
   * 墨卡托投影
   */
  Mercator = "mercator",

  /**
   * WGS84
   */
  WGS84 = "wgs84",
}

/**
 * 地图各模型对应的基础层级枚举 0为坐标点z的0 按照缩小100倍计算
 */
export enum MapLevelEnum {
  /**
   * 基础地图层级
   */
  BASIC_LEVEL = 0.001,

  /**
   * 文本基础层级
   */
  BASIC_LABEL_LEVEL = 0.004,

  /**
   * 顶线基础层级
   */
  BASIC_TOP_LINE_LEVEL = 0.002,

  /**
   * 底线基础层级
   */
  BASIC_BOTTOM_LINE_LEVEL = 0.004,
}

/**
 * 单值占比图方向
 */
export enum SingleRatioDirectionEnum {
  /**
   * 顺时针
   */
  Clockwise = "clockwise",
  /**
   * 逆时针
   */
  Counterclockwise = "counterclockwise",
}

export enum ColorTypeEnum {
  /**
   * 线性渐变
   */
  Linear = "linear",
  /**
   * 纯色
   */
  Pure = "pure",
}

export enum EventLevelEnum {
  /**
   * 高危
   */
  High = "1",
  /**
   * 中危
   */
  Middle = "2",
  /**
   * 低危
   */
  Low = "3",
}

export enum SearchTypeEnum {
  /**
   * 地市
   */
  City = 1,
  /**
   * 行业
   */
  Industry = 2,
}

export enum EventTypeEnum {
  /**
   * 全部
   */
  All = 0,
  /**
   * 待响应
   */
  Pending = 1,
  /**
   * 已响应
   */
  Responded = 2,
  /**
   * 待处置
   */
  PendingDeal = 3,
  /**
   * 已处置
   */
  Dealt = 4,
}

/**
 * 处理结果
 */
export enum DealResultEnum {
  /**
   * 已处置
   */
  Deal = "deal",
  /**
   * 已预警
   */
  Warn = "warn",
}

/**
 * 漏洞等级
 */
export enum VulLevelEnum {
  /**
   * 低危
   */
  Low = "1",
  /**
   * 中危
   */
  Middle = "2",
  /**
   * 高危
   */
  High = "3",
  /**
   * 超危
   */
  Super = "4",
  /**
   * 高危5
   */
  High5 = "5",
  /**
   * 中危6
   */
  Middle6 = "6",
  /**
   * 低危7
   */
  Low7 = "7",
  /**
   * 超危8
   */
  Super8 = "8",
}

/**
 * 通知状态
 */
export enum NotifyStatusEnum {
  /**
   * 待响应
   */
  Pending = 1,
  /**
   * 已响应
   */
  Responded = 2,
  /**
   * 待处置
   */
  PendingDeal = 3,
  /**
   * 已处置
   */
  Dealt = 4,
}

/**
 * 地图类型
 */
export enum TaskMapTypeEnum {
  /**
   * 澳门
   */
  Macao = 0,
  /**
   * 香港
   */
  HongKong = 1,
  /**
   * 广州
   */
  Guangzhou = 2,
  /**
   * 珠海
   */
  Zhuhai = 3,
  /**
   * 任务地图 
   */
  TaskMap = 4,
}
