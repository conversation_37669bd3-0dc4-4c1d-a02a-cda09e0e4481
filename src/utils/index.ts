import type {
  GeoMercator<PERSON>arams,
  LinearConfig,
  JsonParseOptions,
} from "@/typings";
import { kebabCase } from "lodash-es";
import { Ray, Vector3 } from "three";
import tinycolor from "tinycolor2";

export const formatLinear = (config: LinearConfig) => {
  const list =
    [...config.colorStops]?.sort((a, b) => {
      return a.offset - b.offset;
    }) ?? [];

  const str = list.map((val) => `${val.color} ${val.offset * 100}%`);

  return `linear-gradient(${config.angle}deg,${str.join(",")})`;
};

// 对feature做转换
export const transFromGeoJSON = (data: GeoMercatorParams["data"]) => {
  const worldData = data;
  const features = worldData?.features;
  for (let i = 0; i < features.length; i++) {
    const element = features[i];
    if (element.geometry.type === "Polygon") {
      element.geometry.coordinates = [element.geometry.coordinates] as any;
      element.geometry.type = "MultiPolygon";
    }
  }
  return worldData;
};

export function parseJSON<T = any>(
  jsonString?: string,
  options: JsonParseOptions<T> = {}
) {
  const { defaultValue = {}, reviver } = options;

  if (jsonString === undefined) {
    return undefined;
  }

  try {
    const parsedObj = JSON.parse(jsonString, reviver);
    if (typeof parsedObj !== "object" || parsedObj === null) {
      return defaultValue as T;
    }
    return parsedObj as T;
  } catch (err) {
    return defaultValue as T;
  }
}

// 获取hex颜色和透明度
export const transformColor = (color?: string) => {
  const copyColor = tinycolor(color ?? "#000");
  return {
    value: copyColor.toHexString(),
    opacity: copyColor.getAlpha(),
  };
};

// 转换省份名称方法
export const transformProvinceName = (name?: string) => {
  switch (name) {
    case "新疆":
      return "新疆维吾尔自治区";
    case "香港":
      return "香港特别行政区";
    case "澳门":
      return "澳门特别行政区";
    case "西藏":
      return "西藏自治区";
    default:
      return name;
  }
};

/**
 * 将经度和纬度转换为3D坐标。
 *
 * @param lng - 经度，单位为度
 * @param lat - 纬度，单位为度
 * @param radius - 球体的半径（例如，地球的半径）
 * @returns 一个包含x, y和z坐标的对象
 */
export const lngLatToXYZ = (
  lng: number,
  lat: number,
  radius: number
): { x: number; y: number; z: number } => {
  const phi = (180 + lng) * (Math.PI / 180);
  const theta = (90 - lat) * (Math.PI / 180);

  return {
    x: -radius * Math.sin(theta) * Math.cos(phi),
    y: radius * Math.cos(theta),
    z: radius * Math.sin(theta) * Math.sin(phi),
  };
};

/**
 * 把对象css样式转换成string
 * @param valueInfo
 * @returns {String}
 */
export const mapToCssProp = (valueInfo: React.CSSProperties, unit = " ") => {
  const cssProperties = Object.entries(valueInfo).map(([key, value]) => {
    return `${kebabCase(key)}: ${value}!important`;
  });
  return `${cssProperties.join(unit)};`;
};

/**
 * @description 计算v1,v2 的中点
 * @param v1
 * @param v2
 */
export const getVCenter = (v1: Vector3, v2: Vector3) => {
  const v = v1.add(v2);
  return v.divideScalar(2);
};

/**
 * @description 计算V1，V2向量固定长度的点
 * @param v1
 * @param v2
 * @param len
 */
export const getLenVcetor = (v1: Vector3, v2: Vector3, len: number) => {
  const v1v2Len = v1.distanceTo(v2);
  return v1.lerp(v2, len / v1v2Len);
};

/**
 * @description 获取贝塞尔曲线中心点
 * @param v0
 * @param v3
 */
export const getCubicBezierCenterPoint = (v0: Vector3, v3: Vector3) => {
  // 夹角
  const angle = (v0.angleTo(v3) * 1.8) / Math.PI / 0.1; // 0 ~ Math.PI
  const aLen = angle * 15; // 夹角的倍数设置高度
  const hLen = angle * angle * 800;
  const p0 = new Vector3(0, 0, 0);
  // 法线向量
  const rayLine = new Ray(p0, getVCenter(v0.clone(), v3.clone()));
  const temp = new Vector3();
  // 顶点坐标
  const vtop = new Vector3();
  rayLine.at(1, temp);

  rayLine.at(hLen / temp.distanceTo(p0), vtop);
  // 控制点坐标
  const v1 = getLenVcetor(v0.clone(), vtop, aLen);
  const v2 = getLenVcetor(v3.clone(), vtop, aLen);

  return { v1, v2 };
};
