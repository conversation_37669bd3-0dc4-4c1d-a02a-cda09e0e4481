import type { ColorTypeEnum } from "@/enum";
import type { FeatureCollection, Geometry } from "./geojson";

/**
 * 渐变配置
 */
export interface LinearConfig {
  /**
   * 角度
   */
  angle: number;

  /**
   * 开始结束位置配置
   */
  colorStops: {
    offset: number;
    color: string;
  }[];

  /**
   * 透明度
   */
  opacity?: number;
}

export interface GeoMercatorParams {
  /**
   * geojson
   */
  data: FeatureCollection<Geometry>;

  /**
   * 多边形质心
   */
  center: number[];

  /**
   * 缩放大小 中国:960w = 1
   */
  scale: number;
}

export interface JsonParseOptions<T> {
  defaultValue?: T;
  reviver?: (key: any, value: any) => any;
}

export interface CoordinateItem {
  /**
   * 起点坐标x
   */
  x0: number;

  /**
   * 起点坐标y
   */
  y0: number;

  /**
   * 终点坐标x
   */
  x1: number;

  /**
   * 终点坐标y
   */
  y1: number;
}

/**
 * 渐变或纯色颜色配置
 */
export interface ColorConfig {
  /**
   * 颜色类型
   */
  type: ColorTypeEnum;
  /**
   * 纯色颜色
   */
  pure: string;
  /**
   * 渐变配置
   */
  linear: LinearConfig;
}

/**
 * 通用文本配置
 */
export interface TextCommonConfig {
  /**
   * 字体
   */
  fontFamily: string;

  /**
   * 字体大小
   */
  fontSize: number;

  /**
   * 颜色
   */
  color: ColorConfig;

  /**
   * 行高
   */
  lineHeight?: number;

  /**
   * 文字间隔
   */
  letterSpacing: number;

  /**
   * 是否斜体
   */
  italic: boolean; // 斜体

  /**
   * 是否加粗
   */
  bold: boolean;
}
