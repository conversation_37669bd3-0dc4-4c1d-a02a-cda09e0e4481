import Statistics from "./components/Statistics";
import ReinsuranceCompanyList from "./components/ReinsuranceCompanyList";
import DutyOnDuty from "./components/DutyOnDuty";
import AttackCount from "./components/AttackCount";
import SafetyList from "./components/SafetyList";
import Container from "./container";
import ThreatenIp from "./components/ThreatenIp";
import LoopholeInfo from "./components/LoopholeInfo";
import EventInfo from "./components/EventInfo";
import TabList from "./components/TabList";

function Home3() {
  return (
    <Container.Provider>
      <div className="relative z-10">
        <Statistics />
        {/* 重保单位列表 */}
        <ReinsuranceCompanyList />
        {/* 值班值守 */}
        <DutyOnDuty />
        {/* 威胁IP */}
        <ThreatenIp />
        {/* 漏洞信息 */}
        <LoopholeInfo />
        {/* 事件信息 */}
        <EventInfo />
        {/* 攻击次数态势 */}
        <AttackCount />
        {/* 报平安列表 */}
        <SafetyList />
        {/* 切换市 */}
        <TabList />
      </div>
    </Container.Provider>
  );
}
export default Home3;
