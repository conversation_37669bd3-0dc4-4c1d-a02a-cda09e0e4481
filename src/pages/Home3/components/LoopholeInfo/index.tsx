import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import Container from "../../container";
import { useMemo } from "react";
import { dataValue } from "./data";

function LoopholeInfo() {
  const { startTime, endTime } = useTime();

  const columns: TableProps["columns"] = [
    {
      title: "告警名称",
      dataIndex: "eventName",
      key: "eventName",
    },
    {
      title: "被攻击IP",
      dataIndex: "victimIp",
      key: "victimIp",
    },
    {
      title: "告警时间",
      dataIndex: "time",
      key: "time",
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { taskId, isDemo } = Container.useContainer();

  const { loading } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.vulInfoList({
          taskId,
          startTime,
          endTime,
          page: 1,
          size: 20,
        });
      }
    },
    {
      refreshDeps: [taskId, startTime, endTime],
    }
  );

  const list = useMemo(() => {
    if (isDemo) {
      return loading ? [] : dataValue;
    }
    return [];
  }, [isDemo, loading]);

  return (
    <div className="absolute top-[436px] right-[36px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="告警信息" onMore={setTrue} loading={loading}>
        <List width={440} height={238} data={list} size={5} columns={columns} />
      </Card>
    </div>
  );
}
export default LoopholeInfo;
