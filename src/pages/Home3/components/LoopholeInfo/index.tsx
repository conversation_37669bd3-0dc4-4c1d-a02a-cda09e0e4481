import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import { Pagination } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import Container from "../../container";
import { useMemo, useState } from "react";
import { dataValue } from "./data";

function LoopholeInfo() {
  const { startTime, endTime } = useTime();

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const columns: TableProps["columns"] = [
    {
      title: "告警名称",
      dataIndex: "eventName",
      key: "eventName",
    },
    {
      title: "被攻击IP",
      dataIndex: "victimIp",
      key: "victimIp",
    },
    {
      title: "告警时间",
      dataIndex: "time",
      key: "time",
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { taskId, isDemo } = Container.useContainer();

  const { loading, data } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.vulInfoList({
          taskId,
          startTime,
          endTime,
          page: current,
          size: pageSize,
        });
      }
    },
    {
      refreshDeps: [taskId, startTime, endTime, current, pageSize],
    }
  );

  const list = useMemo(() => {
    if (isDemo) {
      return loading ? [] : dataValue;
    }
    return data?.vulInfoListDtoList ?? [];
  }, [data?.vulInfoListDtoList, isDemo, loading]);

  const total = useMemo(() => {
    return data?.total ?? 0;
  }, [data?.total]);

  return (
    <div className="absolute top-[436px] right-[36px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="告警信息" onMore={setTrue} loading={loading}>
        <List width={440} height={238} data={list} size={pageSize} columns={columns} />
        {total > pageSize && (
          <div className="flex justify-end items-center">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, size) => {
                setCurrent(page);
                setPageSize(size);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}
export default LoopholeInfo;
