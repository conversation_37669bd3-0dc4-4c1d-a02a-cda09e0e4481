import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import Container from "@/pages/Home3/container";
// import guaranteeService from "@/service/guaranteeService";
// import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { dataValue } from "../../data";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "attackNum",
      title: "攻击次数",
    },
    {
      dataIndex: "eventName",
      title: "事件名称",
    },
    {
      dataIndex: "time",
      title: "告警时间",
    },
    {
      dataIndex: "attackIp",
      title: "攻击IP",
    },
    {
      dataIndex: "country",
      title: "攻击者国家",
    },
    {
      dataIndex: "victimIp",
      title: "被攻击者IP",
    },
    {
      dataIndex: "level",
      title: "事件等级",
    },
  ];

  // const { startTime, endTime } = useTime();

  const { taskId, isDemo } = Container.useContainer();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    if (isDemo) {
      return {
        data: dataValue.slice(
          (params.page - 1) * params.size,
          params.page * params.size
        ),
        total: dataValue.length,
      };
    }
    if (taskId) {
      // const res = await guaranteeService.vulInfoList({
      //   taskId,
      //   ...params,
      //   startTime,
      //   endTime,
      // });
      return {
        data: [],
        total: 0,
      };
    }
    return {
      data: [],
      total: 0,
    };
  };

  return (
    <ProModal type={2} title="告警信息列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
