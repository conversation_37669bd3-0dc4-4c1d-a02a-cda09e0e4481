import onDutyBg from "@/assets/3/onDutyBg.png";
import BgImage from "@/components/BgImage";

interface IProps {
  index: number;

  rowGap: number;

  itemHeight: number;

  item: {
    list: API.Guarantee.ZbSynergyUnitListDto[];
  };

  // 是否动画
  showAnimate?: boolean;
}
function ListItem(props: IProps) {
  const { index, rowGap, itemHeight, item } = props;

  const { list } = item;

  return (
    <div
      className="grid grid-cols-2 gap-x-2 w-full"
      style={{
        marginBottom: rowGap,
      }}
    >
      {list.map((val, itemIndex) => (
        <BgImage
          key={`item${index}-${itemIndex}`}
          url={onDutyBg}
          style={{
            height: itemHeight,
          }}
        >
          <div
            title={val.unitName}
            className="text-[#E6EDF8] text-[16px] pl-5 relative top-[3px] truncate"
          >
            {val.unitName}
          </div>
          <div className="pl-5 w-max leading-6 pt-[14px]">
            <div className="flex flex-nowrap items-center text-[14px]">
              <span className="text-[rgba(156,180,210,.89)] min-w-[76px]">
                姓名：
              </span>
              <span className="text-[#AEBBE3] truncate">
                {val.nameOfDutyOfficer}
              </span>
            </div>
            <div className="flex flex-nowrap items-center text-[14px]">
              <span className="text-[rgba(156,180,210,.89)] min-w-[76px]">
                职务：
              </span>
              <span className="text-[#AEBBE3] truncate">
                {val.jobTitle ?? "-"}
              </span>
            </div>
            <div className="flex flex-nowrap items-center text-[14px]">
              <span className="text-[rgba(156,180,210,.89)] min-w-[76px]">
                手机号码：
              </span>
              <span className="text-[#AEBBE3] truncate">{val.phoneNumber}</span>
            </div>
          </div>
        </BgImage>
      ))}
    </div>
  );
}
export default ListItem;
