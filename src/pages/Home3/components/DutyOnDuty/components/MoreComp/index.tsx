import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import Container from "@/pages/Home3/container";
import guaranteeService from "@/service/guaranteeService";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { dataValue } from "../../data";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      width: 80,
      align: "center",
    },
    {
      dataIndex: "unitName",
      title: "单位名称",
      align: "left",
    },
    {
      dataIndex: "nameOfDutyOfficer",
      title: "值班人员姓名",
    },
    {
      dataIndex: "jobTitle",
      title: "职务",
    },
    {
      dataIndex: "phoneNumber",
      title: "手机号码",
    },
  ];

  const { taskId, isDemo } = Container.useContainer();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    if (isDemo) {
      return {
        data: dataValue.slice(
          (params.page - 1) * params.size,
          params.page * params.size
        ),
        total: dataValue.length,
      };
    }
    if (taskId) {
      const res = await guaranteeService.zbSynergyUnitList({
        taskId,
        ...params,
      });
      return {
        data: res.unitList ?? [],
        total: res.total,
      };
    }

    return {
      data: [],
      total: 0,
    };
  };

  return (
    <ProModal title="值班值守" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
