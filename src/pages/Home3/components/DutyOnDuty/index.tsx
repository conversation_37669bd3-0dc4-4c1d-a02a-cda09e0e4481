import Card from "@/components/Card";
import useVisible from "@/hooks/useVisible";
import List from "./components/List";
import { useMemo } from "react";
import MoreComp from "./components/MoreComp";
import Container from "../../container";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import { dataValue } from "./data";

function DutyOnDuty() {
  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { taskId, ready, isDemo } = Container.useContainer();

  const { data, loading } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.zbSynergyUnitList({
          taskId,
          page: 1,
          size: 20,
        });
      }
    },
    {
      ready,
      refreshDeps: [taskId],
    }
  );

  const list = useMemo(() => {
    let value: API.Guarantee.ZbSynergyUnitListDto[] = [];
    if (loading) {
      return [];
    }
    if (isDemo) {
      value = dataValue;
    } else {
      value = data?.unitList ?? [];
    }
    const list = value.reduce(
      (acc, cur) => {
        const last = acc[acc.length - 1];
        if (last && last.list.length < 2) {
          last.list.push(cur);
        } else {
          acc.push({
            list: [cur],
          });
        }
        return acc;
      },
      [] as { list: API.Guarantee.ZbSynergyUnitListDto[] }[]
    );
    return list;
  }, [data?.unitList, isDemo, loading]);

  return (
    <div className="absolute left-[48px] top-[744px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="值班值守" onMore={setTrue} loading={loading}>
        <List data={list} />
      </Card>
    </div>
  );
}
export default DutyOnDuty;
