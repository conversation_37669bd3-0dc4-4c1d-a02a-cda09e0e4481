import icon from "@/assets/3/icon1.png";
import bg from "@/assets/3/unitItemBg.png";
import BgImage from "@/components/BgImage";

interface IProps {
  index: number;

  rowGap: number;

  itemHeight: number;

  item: API.Guarantee.ZbUnitListDto;

  // 是否动画
  showAnimate?: boolean;
}
function ListItem(props: IProps) {
  const { index, rowGap, itemHeight, item } = props;

  const { unitName, assetsCount, eventCount } = item;

  return (
    <div
      key={`item${index}`}
      className="flex items-center pl-4 pr-5"
      style={{
        marginBottom: rowGap,
        height: itemHeight,
      }}
    >
      <img src={icon} className="size-[66px] min-w-[66px]" />
      <div className="flex-1">
        <BgImage url={bg} className="h-[26px] flex items-center">
          <div
            className="text-[18px] pl-[28px]"
            style={{
              backgroundImage:
                "linear-gradient(0deg, #ACDDFF 0%, #FFFFFF 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            {unitName}
          </div>
        </BgImage>
        <div className="text-[#CEDCE6] text-[16px] px-[28px] mt-2 flex items-center">
          <span>资产数：</span>
          <span className="text-[#10F7FC] text-[18px] min-w-[80px]">
            {assetsCount ?? 0}
          </span>
          <span>事件数：</span>
          <span className="text-[#10F7FC] text-[18px]">{eventCount ?? 0}</span>
        </div>
      </div>
    </div>
  );
}
export default ListItem;
