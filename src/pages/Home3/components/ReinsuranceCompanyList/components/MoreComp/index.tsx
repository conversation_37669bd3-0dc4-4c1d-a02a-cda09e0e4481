import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import Container from "@/pages/Home3/container";
import guaranteeService from "@/service/guaranteeService";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      width: 100,
    },
    {
      dataIndex: "unitName",
      title: "企业",
    },
    {
      dataIndex: "assetsCount",
      title: "关联资产数",
    },
    {
      dataIndex: "eventCount",
      title: "关联事件数",
    },
  ];

  const { taskId } = Container.useContainer();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    if (taskId) {
      const res = await guaranteeService.zbUnitList({
        taskId,
        ...params,
      });
      return {
        data: res.zbUnitListDtoList ?? [],
        total: res.total,
      };
    }

    return {
      data: [],
      total: 0,
    };
  };

  return (
    <ProModal title="重保单位列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
