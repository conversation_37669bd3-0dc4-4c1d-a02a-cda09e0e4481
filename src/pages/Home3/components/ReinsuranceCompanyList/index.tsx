import Card from "@/components/Card";
import useVisible from "@/hooks/useVisible";
import List from "./components/List";
import { useMemo } from "react";
import MoreComp from "./components/MoreComp";
import Container from "../../container";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";

function ReinsuranceCoordination() {
  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { taskId, ready } = Container.useContainer();

  const { data, loading } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.zbUnitList({
          taskId,
          page: 1,
          size: 20,
        });
      }
    },
    {
      ready,
      refreshDeps: [taskId],
    }
  );

  const list = useMemo(() => data?.zbUnitListDtoList ?? [], [data?.zbUnitListDtoList]);

  return (
    <div className="absolute left-[46px] top-[350px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card
        headerType={1}
        title="重保单位列表"
        onMore={setTrue}
        loading={loading}
      >
        <div className="mt-[14px] ml-[-12px]">
          <List data={list} />
        </div>
      </Card>
    </div>
  );
}
export default ReinsuranceCoordination;
