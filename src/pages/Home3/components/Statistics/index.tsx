import { Spin } from "antd";
import icon1 from "@/assets/3/1.png";
import icon2 from "@/assets/3/2.png";
import icon3 from "@/assets/3/3.png";
import ValueNumber from "@/components/ValueNumber";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import { useMemo } from "react";
import Container from "../../container";

function Statistics() {
  const { taskId, ready } = Container.useContainer();

  const { data: overviewData } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.zbOverview(taskId);
      }
      return undefined;
    },
    {
      ready,
      refreshDeps: [taskId],
    }
  );

  const columns = useMemo(() => {
    return [
      {
        label: "重报单位数",
        value: overviewData?.zbUnitCount,
        icon: icon1,
      },
      {
        label: "资产数",
        value: overviewData?.zbAssetsCount,
        icon: icon2,
      },
      {
        label: "事件数",
        value: overviewData?.eventUnitCount,
        icon: icon3,
      },
    ];
  }, [
    overviewData?.eventUnitCount,
    overviewData?.zbAssetsCount,
    overviewData?.zbUnitCount,
  ]);

  return (
    <div className="absolute left-[46px] top-[136px]">
      <div
        className="mt-[6px] h-[180px]"
        style={{
          background:
            "linear-gradient(180deg,rgba(0,42,72,0.5) 0%,rgba(0,42,72,0.4) 50%,rgba(0,42,72,0.1) 100%)",
        }}
      >
        <Spin spinning={false}>
          <div className="grid grid-cols-3 mt-[-4px] w-[475px]">
            {columns.map((val, index) => (
              <div key={index} className="flex flex-col items-center">
                <img className="w-[96px] object-cover" src={val.icon} alt="" />
                <div className="flex flex-col items-center pt-1">
                  <div className="color-secondary text-[18px]">{val.label}</div>
                  <div className="text-[#10F7FC] text-[32px] leading-[32px]">
                    <ValueNumber dataValue={val.value ?? 0}></ValueNumber>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Spin>
      </div>
    </div>
  );
}
export default Statistics;
