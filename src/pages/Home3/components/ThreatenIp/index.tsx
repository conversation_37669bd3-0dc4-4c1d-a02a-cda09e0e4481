import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import Container from "../../container";
import { useMemo } from "react";
import { dataValue } from "./data";

function ThreatenIp() {
  const { startTime, endTime } = useTime();

  const columns: TableProps["columns"] = [
    {
      title: "威胁IP",
      dataIndex: "threatIp",
      key: "threatIp",
    },
    {
      title: "威胁等级",
      dataIndex: "threatLevel",
      key: "threatLevel",
    },
    {
      title: "发现时间",
      dataIndex: "findTime",
      key: "findTime",
      width: 170,
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { taskId, ready, isDemo } = Container.useContainer();

  const { loading, data } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.threatInfoList({
          startTime,
          endTime,
          page: 1,
          size: 20,
          taskId,
        });
      }
    },
    {
      ready,
      refreshDeps: [taskId, startTime, endTime],
    }
  );

  const list = useMemo(() => {
    if (isDemo) {
      return loading ? [] : dataValue;
    }
    return data?.threatIpListDtoList ?? [];
  }, [data?.threatIpListDtoList, isDemo, loading]);

  return (
    <div className="absolute top-[130px] right-[36px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="威胁IP" onMore={setTrue} loading={loading}>
        <List width={440} height={238} data={list} size={5} columns={columns} />
      </Card>
    </div>
  );
}
export default ThreatenIp;
