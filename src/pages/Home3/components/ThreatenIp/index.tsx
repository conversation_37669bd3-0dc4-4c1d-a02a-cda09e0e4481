import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import { Pagination } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import Container from "../../container";
import { useMemo, useState } from "react";
import { dataValue } from "./data";

function ThreatenIp() {
  const { startTime, endTime } = useTime();

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const columns: TableProps["columns"] = [
    {
      title: "威胁IP",
      dataIndex: "threatIp",
      key: "threatIp",
    },
    {
      title: "威胁等级",
      dataIndex: "threatLevel",
      key: "threatLevel",
    },
    {
      title: "发现时间",
      dataIndex: "findTime",
      key: "findTime",
      width: 170,
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { taskId, ready, isDemo } = Container.useContainer();

  const { loading, data } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.threatInfoList({
          startTime,
          endTime,
          page: current,
          size: pageSize,
          taskId,
        });
      }
    },
    {
      ready,
      refreshDeps: [taskId, startTime, endTime, current, pageSize],
    }
  );

  const list = useMemo(() => {
    if (isDemo) {
      return loading ? [] : dataValue;
    }
    return data?.threatIpListDtoList ?? [];
  }, [data?.threatIpListDtoList, isDemo, loading]);

  const total = useMemo(() => {
    return data?.total ?? 0;
  }, [data?.total]);

  return (
    <div className="absolute top-[130px] right-[36px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="威胁IP" onMore={setTrue} loading={loading}>
        <List width={440} height={238} data={list} size={pageSize} columns={columns} />
        {total > pageSize && (
          <div className="flex justify-end items-center">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, size) => {
                setCurrent(page);
                setPageSize(size);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}
export default ThreatenIp;
