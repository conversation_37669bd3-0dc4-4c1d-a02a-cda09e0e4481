import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import Container from "@/pages/Home3/container";
import guaranteeService from "@/service/guaranteeService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { dataValue } from "../../data";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns<API.Synergy.ThreatIpListDto>[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "threatIp",
      title: "单位名称",
    },
    {
      title: "攻击次数",
      dataIndex: "attackNum",
    },
    {
      dataIndex: "threatLevel",
      title: "威胁等级",
    },
    {
      dataIndex: "threatType",
      title: "主要威胁类型",
    },
    {
      dataIndex: "findTime",
      title: "最早发现时间",
    },
    // {
    //   dataIndex: "dealResult",
    //   title: "处置结果",
    //   width: 140,
    //   render: (_, record) => {
    //     const item = dictInfo.dealResult.find(
    //       (val) => val.value === record.dealResult
    //     );
    //     return item?.label;
    //   },
    // },
  ];

  const { startTime, endTime } = useTime();

  const { taskId, isDemo } = Container.useContainer();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    if (isDemo) {
      return {
        data: dataValue.slice(
          (params.page - 1) * params.size,
          params.page * params.size
        ),
        total: dataValue.length,
      };
    }
    if (taskId) {
      const res = await guaranteeService.threatInfoList({
        ...params,
        startTime,
        endTime,
        taskId,
      });
      return {
        data: res.threatIpListDtoList ?? [],
        total: res.total,
      };
    }
    return {
      data: [],
      total: 0,
    };
  };

  return (
    <ProModal type={3} title="威胁IP列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
