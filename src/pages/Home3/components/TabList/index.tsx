import bg from "@/assets/3/tabBg.webp";
import BgImage from "@/components/BgImage";
import Container from "../../container";
import { useMemo } from "react";
import React from "react";
import mapBg1 from "@/assets/3/mapBg1.webp";
import mapBg2 from "@/assets/3/mapBg2.webp";
import mapBg3 from "@/assets/3/mapBg3.webp";
import mapBg4 from "@/assets/3/mapBg4.webp";
import VisibleComp from "@/components/VisibleComp";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import { useTime } from "@/store/useTime";
import { TaskMapTypeEnum } from "@/enum";
import TaskMap from "./components/TaskMap";
import { Select } from "antd";

function TabList() {
  const { taskId, setTaskId } = Container.useContainer();

  const { startTime, endTime } = useTime();

  const { data } = useRequest(() => guaranteeService.guaranteeTaskList(), {
    onSuccess: (res) => {
      const total = res.zbTaskInfoDtoList?.length ?? 0;
      if (total > 0) {
        setTaskId("7");
      }
      // setTaskId(res.zbTaskInfoDtoList?.[0]?.taskId);
    },
    refreshDeps: [startTime, endTime],
  });

  const list = useMemo(
    () =>
      data?.zbTaskInfoDtoList?.map((val) => ({
        ...val,
        districtCodeList: val.districtCodeList?.map((code) => {
          if (["853", "820000"].includes(code)) {
            return 820000;
          } else if (["852", "810000"].includes(code)) {
            return 810000;
          }
          return Number(code);
        }),
      })) ?? [],
    [data?.zbTaskInfoDtoList]
  );

  const selectItem = useMemo(
    () => list.find((val) => val.taskId === taskId),
    [list, taskId]
  );

  // const name = useMemo(() => selectItem?.taskName, [selectItem]);

  const mapType = useMemo(() => {
    const districtCodeList = selectItem?.districtCodeList ?? [];
    if (districtCodeList.length === 1) {
      const [code] = districtCodeList;
      switch (code) {
        case 820000:
          return TaskMapTypeEnum.Macao;
        case 810000:
          return TaskMapTypeEnum.HongKong;
        case 440100:
          return TaskMapTypeEnum.Guangzhou;
        case 440400:
          return TaskMapTypeEnum.Zhuhai;
        default:
          break;
      }
    }
    return TaskMapTypeEnum.TaskMap;
  }, [selectItem?.districtCodeList]);

  const showTaskMap = useMemo(() => {
    return (
      mapType === TaskMapTypeEnum.TaskMap &&
      selectItem?.taskId &&
      selectItem?.districtCodeList?.length > 0
    );
  }, [mapType, selectItem?.districtCodeList?.length, selectItem?.taskId]);

  // const onPrev = () => {
  //   const currentIndex = list.findIndex((val) => val.taskId === taskId);
  //   const prevIndex = (currentIndex - 1 + list.length) % list.length;
  //   setTaskId(list[prevIndex].taskId);
  // };

  // const onNext = () => {
  //   const currentIndex = list.findIndex((val) => val.taskId === taskId);
  //   const nextIndex = (currentIndex + 1) % list.length;
  //   setTaskId(list[nextIndex].taskId);
  // };

  return (
    <React.Fragment>
      {/* <BgImage
        className="w-[536px] h-[61px] x-centered mt-1 absolute top-[140px] x-centered flex justify-center items-center z-[1000]"
        url={bg}
      > */}
      <Select
        fieldNames={{
          label: "taskName",
          value: "taskId",
        }}
        value={taskId}
        className="w-[536px] h-[61px] x-centered mt-1 absolute top-[140px] x-centered flex justify-center items-center z-[1000]"
        options={list}
        style={{
          background: `url(${bg}) no-repeat left top / cover`,
        }}
        onChange={(value) => setTaskId(value)}
        labelRender={(value) => (
          <span
            className="font-bold text-[24px] tracking-[3px] flex justify-center w-full"
            style={{
              backgroundImage:
                "linear-gradient(0deg, #EEDA9A 0%, #DCC170 100%)",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
            }}
          >
            {value.label}
          </span>
        )}
        suffixIcon={false}
        // suffixIcon={
        //   <div className="border-transparent border-t-[#137AD0] border-[8px] mr-9 mt-[8px]"></div>
        // }
        variant="borderless"
      ></Select>
      {/* <div
          className="size-[20px] cursor-pointer absolute y-centered left-[48px]"
          onClick={onPrev}
        ></div>
        <div
          className="size-[20px] cursor-pointer absolute y-centered right-[48px]"
          onClick={onNext}
        ></div>
        <span
          className="font-bold text-[24px] tracking-[3px]"
          style={{
            backgroundImage: "linear-gradient(0deg, #EEDA9A 0%, #DCC170 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          {name}
        </span> */}
      {/* </BgImage> */}

      <VisibleComp visible={mapType === TaskMapTypeEnum.Macao}>
        <BgImage
          url={mapBg1}
          className="w-[1920px] h-[1080px] absolute top-0 left-0 pointer-events-none"
        />
      </VisibleComp>
      <VisibleComp visible={mapType === TaskMapTypeEnum.Guangzhou}>
        <BgImage
          url={mapBg4}
          className="w-[1920px] h-[1080px] absolute top-0 left-0 pointer-events-none"
        />
      </VisibleComp>
      <VisibleComp visible={mapType === TaskMapTypeEnum.Zhuhai}>
        <BgImage
          url={mapBg2}
          className="w-[1920px] h-[1080px] absolute top-0 left-0 pointer-events-none"
        />
      </VisibleComp>
      <VisibleComp visible={mapType === TaskMapTypeEnum.HongKong}>
        <BgImage
          url={mapBg3}
          className="w-[1920px] h-[1080px] absolute top-0 left-0 pointer-events-none"
        />
      </VisibleComp>
      {showTaskMap && (
        <TaskMap key={taskId} codeList={selectItem?.districtCodeList ?? []} />
      )}
    </React.Fragment>
  );
}
export default TabList;
