import React, { memo, useMemo } from "react";
import { LineGeometry, LineMaterial, Line2 } from "three-stdlib";
import Container from "../../../../container";

interface IProps {
  points: number[];
}
function RenderLine(props: IProps) {
  const { points } = props;
  const { topLinePosition } = Container.useContainer();

  // 顶线
  // 创建线条的几何和材料
  const topLineMesh = useMemo(() => {
    const geometry = new LineGeometry();
    geometry.setPositions(points);
    const material = new LineMaterial({
      color: "#49F8FF" as any,
      linewidth: 2,
      dashed: false,
      transparent: true,
      opacity: 1,
    });
    material.resolution.set(window.innerWidth, window.innerHeight);
    const line = new Line2(geometry, material);
    return line;
  }, [points]);

  return (
    <React.Fragment>
      {/* 顶线 */}
      <group name="topLine" position={topLinePosition}>
        <primitive object={topLineMesh} />
      </group>
    </React.Fragment>
  );
}
export default memo(RenderLine);
