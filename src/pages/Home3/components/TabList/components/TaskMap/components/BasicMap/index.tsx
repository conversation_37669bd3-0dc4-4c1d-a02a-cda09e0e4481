import { memo } from "react";
import RenderShape from "./components/RenderShape";
import React from "react";
import Container from "../../container";
// import useEvent from "../../hooks/useEvent";
import RenderName from "./components/RenderName";
import RenderLine from "./components/RenderLine";

function BasicMap() {
  const { featureData, mapPosition, codeList } = Container.useContainer();

  // const { onPointerEnter, onPointerLeave } = useEvent();

  return (
    <React.Fragment>
      <group position={mapPosition} renderOrder={1}>
        {featureData.map((val, index) => {
          const adcode = val.properties?.adcode;
          const name = val.properties?.name;
          return (
            <group
              key={adcode ?? index}
              // onPointerEnter={(event) => {
              //   onPointerEnter(event);
              // }}
              // onPointerLeave={(event) => {
              //   onPointerLeave(event);
              // }}
              userData={{
                adcode,
                name,
                index,
                cityPosition: val.cityPosition,
              }}
            >
              {/* 遍历每个特征的多边形坐标，并为每个多边形渲染线条 */}
              {val.shapeList.map((item) => {
                const showTopLine = codeList.includes(adcode);
                return (
                  <React.Fragment key={item.key}>
                    {/* 面 */}
                    <RenderShape
                      shape={item.shape}
                      adcode={adcode}
                      name={name}
                    />
                    {showTopLine && <RenderLine points={item.points} />}
                  </React.Fragment>
                );
              })}

              <RenderName
                index={index}
                x={val.cityPosition.x}
                y={val.cityPosition.y}
                name={name}
                adcode={adcode}
              />
            </group>
          );
        })}
      </group>
    </React.Fragment>
  );
}
export default memo(BasicMap);
