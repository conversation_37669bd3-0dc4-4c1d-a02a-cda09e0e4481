import { createContainer } from "unstated-next";
import mapJson from "@/assets/json/440000.json";
import macaoJson from "@/assets/json/820000.json";
import hongKongJson from "@/assets/json/810000.json";
import { ProjectionTypeEnum } from "@/enum";
import useMapData from "./hooks/useMapData";
import useShape from "./hooks/useShape";
import mapBgUrl from "@/assets/3/taskMapTexture.webp";
import * as THREE from "three";
import { useMemo, useRef } from "react";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import situationService from "@/service/situationService";

export interface MapConfig {
  /**
   * 层级-地图的拉长长度
   */
  level?: number;

  /**
   * 墨卡托投影转换
   */
  geoProjection: (args: [number, number]) => [number, number];

  /**
   * html缩放比例
   */
  htmlScale: number;

  /**
   * 获取省份中心点
   */
  getProvinceCenter: (adcode: number, name?: string) => number[] | undefined;
}

interface IProps {
  codeList: number[];
}
function useContainer(props: IProps) {
  const { codeList } = props;

  const projectionType = ProjectionTypeEnum.Mercator;

  const depth = 6;

  const subGroupRef = useRef<THREE.Group>(null);

  const infoPanelRefs = useRef<HTMLDivElement[]>([]);

  const mapJsonValue = useMemo(() => {
    if (codeList.length === 0) {
      return mapJson;
    }
    const value = { ...mapJson };
    if (codeList.includes(820000)) {
      value.features.unshift(macaoJson.features[0] as any);
    }
    if (codeList.includes(810000)) {
      value.features.unshift(hongKongJson.features[0] as any);
    }
    return value;
  }, [codeList]);

  const {
    featureData,
    mapPosition,
    handleCalcUv2,
    geoProjection,
    getProvinceCenter,
  } = useMapData({
    projectionType,
    dataJson: JSON.stringify(mapJsonValue),
    depth,
  });

  const mapBgMargin = useMemo(
    () => ({
      left: -0,
      right: -0.0,
      top: -0.144,
      bottom: -0.09,
    }),
    []
  );

  const { meshMaterial, sideMaterial } = useShape({
    mapBgUrl,
    mapBgMargin,
  });

  const { endTime, startTime } = useTime();

  const { data } = useRequest(
    () => situationService.attackGuangDongMap({ startTime, endTime }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const guangDongMapData = useMemo(() => {
    const map = new Map<number, API.Situation.AttackGuangDongMapDto>();
    data?.attackGuangDongMapDtoList?.forEach((val) => {
      map.set(val.dstCode, val);
    });
    return map;
  }, [data?.attackGuangDongMapDtoList]);

  // 给主组件用的 material  材质
  const heatMaterial = useMemo(() => {
    // 处理区域热力颜色 用adcode 或者 name取值
    const material = new THREE.MeshBasicMaterial({
      color: "#9fd5ff",
      opacity: 0.5,
      transparent: true,
    });
    return material;
  }, []);

  // 顶线position
  const topLinePosition = useMemo(() => {
    const level = 0.002 + depth;
    return new THREE.Vector3(0, 0, level);
  }, []);

  return {
    featureData,
    mapPosition,
    meshMaterial,
    handleCalcUv2,
    sideMaterial,
    depth,
    subGroupRef,
    geoProjection,
    getProvinceCenter,
    infoPanelRefs,
    guangDongMapData,
    heatMaterial,
    codeList,
    topLinePosition,
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
