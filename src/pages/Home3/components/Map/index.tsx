import * as THREE from "three";
import { Canvas } from "@react-three/fiber";
import ScaleCanvas from "@/components/Map/ScaleCanvas";
import {
  OrbitControls,
  PerspectiveCamera,
  useProgress,
} from "@react-three/drei";
import BasicMap from "./components/BasicMap";
import SubComponent from "./components/SubComponent";
import Container from "./container";
import { useMemo } from "react";
import React from "react";
import { Spin } from "antd";

function Map() {
  const width = 1920;
  const height = 1080;

  const progress = useProgress();

  const spinning = useMemo(() => progress.progress < 100, [progress.progress]);

  return (
    <React.Fragment>
      <Canvas
        gl={{
          //  设备像素比 不同硬件设备的屏幕window.devicePixelRatio的值可能不同 设置是为了适应不同的硬件设备屏幕 min是为了防止设备像素比过大导致渲染器内存溢出
          pixelRatio: Math.min(window.devicePixelRatio, 2),
          // 色调映射
          toneMapping: THREE.NoToneMapping,
        }}
        className="absolute x-centered ml-[-30px] top-[-90px]"
        style={{
          pointerEvents: "auto",
          userSelect: "none",
          opacity: spinning ? 0 : 1,
        }}
      >
        <ScaleCanvas width={width} height={height} />
        <PerspectiveCamera
          near={1}
          far={1000}
          makeDefault
          up={[0, 1, 0]}
          fov={60}
          position={[0, 300, 0]}
        />
        <group rotation={[-Math.PI / 1.5, 0, 0]}>
          {/* 地图 */}
          <BasicMap />
          <SubComponent />
        </group>

        <ambientLight color={"#FFFFFF"} intensity={3} />
        <OrbitControls
          makeDefault
          enableDamping={false}
          enablePan={false}
          enableRotate={false}
          enableZoom={false}
          zoomSpeed={0.2}
          maxZoom={0.1}
        />
        {/* <Stats /> */}
      </Canvas>
      <Spin spinning={spinning} className="x-centered z-[100] top-[400px]" />
    </React.Fragment>
  );
}
export default () => {
  return (
    <Container.Provider>
      <Map />
    </Container.Provider>
  );
};
