import { Html } from "@react-three/drei";
import { useMemo } from "react";
import * as THREE from "three";
import Container from "../../../../container";
import bg from "@/assets/3/bg5.png";
import BgImage from "@/components/BgImage";

export interface IProps {
  x: number;

  y: number;

  index: number;
}
function InfoPanel(props: IProps) {
  const { x, y, index } = props;

  const { depth, infoPanelRefs } = Container.useContainer();

  const position = useMemo(() => {
    return new THREE.Vector3(x, -y, 0.008 + depth);
  }, [x, y, depth]);

  return (
    <Html
      position={position}
      transform
      name="111"
      sprite
      scale={[7, 7, 7]}
      userData={{}}
      pointerEvents="none"
      ref={(ref) => {
        infoPanelRefs.current[index] = ref!;
      }}
    >
      <BgImage className="w-[328px] h-[150px] absolute left-0 top-0" url={bg}></BgImage>
    </Html>
  );
}
export default InfoPanel;
