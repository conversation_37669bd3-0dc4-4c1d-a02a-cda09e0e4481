import { memo } from "react";
import RenderShape from "./components/RenderShape";
import React from "react";
import Container from "../../container";
import useEvent from "../../hooks/useEvent";

function BasicMap() {
  const { featureData, mapPosition } = Container.useContainer();

  const { onPointerEnter, onPointerLeave } = useEvent();

  return (
    <React.Fragment>
      <group position={mapPosition} renderOrder={1}>
        {featureData.map((val, index) => {
          const adcode = val.properties?.adcode;
          const name = val.properties?.name;
          return (
            <group
              key={adcode ?? index}
              onPointerEnter={(event) => {
                onPointerEnter(event);
              }}
              onPointerLeave={(event) => {
                onPointerLeave(event);
              }}
              userData={{
                adcode,
                name,
                index,
                cityPosition: val.cityPosition,
              }}
            >
              {/* 遍历每个特征的多边形坐标，并为每个多边形渲染线条 */}
              {val.shapeList.map((item) => {
                return (
                  <React.Fragment key={item.key}>
                    {/* 面 */}
                    <RenderShape
                      shape={item.shape}
                      adcode={adcode}
                      name={name}
                    />
                  </React.Fragment>
                );
              })}
            </group>
          );
        })}
      </group>
    </React.Fragment>
  );
}
export default memo(BasicMap);
