import { useTexture } from "@react-three/drei";
import mapBg from "@/assets/3/mapBg.webp";

export default () => {
  const texture = useTexture(mapBg);

  const width = 490;

  return (
    <group renderOrder={-2}>
      <mesh position={[9, -20, 2]} rotation={[0, -0.04, 0]}>
        <planeGeometry args={[width, width / 1.77777778]} attach="geometry" />
        <meshBasicMaterial transparent map={texture} opacity={0.4} />
      </mesh>
    </group>
  );
};
