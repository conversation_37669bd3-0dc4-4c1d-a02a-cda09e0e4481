import { createContainer } from "unstated-next";
import mapJson from "@/assets/json/440100.json";
import { ProjectionTypeEnum } from "@/enum";
import useMapData from "./hooks/useMapData";
import useShape from "./hooks/useShape";
import mapBgUrl from "@/assets/3/mapTexture.webp";
import type * as THREE from "three";
import { useRef } from "react";

export interface MapConfig {
  /**
   * 层级-地图的拉长长度
   */
  level?: number;

  /**
   * 墨卡托投影转换
   */
  geoProjection: (args: [number, number]) => [number, number];

  /**
   * html缩放比例
   */
  htmlScale: number;

  /**
   * 获取省份中心点
   */
  getProvinceCenter: (adcode: number, name?: string) => number[] | undefined;
}

function useContainer() {
  const projectionType = ProjectionTypeEnum.Mercator;

  const depth = 6;

  const subGroupRef = useRef<THREE.Group>(null);

  const infoPanelRefs = useRef<HTMLDivElement[]>([]);

  const {
    featureData,
    mapPosition,
    handleCalcUv2,
    geoProjection,
    getProvinceCenter,
  } = useMapData({
    projectionType,
    dataJson: JSON.stringify(mapJson),
    depth,
  });

  const mapBgMargin = {
    left: -0.101,
    right: -0.18,
    top: 0,
    bottom: 0,
  };
  const { meshMaterial, sideMaterial } = useShape({
    mapBgUrl,
    mapBgMargin,
  });

  return {
    featureData,
    mapPosition,
    meshMaterial,
    handleCalcUv2,
    sideMaterial,
    depth,
    subGroupRef,
    geoProjection,
    getProvinceCenter,
    infoPanelRefs,
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
