import List from "./components/List";
import type { TableProps } from "antd";
import Container from "../../container";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import { useMemo, useState } from "react";

function SafetyList() {
  const { taskId, ready } = Container.useContainer();

  const [activeKey, setActiveKey] = useState("1");

  const { data } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.zbIndustryCityIsOk({
          taskId,
          dataType: activeKey,
        });
      }
    },
    {
      ready,
      refreshDeps: [taskId, activeKey],
    }
  );

  const list = useMemo(() => data?.isOkDtoList ?? [], [data?.isOkDtoList]);

  const columns: TableProps["columns"] = [
    {
      title: "序号",
      key: "dataIndex",
      dataIndex: "dataIndex",
      render: (_, __, index) => index + 1,
      width: 48,
      align: "center",
    },
    {
      title: activeKey === "1" ? "行业名称" : "地市名称",
      dataIndex: "cityOrIndustryName",
      key: "cityOrIndustryName",
      width: 80,
      align: "center",
    },
    {
      title: "报平安次数",
      dataIndex: "isOkCount",
      key: "isOkCount",
      align: "center",
    },
  ];

  const tabsProps = {
    items: [
      {
        key: "1",
        label: "行业",
      },
      {
        key: "2",
        label: "地市",
      },
    ],
    activeKey,
    onChange: setActiveKey,
  };

  return (
    <div className="absolute left-[1150px] top-[538px] z-[1000]">
      <div className="flex items-center mb-2">
        {tabsProps.items?.map((val) => (
          <div
            key={val.key}
            onClick={() => tabsProps.onChange?.(val.key)}
            className={`${tabsProps.activeKey === val.key ? "bg-[#165184] color-text" : "bg-[#27476A] color-secondary"} border border-[#3678BC] w-[54px] h-[26px] flex items-center justify-center cursor-pointer hover:bg-[#165184] color-text`}
          >
            {val.label}
          </div>
        ))}
      </div>
      <List width={224} height={160} data={list} size={3} columns={columns} />
    </div>
  );
}
export default SafetyList;
