import { memo, useMemo } from "react";
import Container from "../../container";

function ListHeader() {
  const { columns, headerHeight } = Container.useContainer();

  const headerStyleValue: React.CSSProperties = useMemo(() => {
    return {
      height: headerHeight,
      backgroundColor: "rgba(22,46,71,.85)",
    };
  }, [headerHeight]);

  return (
    <div
      className="flex px-[2px] items-center w-full rounded-[2px]"
      style={headerStyleValue}
    >
      {columns?.map((val, index) => (
        <div
          key={val.key ?? index}
          className={`truncate color-secondary px-2 text-[15px] ${val.className}`}
          style={{
            flex: val.width ? `${val.width}px` : 1,
            maxWidth: val.width,
            textAlign: val.align,
          }}
        >
          {val.title?.toString()}
        </div>
      ))}
    </div>
  );
}
export default memo(ListHeader);
