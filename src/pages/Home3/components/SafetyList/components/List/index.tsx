import Container from "./container";
import ListHeader from "./components/ListHeader";
import ContentList from "./components/ContentList";
import type { IProps } from "./container";

function List(props: Omit<IProps, "headerHeight">) {
  const { width, height } = props;

  const headerHeight = 36;

  return (
    <div
      className="overflow-hidden text-[16px]"
      style={{
        width,
        height,
      }}
    >
      <Container.Provider
        initialState={{
          ...props,
          headerHeight,
        }}
      >
        {/* 表头 */}
        <ListHeader />
        {/* 内容 */}
        <ContentList />
      </Container.Provider>
    </div>
  );
}
export default List;
