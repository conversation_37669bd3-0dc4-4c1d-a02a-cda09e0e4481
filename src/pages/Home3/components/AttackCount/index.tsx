import Card from "@/components/Card";
import useVisible from "@/hooks/useVisible";
import type { Coloumns } from "@/components/BasicLine";
import BasicLine from "@/components/BasicLine";
import { useMemo } from "react";
import ProModal from "@/components/Modal";
import { useTime } from "@/store/useTime";
import Container from "../../container";
import guaranteeService from "@/service/guaranteeService";
import { useRequest } from "ahooks";

function AttackCount() {
  const [open, { setFalse, setTrue }] = useVisible(false);

  const { taskId, ready } = Container.useContainer();

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.zbSecurityPosture({
          taskId,
          startTime,
          endTime,
        });
      }
    },
    {
      ready,
      refreshDeps: [taskId, startTime, endTime],
    }
  );

  const list = useMemo(
    () => data?.zbSecurityPostureDtoList ?? [],
    [data?.zbSecurityPostureDtoList]
  );

  const columns = useMemo<Coloumns>(() => {
    return [
      {
        dataKey: "attackCount",
        name: "攻击次数",
        color: "rgba(43, 128, 255, 1)",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(43, 128, 255, .4)",
            },
            {
              offset: 1,
              color: "rgba(43, 128, 255, 0)",
            },
          ],
        },
      },
    ];
  }, []);

  return (
    <div className="absolute left-[545px] top-[744px]">
      <ProModal type={3} title="攻击次数态势" open={open} onCancel={setFalse}>
        <div className="p-4 mt-[30px]">
          <BasicLine
            data={list}
            xDataKey="dateTime"
            width={1290}
            height={440}
            coloumns={columns}
          />
        </div>
      </ProModal>
      <Card
        headerType={2}
        title="攻击次数态势"
        onMore={setTrue}
        loading={loading}
      >
        <BasicLine
          xDataKey="dateTime"
          data={list}
          width={800}
          height={240}
          coloumns={columns}
        />
      </Card>
    </div>
  );
}
export default AttackCount;
