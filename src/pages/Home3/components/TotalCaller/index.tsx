import Card from "@/components/Card";
import icon from "@/assets/3/5.png";
import BgImage from "@/components/BgImage";
import bg from "@/assets/3/bg2.png";
import { useRequest } from "ahooks";
import Container from "../../container";
import guaranteeService from "@/service/guaranteeService";
import { useMemo } from "react";

function TotalCaller() {
  const { taskId, ready } = Container.useContainer();

  const { data } = useRequest(
    async () => (taskId ? guaranteeService.convener(taskId) : undefined),
    {
      refreshDeps: [taskId],
      ready,
    }
  );

  const columns = useMemo(() => {
    return [
      {
        label: "工作单位",
        value: data?.corporation,
      },
      {
        label: "固定电话",
        value: data?.tel,
      },
      {
        label: "移动电话",
        value: data?.phone,
      },
    ];
  }, [data?.corporation, data?.phone, data?.tel]);

  return (
    <div className="absolute right-[48px] top-[744px]">
      <Card headerType={1} title="总召集人">
        <div className="flex ml-[-16px] h-full items-center mt-[10px]">
          <img src={icon} alt="" className="w-[170px]" />
          <div>
            <BgImage
              url={bg}
              className="w-[260px] h-[48px] mb-2 flex items-center px-6"
            >
              {data?.convener}
            </BgImage>
            {columns.map((val, index) => (
              <div
                key={index}
                className="w-[280px] rounded-[2px] mt-1 h-[48px] flex items-center px-6 bg-[rgba(43,55,74,0.39)] text-secondary"
              >
                {val.label}：{val.value}
              </div>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
}
export default TotalCaller;
