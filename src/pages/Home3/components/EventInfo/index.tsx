import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import Container from "../../container";
import { useMemo } from "react";
import { dataValue } from "./data";

function EventInfo() {
  const { startTime, endTime } = useTime();

  const columns: TableProps["columns"] = [
    {
      title: "事件类型",
      dataIndex: "eventType",
      key: "eventType",
    },
    {
      title: "单位名称",
      dataIndex: "uniName",
      key: "uniName",
    },
    {
      title: "发现时间",
      dataIndex: "foundTime",
      key: "foundTime",
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { taskId, ready, isDemo } = Container.useContainer();

  const { loading } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.eventInfoList({
          taskId,
          startTime,
          endTime,
          page: 1,
          size: 20,
        });
      }
    },
    {
      ready,
      refreshDeps: [taskId, startTime, endTime],
    }
  );

  const list = useMemo(() => {
    if (isDemo) {
      return loading ? [] : dataValue;
    }
    return [];
  }, [isDemo, loading]);

  return (
    <div className="absolute top-[742px] right-[36px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="事件信息" onMore={setTrue} loading={loading}>
        <List width={440} height={238} data={list} size={5} columns={columns} />
      </Card>
    </div>
  );
}
export default EventInfo;
