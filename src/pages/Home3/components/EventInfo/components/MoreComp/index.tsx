import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import Container from "@/pages/Home3/container";
import guaranteeService from "@/service/guaranteeService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { dataValue } from "../../data";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "eventType",
      title: "事件类型",
    },
    {
      dataIndex: "eventDetail",
      title: "事件详情",
      width: 300,
    },
    {
      dataIndex: "foundTime",
      title: "发现时间",
    },
    {
      dataIndex: "foundIp",
      title: "发现IP",
    },
    {
      dataIndex: "uniName",
      title: "单位名称",
    },
    {
      dataIndex: "status",
      title: "研判状态",
    },
  ];

  const { startTime, endTime } = useTime();

  const { taskId, isDemo } = Container.useContainer();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    if (isDemo) {
      return {
        data: dataValue,
        total: dataValue.length,
      };
    }
    return {
      data: [],
      total: 0,
    };
    // if (taskId) {
    //   const res = await guaranteeService.eventInfoList({
    //     taskId,
    //     ...params,
    //     startTime,
    //     endTime,
    //   });
    //   return {
    //     data: res.eventUnitList ?? [],
    //     total: res.total,
    //   };
    // }
    // return {
    //   data: [],
    //   total: 0,
    // };
  };

  return (
    <ProModal type={3} title="事件信息列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
