import Card from "@/components/Card";
import List from "@/components/List";
import useVisible from "@/hooks/useVisible";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import { useMemo, useState } from "react";
import Container from "../../container";
import { useRequest } from "ahooks";
import guaranteeService from "@/service/guaranteeService";
import { useTime } from "@/store/useTime";

function AttackSourceDistribution() {
  const [activeKey, setActiveKey] = useState("1");

  const { taskId, ready } = Container.useContainer();

  const { startTime, endTime } = useTime();

  const { data,loading } = useRequest(
    async () => {
      if (taskId) {
        return guaranteeService.attackSourceListTop5({
          taskId,
          page: 1,
          size: 20,
          dataType: activeKey,
          startTime,
          endTime,
        });
      }
    },
    {
      ready,
      refreshDeps: [taskId, startTime, endTime, activeKey],
    }
  );

  const list = useMemo(
    () => data?.attackSourceList ?? [],
    [data?.attackSourceList]
  );

  const columns: TableProps["columns"] = [
    {
      title: "序号",
      key: "dataIndex",
      dataIndex: "dataIndex",
      render: (_, __, index) => index + 1,
      width: 60,
      align: "center",
      className: "mr-4",
    },
    {
      title: "国家/地区",
      dataIndex: "region",
      key: "region",
      width: 140,
    },
    {
      title: "事件数",
      dataIndex: "eventCount",
      key: "eventCount",
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  return (
    <div className="absolute right-[44px] top-[440px]">
      <MoreComp
        open={open}
        onCancel={setFalse}
        key={visibleKey}
        dataType={activeKey}
      />
      <Card
        headerType={1}
        title="攻击源Top5"
        onMore={setTrue}
        loading={loading}
        tabsProps={{
          items: [
            {
              key: "1",
              label: "境内",
            },
            {
              key: "2",
              label: "境外",
            },
          ],
          activeKey,
          onChange: setActiveKey,
        }}
      >
        <div className="mt-1 ml-[-12px]">
          <List
            width={452}
            height={238}
            data={list}
            size={5}
            columns={columns}
          />
        </div>
      </Card>
    </div>
  );
}
export default AttackSourceDistribution;
