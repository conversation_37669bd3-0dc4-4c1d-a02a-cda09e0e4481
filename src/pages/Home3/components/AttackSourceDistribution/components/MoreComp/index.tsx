import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import Container from "@/pages/Home3/container";
import guaranteeService from "@/service/guaranteeService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

interface IProps extends CustomModalProps {
  dataType: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, dataType } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "region",
      title: "国家/地区",
    },
    {
      dataIndex: "eventCount",
      title: "事件数",
    },
  ];

  const { taskId } = Container.useContainer();

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    if (taskId) {
      const res = await guaranteeService.attackSourceListTop5({
        taskId,
        ...params,
        dataType,
        startTime,
        endTime,
      });
      return {
        data: res.attackSourceList,
        total: res.total,
      };
    }
    return {
      data: [],
      total: 0,
    };
  };

  return (
    <ProModal type={1} title="应急指挥实时事件" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
