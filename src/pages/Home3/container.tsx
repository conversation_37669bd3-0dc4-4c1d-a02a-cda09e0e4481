import { createContainer } from "unstated-next";
import { useMemo, useState } from "react";

function useContainer() {
  const [taskId, setTaskId] = useState<string>();

  const ready = useMemo(() => !!taskId, [taskId]);

  // 是演示
  const isDemo = useMemo(() => {
    return taskId === "7";
  }, [taskId]);

  return {
    taskId,
    setTaskId,
    ready,
    isDemo,
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
