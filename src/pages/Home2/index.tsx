import Statistics from "./components/Statistics";
import AssetsDistribution from "./components/AssetsDistribution";
import AssetTopRanking from "./components/AssetTopRanking";
import UnitTypeDistribution from "./components/UnitTypeDistribution";
import Container from "./container";
import AssetDetailModal from "./components/AssetDetailModal";
import BusinessType from "./components/BusinessType";
import DeviceType from "./components/DeviceType";

function Home2() {
  return (
    <Container.Provider>
      {/* 资产总数 */}
      <Statistics />
      {/* 设备类型 */}
      <DeviceType />
      {/* 业务系统 */}
      <BusinessType />
      {/* 单位类型分布 */}
      <UnitTypeDistribution />
      {/* 地市资产排行Top10 */}
      <AssetTopRanking />
      {/* 行业资产分布 */}
      <AssetsDistribution />
      {/* 资产详单弹框 */}
      <AssetDetailModal />
    </Container.Provider>
  );
}
export default Home2;
