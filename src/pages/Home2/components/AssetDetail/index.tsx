import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import resourcesService from "@/service/resourcesService";
import type { ProColumns } from "@ant-design/pro-table";
import { useRequest } from "ahooks";
import type { GetProp } from "antd";
import { useState } from "react";

interface IProps extends CustomModalProps {
  defaultValue?: Partial<API.Resources.BaseAssetsListParams>;
}
function AssetDetail(props: IProps) {
  const { open, onCancel, defaultValue } = props;

  const { data: typeData } = useRequest(() => resourcesService.type());

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
    },
    {
      dataIndex: "organizationName",
      title: "单位名称",
      width: 220,
    },
    {
      dataIndex: "organizationType",
      title: "单位类型",
    },
    {
      dataIndex: "industryName",
      title: "行业名称",
    },
    {
      dataIndex: "area",
      title: "所属地区",
    },
    {
      dataIndex: "sysName",
      title: "设施名称",
    },
    {
      dataIndex: "ipAddress",
      title: "IP地址",
    },
    {
      dataIndex: "assetsTypeName",
      title: "资产类型",
    },
    {
      dataIndex: "domain",
      title: "域名",
    },
    {
      dataIndex: "sysType",
      title: "业务类型",
    },
  ];

  const searchColumns: ProColumns[] = [
    {
      dataIndex: "industryName",
      title: "行业名称",
      valueType: "select",
      fieldProps: {
        placeholder: "请选择行业名称",
        options: typeData?.industryTypeList,
      },
    },
    {
      dataIndex: "homeCity",
      title: "所属地市",
      valueType: "select",
      fieldProps: {
        placeholder: "请选择所属地市",
        options: typeData?.cityList,
      },
    },
    {
      dataIndex: "unitType",
      title: "单位类型",
      valueType: "select",
      fieldProps: {
        placeholder: "请选择单位类型",
        options: typeData?.unitTypeList,
      },
      valueEnum: {},
    },
    {
      dataIndex: "businessType",
      title: "业务类型",
      valueType: "select",
      fieldProps: {
        placeholder: "请选择业务类型",
        options: typeData?.businessTypeList,
      },
      valueEnum: {},
    },
    {
      dataIndex: "assetsType",
      title: "资产类型",
      valueType: "select",
      fieldProps: {
        placeholder: "请选择资产类型",
        options:
          typeData?.equipmentTypeList?.map((val) => ({
            label: val.assetsTypeName,
            value: val.assetsType,
          })) ?? [],
      },
      valueEnum: {},
    },
    {
      dataIndex: "unitName",
      title: "单位名称",
      fieldProps: {
        placeholder: "请输入单位名称",
      },
    },
  ];

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.Resources.BaseAssetsListParams
  ) => {
    const res = await resourcesService.baseAssetsList(params);
    return {
      data: res.baseAssetsList,
      total: res.totalCount,
    };
  };

  const [collapsed, setCollapsed] = useState(false);

  return (
    <ProModal type={2} title="资产详单" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        searchColumns={searchColumns}
        form={{
          labelWidth: 90,
          size: "large",
          initialValues: defaultValue,
        }}
        search={{
          collapsed,
          onCollapse: (e) => {
            setCollapsed(e);
          },
        }}
        request={loadList}
        scroll={{
          y: collapsed ? 460 : 340,
        }}
      />
    </ProModal>
  );
}

export default AssetDetail;
