import icon1 from "@/assets/2/icon1.png";
import icon2 from "@/assets/2/icon2.png";
import icon3 from "@/assets/2/icon3.png";
import icon4 from "@/assets/2/icon4.png";
import icon5 from "@/assets/2/icon5.png";
import icon6 from "@/assets/2/icon6.png";
import icon7 from "@/assets/2/icon7.png";
import icon8 from "@/assets/2/icon8.png";
import icon9 from "@/assets/2/icon9.png";
import ValueNumber from "@/components/ValueNumber";
import { useRequest } from "ahooks";
import { useTime } from "@/store/useTime";
import { useMemo } from "react";
import Container from "../../container";
import resourcesService from "@/service/resourcesService";
import Card from "@/components/Card";

function Statistics() {
  const { startTime, endTime } = useTime();
  const { onShowList } = Container.useContainer();

  const { data, loading } = useRequest(
    () =>
      resourcesService.assetsInfoCount({
        startTime,
        endTime,
        assetsType: 0, // 固定为全量资产
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const columns = useMemo(() => {
    return [
      {
        label: "单位数",
        value: data?.unitCount,
        icon: icon1,
      },
      {
        label: "IP资产",
        value: data?.ipAssetsCount,
        icon: icon2,
      },
      {
        label: "域名资产",
        value: data?.domainAssetsCount,
        icon: icon3,
      },
      {
        label: "党政机关网站",
        value: data?.agencyWebsiteCount,
        icon: icon4,
      },
      {
        label: "政务系统",
        value: data?.governmentAffairsSystemCount,
        icon: icon5,
      },
      {
        label: "关基",
        value: data?.baseCount,
        icon: icon6,
      },
      {
        label: "公共服务平台",
        value: data?.publicServicePlatformCount,
        icon: icon7,
      },
      {
        label: "新闻网站",
        value: data?.newsWebsiteCount,
        icon: icon8,
      },
      {
        label: "其他",
        value: data?.otherCount,
        icon: icon9,
      },
    ];
  }, [
    data?.domainAssetsCount,
    data?.ipAssetsCount,
    data?.unitCount,
    data?.agencyWebsiteCount,
    data?.baseCount,
    data?.governmentAffairsSystemCount,
    data?.newsWebsiteCount,
    data?.otherCount,
    data?.publicServicePlatformCount,
  ]);

  return (
    <div className="absolute left-[48px] top-[142px]">
      <Card
        headerType={1}
        title="资产概况"
        onMore={() => onShowList({})}
        loading={loading}
      >
        <div className="grid grid-cols-3 w-full mb-[12px] items-center">
          {columns.slice(0, 3).map((val, index) => (
            <div key={index} className="flex flex-col items-center">
              <img className="w-[72px] object-cover" src={val.icon} alt="" />
              <div className="text-[rgba(255,255,255,0.6)] text-[16px] mt-[-6px]">
                {val.label}
              </div>
              <div className="text-white text-[22px] mt-1">
                <ValueNumber dataValue={val.value ?? 0}></ValueNumber>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}
export default Statistics;
