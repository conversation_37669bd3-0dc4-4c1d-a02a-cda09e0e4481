import { Select } from "antd";
import icon1 from "@/assets/2/icon1.png";
import icon2 from "@/assets/2/icon2.png";
import icon3 from "@/assets/2/icon3.png";
import icon4 from "@/assets/2/icon4.png";
import icon5 from "@/assets/2/icon5.png";
import icon6 from "@/assets/2/icon6.png";
import icon7 from "@/assets/2/icon7.png";
import icon8 from "@/assets/2/icon8.png";
import icon9 from "@/assets/2/icon9.png";
import cardBg1 from "@/assets/2/cardBg1.webp";
import cardBg2 from "@/assets/2/cardBg2.webp";
import ValueNumber from "@/components/ValueNumber";
import bg1 from "@/assets/3/bg1.png";
import { useRequest } from "ahooks";
import { useTime } from "@/store/useTime";
import { useMemo, useState } from "react";
import BgImage from "@/components/BgImage";
// import AssetsType from "../AssetsType";
import BusinessType from "../BusinessType";
import Container from "../../container";
import resourcesService from "@/service/resourcesService";

function Statistics() {
  const { startTime, endTime } = useTime();

  const [assetsType, setAssetsType] = useState(0);

  const { onShowList } = Container.useContainer();

  const { data } = useRequest(
    () =>
      resourcesService.assetsInfoCount({
        startTime,
        endTime,
        assetsType,
      }),
    {
      refreshDeps: [startTime, endTime, assetsType],
    }
  );

  const options = useMemo(() => {
    return [
      {
        label: "全量资产",
        value: 0,
      },
      {
        label: "省重点防护对象资产",
        value: 1,
      },
      {
        label: "其他省级资产",
        value: 2,
      },
      {
        label: "地市资产",
        value: 3,
      },
    ];
  }, []);

  const columns = useMemo(() => {
    return [
      {
        label: "单位数",
        value: data?.unitCount,
        icon: icon1,
      },
      {
        label: "IP资产",
        value: data?.ipAssetsCount,
        icon: icon2,
      },
      {
        label: "域名资产",
        value: data?.domainAssetsCount,
        icon: icon3,
      },
    ];
  }, [data?.domainAssetsCount, data?.ipAssetsCount, data?.unitCount]);

  const columns2 = useMemo(() => {
    return [
      {
        label: "党政机关网站",
        value: data?.agencyWebsiteCount,
        icon: icon4,
      },
      {
        label: "政务系统",
        value: data?.governmentAffairsSystemCount,
        icon: icon5,
      },
      {
        label: "关基",
        value: data?.baseCount,
        icon: icon6,
      },
      {
        label: "公共服务平台",
        value: data?.publicServicePlatformCount,
        icon: icon7,
      },
      {
        label: "新闻网站",
        value: data?.newsWebsiteCount,
        icon: icon8,
      },
      {
        label: "其他",
        value: data?.otherCount,
        icon: icon9,
      },
    ];
  }, [
    data?.agencyWebsiteCount,
    data?.baseCount,
    data?.governmentAffairsSystemCount,
    data?.newsWebsiteCount,
    data?.otherCount,
    data?.publicServicePlatformCount,
  ]);

  const isSingle = useMemo(() => assetsType !== 1, [assetsType]);

  return (
    <div className="absolute left-[48px] top-[142px] flex flex-col items-center">
      <Select
        className="w-[474px] mt-1 mb-4"
        options={options}
        value={assetsType}
        onChange={setAssetsType}
        style={{
          height: 50,
          background: `url(${bg1}) no-repeat left top / cover`,
        }}
        suffixIcon={
          <div className="border-transparent border-t-[#137AD0] border-[8px] mr-3 mt-[8px]"></div>
        }
        variant="borderless"
      ></Select>
      {isSingle && (
        <BgImage
          url={cardBg1}
          className="grid grid-cols-3 w-[474px] mb-[12px] h-[280px] px-4 py-3 items-center cursor-pointer"
          onClick={() => onShowList({})}
        >
          {columns.map((val, index) => (
            <div key={index} className="flex flex-col items-center">
              <img className="w-[72px] object-cover" src={val.icon} alt="" />
              <div className="text-[rgba(255,255,255,0.6)] text-[16px] mt-[-6px]">
                {val.label}
              </div>
              <div className="text-white text-[22px] mt-1">
                <ValueNumber dataValue={val.value ?? 0}></ValueNumber>
              </div>
            </div>
          ))}
        </BgImage>
      )}
      {!isSingle && (
        <BgImage
          url={cardBg2}
          className="grid grid-cols-3 w-[474px] mb-[12px] h-[280px] px-4 py-3 items-center cursor-pointer"
          onClick={() => onShowList({})}
        >
          {columns2.map((val, index) => (
            <div key={index} className="flex flex-col items-center">
              <img
                className="w-[72px] object-cover mt-[-12px]"
                src={val.icon}
                alt=""
              />
              <div className="text-[rgba(255,255,255,0.6)] text-[16px] mt-[-14px]">
                {val.label}
              </div>
              <div className="text-white text-[22px] mt-[-4px]">
                <ValueNumber dataValue={val.value ?? 0}></ValueNumber>
              </div>
            </div>
          ))}
        </BgImage>
      )}
      {/* <AssetsType isSingle={isSingle} /> */}
      <BusinessType />
    </div>
  );
}
export default Statistics;
