import bg from "@/assets/2/bg11.png";
import BgImage from "@/components/BgImage";
import ValueNumber from "@/components/ValueNumber";
import icon1 from "@/assets/2/icon10.png";
import icon2 from "@/assets/2/icon11.png";
import icon3 from "@/assets/2/icon12.png";
import icon4 from "@/assets/2/icon13.png";
import titleBg from "@/assets/2/titleBg.png";
import { useRequest } from "ahooks";
import resourcesService from "@/service/resourcesService";
import { useMemo } from "react";
import Container from "../../container";
import { useTime } from "@/store/useTime";
import { Spin } from "antd";

function AssetsTotal() {
  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      resourcesService.serviceTypeDistribution({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const columns = useMemo(() => {
    return [
      {
        title: "网站类",
        value: data?.webSiteCount,
        icon: icon1,
        businessType: "网站",
      },
      {
        title: "办公管理系统",
        value: data?.platformCount,
        icon: icon2,
        businessType: "办公管理系统",
      },
      {
        title: "业务信息系统",
        value: data?.prodCount,
        icon: icon3,
        businessType: "业务信息系统",
      },
      {
        title: "其他类",
        value: data?.otherCount,
        icon: icon4,
        businessType: "其他",
      },
    ];
  }, [data]);

  const { onShowList } = Container.useContainer();

  return (
    <Spin spinning={loading}>
      <BgImage
        url={titleBg}
        className={`w-[452px] h-[60px] flex items-center mt-4`}
      >
        <span className="text-[22px] text-white ml-6">业务系统</span>
        <span
          className="font-[DINAlternate-Bold] text-[40px] ml-5"
          style={{
            backgroundImage:
              "linear-gradient(0deg, rgba(63, 255, 255, 0.76) 0%, rgba(166, 255, 255, 0.86) 43%,rgba(206, 255, 255, 0.95) 79%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            filter: "drop-shadow(0 0 5px rgba(63, 255, 255, 0.18))", // 模拟外发光
          }}
        >
          <ValueNumber dataValue={data?.allCount ?? 0} />
        </span>
      </BgImage>
      <div className="flex flex-wrap justify-between gap-y-4 mt-[24px] w-[448px] ml-[6px]">
        {columns.map((item, index) => (
          <BgImage
            key={index + 1}
            url={bg}
            onClick={() => {
              onShowList({
                businessType: item.businessType,
              });
            }}
            className="w-full h-[88px] flex items-center justify-between cursor-pointer"
          >
            <div className="ml-6">
              <div
                className="color-text  text-[24px]"
                style={{
                  fontFamily: "DINCond-Bold",
                }}
              >
                <ValueNumber dataValue={item.value ?? 0} />
              </div>
              <div className="text-[16px] color-secondary">{item.title}</div>
            </div>
            <BgImage className="size-[62px] mr-4" url={item.icon} />
          </BgImage>
        ))}
      </div>
    </Spin>
  );
}
export default AssetsTotal;
