import bg from "@/assets/2/bg11.png";
import BgImage from "@/components/BgImage";
import ValueNumber from "@/components/ValueNumber";
import icon1 from "@/assets/2/icon10.png";
import icon2 from "@/assets/2/icon11.png";
import icon3 from "@/assets/2/icon12.png";
import icon4 from "@/assets/2/icon13.png";
import { useRequest } from "ahooks";
import resourcesService from "@/service/resourcesService";
import { useMemo } from "react";
import Container from "../../container";
import { useTime } from "@/store/useTime";
import Card from "@/components/Card";
import titleBg from "@/assets/2/titleBg.png";

function BusinessType() {
  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      resourcesService.serviceTypeDistribution({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const columns = useMemo(() => {
    return [
      {
        title: "网站类",
        value: data?.webSiteCount,
        icon: icon1,
        businessType: "网站",
      },
      {
        title: "办公管理系统",
        value: data?.platformCount,
        icon: icon2,
        businessType: "办公管理系统",
      },
      {
        title: "业务信息系统",
        value: data?.prodCount,
        icon: icon3,
        businessType: "业务信息系统",
      },
      {
        title: "其他类",
        value: data?.otherCount,
        icon: icon4,
        businessType: "其他",
      },
    ];
  }, [data]);

  const { onShowList } = Container.useContainer();

  return (
    <div className="absolute left-[48px] top-[680px]">
      <Card
        headerType={1}
        title="业务系统"
        onMore={() => onShowList({})}
        loading={loading}
      >
        <div className="flex items-center gap-[28px] px-[32px] h-[88px]" style={{ background: `url(${titleBg}) no-repeat left center / cover` }}>
          <div className="text-[rgba(255,255,255,0.8)] text-[32px]">
            总数
          </div>
          <div className="text-white text-[32px]" style={{
            fontFamily: "DINCond-Bold",
          }}>
            <ValueNumber dataValue={data?.allCount ?? 0} />
          </div>
        </div>
        <div>
          <div className="grid grid-cols-2 grid-rows-2 gap-y-4 mt-[24px] w-[448px] mx-auto">
            {columns.map((item, index) => (
              <BgImage
                key={index + 1}
                url={bg}
                onClick={() => {
                  onShowList({
                    businessType: item.businessType,
                  });
                }}
                className="w-full h-[88px] flex items-center justify-between cursor-pointer !bg-center"
              >
                <div className="ml-6">
                  <div
                    className="color-text  text-[24px]"
                    style={{
                      fontFamily: "DINCond-Bold",
                    }}
                  >
                    <ValueNumber dataValue={item.value ?? 0} />
                  </div>
                  <div className="text-[16px] color-secondary">{item.title}</div>
                </div>
                <BgImage className="size-[62px] mr-4" url={item.icon} />
              </BgImage>
            ))}
          </div>
        </div>
      </Card>
    </div>
  );
}
export default BusinessType;
