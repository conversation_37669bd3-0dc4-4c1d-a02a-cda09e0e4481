import floor1 from "@/assets/2/floor1.png";
import floor2 from "@/assets/2/floor2.png";
import floor3 from "@/assets/2/floor3.png";
import floor4 from "@/assets/2/floor4.png";
import floor5 from "@/assets/2/floor5.png";
import floor6 from "@/assets/2/floor6.png";
import BgImage from "@/components/BgImage";
import React from "react";
import TipComp from "./components/TipComp";
import { useRequest } from "ahooks";
import resourcesService from "@/service/resourcesService";
import Container from "../../container";

function StatisticsCount() {
  const { onShowList } = Container.useContainer();

  const { data, loading } = useRequest(() => resourcesService.unitAsset());

  const onViewDetail = (unitType: string) => {
    // 选中的单位类型，详情弹框默认选中筛选条件
    // 国有企业=企业、事业单位、社会团体、党政机关=其他机关、民营企业=其他企业、其他=其他组织机构 新旧类型转换 页面看到的和列表筛选条件的转换
    onShowList({
      unitType,
    });
  };

  if (loading) {
    return null;
  }

  return (
    <React.Fragment>
      <div className="animate-[floorKeyframes1_30s_linear_infinite] animation-direction-alternate floor-hover">
        <BgImage
          className="absolute w-[117px] h-[293px] left-[455px] top-[426px]"
          url={floor1}
          onClick={() => onViewDetail("其他企业")}
        >
          <TipComp>民营企业：4963</TipComp>
        </BgImage>
      </div>
      <div className="animate-[floorKeyframes2_30s_ease-in_infinite] animation-direction-alternate floor-hover">
        <BgImage
          className="absolute z-20 w-[112px] h-[92px] left-[606px] top-[608px]"
          url={floor2}
          onClick={() => onViewDetail("社会团体")}
        >
          <TipComp>社会团体：{data?.socialGroupCount}</TipComp>
        </BgImage>
      </div>
      <div className="animate-[floorKeyframes3_30s_ease-in_infinite] animation-direction-alternate floor-hover">
        <BgImage
          className="absolute w-[100px] h-[461px] left-[825px] top-[346px]"
          url={floor3}
          onClick={() => onViewDetail("企业")}
        >
          <TipComp>国有企业：{data?.stateEnterpriseCount}</TipComp>
        </BgImage>
      </div>
      <div className="animate-[floorKeyframes4_30s_ease-in_infinite] animation-direction-alternate floor-hover">
        <BgImage
          className="absolute w-[100px] h-[466px] left-[961px] top-[274px]"
          url={floor4}
          onClick={() => onViewDetail("其他机关")}
        >
          <TipComp>党政机关：{data?.partyCommitteeOrgCount}</TipComp>
        </BgImage>
      </div>
      <div className="animate-[floorKeyframes5_30s_ease-in_infinite] animation-direction-alternate floor-hover">
        <BgImage
          className="absolute w-[78px] h-[323px] left-[1076px] top-[384px]"
          url={floor5}
          onClick={() => onViewDetail("事业单位")}
        >
          <TipComp>事业单位：{data?.publicInstitutionCount}</TipComp>
        </BgImage>
      </div>
      <div className="animate-[floorKeyframes6_30s_ease-in_infinite] animation-direction-alternate floor-hover">
        <BgImage
          className="absolute w-[107px] h-[140px] left-[1328px] top-[547px]"
          url={floor6}
          onClick={() => onViewDetail("其他组织机构")}
        >
          <TipComp>其他：{data?.otherCount}</TipComp>
        </BgImage>
      </div>
    </React.Fragment>
  );
}
export default StatisticsCount;
