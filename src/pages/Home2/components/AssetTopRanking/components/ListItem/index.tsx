import { motion } from "framer-motion";
import { useMemo } from "react";

interface AssetItem {
  name: string;
  value: number;
}

interface IProps {
  index: number;
  rowGap: number;
  itemHeight: number;
  item: AssetItem;
  showAnimate?: boolean;
  maxValue?: number;
}

function ListItem(props: IProps) {
  const {
    index,
    rowGap,
    itemHeight,
    item,
    maxValue = 100,
    showAnimate = true,
  } = props;
  
  // 获取城市名称和值
  const cityName = item.name;
  const displayValue = item.value;
  
  // 计算宽度百分比
  const widthPercentage = (displayValue / maxValue) * 100;

  const animate = useMemo(() => {
    return {
      width: `${widthPercentage}%`,
      transition: {
        duration: showAnimate ? 1 : 0,
      },
    };
  }, [widthPercentage, showAnimate]);

  const indexStyle: React.CSSProperties = useMemo(() => {
    switch (index) {
      case 0:
        return {
          border: "1px solid rgba(243, 182, 123, 1)",
          background: "rgba(243, 182, 123, 0.65)",
        };
      case 1:
        return {
          border: "1px solid rgba(255, 255, 128, 1)",
          background: "rgba(255, 255, 128, 0.65)",
        };
      case 2:
        return {
          border: "1px solid rgba(111, 184, 255, 1)",
          background: "rgba(111, 184, 255, 0.65)",
        };

      default:
        return {
          background: "rgba(20, 62, 127, 1)",
        };
    }
  }, [index]);

  const barStyle: React.CSSProperties = useMemo(() => {
    switch (index) {
      case 0:
        return {
          background:
            "linear-gradient(90deg,rgba(159, 124, 91, 0.79),rgba(243, 182, 123, 0.79)",
        };
      case 1:
        return {
          background:
            "linear-gradient(90deg,rgba(167, 171, 94, 0.79),rgba(255, 255, 128, 0.79)",
        };
      case 2:
        return {
          background:
            "linear-gradient(90deg,rgba(73, 126, 180, 0.79),rgba(111, 184, 255, 0.79)",
        };
      default:
        return {
          background:
            "linear-gradient(90deg,rgba(14, 50, 102, 0.79),rgba(73, 125, 177, 0.79)",
        };
    }
  }, [index]);

  const valueStyle: React.CSSProperties = useMemo(() => {
    switch (index) {
      case 0:
        return {
          background:
            "linear-gradient(90deg,rgba(243, 182, 123, 0.45),rgba(243, 182, 123, 0.05)",
        };
      case 1:
        return {
          background:
            "linear-gradient(90deg,rgba(167, 171, 94, 0.55),rgba(167, 171, 94, 0.05)",
        };
      case 2:
        return {
          background:
            "linear-gradient(90deg,rgba(111, 184, 255, 0.45),rgba(111, 184, 255, 0.05)",
        };
      default:
        return {
          background:
            "linear-gradient(90deg,rgba(20, 62, 127, 0.45),rgba(20, 62, 127, 0.05)",
        };
    }
  }, [index]);

  return (
    <div
      key={`item${index}`}
      className="flex items-center gap-x-2 cursor-pointer hover:bg-[rgba(57,90,191,.25)]"
      style={{
        marginBottom: rowGap,
        height: itemHeight,
      }}
    >
      <div className="flex-1 h-full px-2 flex items-center gap-x-4 border border-[rgba(197,208,212,.15)]">
        <div
          className="w-[66px] min-w-[66px] h-[26px] color-text flex items-center justify-center"
          style={{
            ...indexStyle,
          }}
        >
          Top {index + 1 < 10 ? 0 : ""}
          {index + 1}
        </div>
        <div className="w-[50px] truncate">{cityName}</div>
        <div className="h-[8px] flex-1 flex items-center bg-[#232E40] relative rounded-[3px] overflow-hidden">
          <motion.div
            className="h-full rounded-[3px]"
            style={barStyle}
            animate={animate}
          ></motion.div>
        </div>
      </div>
      <div
        className="text-white text-[18px] w-[68px] h-full items-center justify-center flex"
        style={{
          fontFamily: "DINCond-Bold",
          ...valueStyle,
        }}
      >
        {displayValue}
      </div>
    </div>
  );
}
export default ListItem; 