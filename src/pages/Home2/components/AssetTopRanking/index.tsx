import Card from "@/components/Card";
import List from "./components/List";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useRequest } from "ahooks";
import resourcesService from "@/service/resourcesService";
import { useMemo, useState } from "react";
import { useTime } from "@/store/useTime";
import { Pagination } from "antd";

function AssetTopRanking() {
  const { startTime, endTime } = useTime();

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const { data, loading } = useRequest(
    () =>
      resourcesService.cityTopList({
        startTime,
        endTime,
        page: current,
        size: pageSize,
      }),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
    }
  );

  const list = useMemo(
    () =>
      data?.cityTopList?.map((val) => ({
        name: val.cityName,
        value: val.count,
      })) ?? [],
    [data?.cityTopList]
  );

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const maxValue = useMemo(() => {
    if (list.length > 0) {
      return Math.max(...list.map(item => item.value));
    }
    return 100;
  }, [list]);

  return (
    <div className="absolute right-[46px] top-[650px]">
      <MoreComp
        open={open}
        onCancel={setFalse}
        key={visibleKey}
      />
      <Card
        headerType={1}
        title="地市资产排名Top10"
        onMore={setTrue}
        loading={loading}
        bodyClass="!py-1"
      >
        <List 
          data={list.slice(0, 10)} 
          maxValue={maxValue}
        />
        {list.length > pageSize && (
          <div className="flex justify-end items-center">
            <Pagination
              size="small"
              total={list.length}
              current={current}
              pageSize={pageSize}
              onChange={(page, pageSize) => {
                setCurrent(page);
                setPageSize(pageSize);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}
export default AssetTopRanking;
