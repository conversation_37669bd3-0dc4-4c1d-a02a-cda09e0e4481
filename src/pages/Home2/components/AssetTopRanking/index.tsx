import Card from "@/components/Card";
import Chart from "./components/Chart";
import ProModal from "@/components/Modal";
import useVisible from "@/hooks/useVisible";
import { useRequest } from "ahooks";
import resourcesService from "@/service/resourcesService";
import { useMemo, useState } from "react";
import { useTime } from "@/store/useTime";

function AssetTopRanking() {
  const { startTime, endTime } = useTime();

  const [key, setKey] = useState(0);

  const { data, loading } = useRequest(
    () =>
      resourcesService.cityTopList({
        startTime,
        endTime,
        page: 1,
        size: 10,
      }),
    {
      refreshDeps: [startTime, endTime],
      onSuccess: () => {
        setKey((prev) => prev + 1);
      },
    }
  );

  const list = useMemo(
    () =>
      data?.cityTopList?.map((val) => ({
        name: val.cityName,
        value: val.count,
      })) ?? [],
    [data?.cityTopList]
  );

  const top10List = useMemo(() => {
    return [...list]?.slice(0, 10) ?? [];
  }, [list]);

  const [open, { setFalse, setTrue }] = useVisible(false);

  return (
    <div className="absolute left-[542px] top-[590px] w-[826px]" key={key}>
      <ProModal
        type={3}
        title="应急指挥实时事件"
        open={open}
        onCancel={setFalse}
      >
        <div className="mt-6 ml-2">
          <Chart width={1330} height={420} data={list} />
        </div>
      </ProModal>
      <Card
        headerType={2}
        title="地市资产排名Top10"
        onMore={setTrue}
        loading={loading}
      >
        <Chart width={836} height={350} data={top10List} />
      </Card>
    </div>
  );
}
export default AssetTopRanking;
