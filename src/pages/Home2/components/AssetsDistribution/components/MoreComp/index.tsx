import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import resourcesService from "@/service/resourcesService";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { useTime } from "@/store/useTime";
function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const { startTime, endTime } = useTime();

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "industryName",
      title: "行业名称",
    },
    {
      dataIndex: "count",
      title: "资产数",
    },
  ];

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await resourcesService.industryAssetsCountList({
      ...params,
      startTime,
      endTime,
    });
    return {
      data: res.industryAssetsCountList,
      total: res.total,
    };
  };

  return (
    <ProModal type={1} title="行业资产分布" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 390,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
