import { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Card from "@/components/Card";
import Container from "../../container";
import ValueNumber from "@/components/ValueNumber";
import { Pagination } from "antd";

// 设备图标
import pc from "@/assets/2/icon1.png";
import server from "@/assets/2/icon2.png";
import router from "@/assets/2/icon3.png";
import firewall from "@/assets/2/icon4.png";
import switch1 from "@/assets/2/icon5.png";
import cloud from "@/assets/2/icon6.png";
import terminal from "@/assets/2/icon7.png";
import storage from "@/assets/2/icon8.png";
import camera from "@/assets/2/icon9.png";
import mobile from "@/assets/2/icon10.png";
import tablet from "@/assets/2/icon11.png";
import monitor from "@/assets/2/icon12.png";

// 定义设备类型的接口
interface DeviceItem {
  id: number;
  name: string;
  count: number;
  icon: string;
  assetsType: string; // 对应后端接口的资产类型
}

function DeviceType() {
  const { onShowList } = Container.useContainer();
  const [currentPage, setCurrentPage] = useState(1);
  const [direction, setDirection] = useState(1); // 1表示向右，-1表示向左
  const itemsPerPage = 9;

  // 创建模拟数据
  const allDevices = useMemo<DeviceItem[]>(() => {
    return [
      { id: 1, name: "个人电脑", count: 2453, icon: pc, assetsType: "PC" },
      { id: 2, name: "服务器", count: 1896, icon: server, assetsType: "SERVER" },
      { id: 3, name: "路由器", count: 1247, icon: router, assetsType: "ROUTER" },
      { id: 4, name: "防火墙", count: 987, icon: firewall, assetsType: "FIREWALL" },
      { id: 5, name: "交换机", count: 1742, icon: switch1, assetsType: "SWITCH" },
      { id: 6, name: "云设备", count: 654, icon: cloud, assetsType: "CLOUD" },
      { id: 7, name: "终端设备", count: 3124, icon: terminal, assetsType: "TERMINAL" },
      { id: 8, name: "存储设备", count: 825, icon: storage, assetsType: "STORAGE" },
      { id: 9, name: "摄像设备", count: 2136, icon: camera, assetsType: "CAMERA" },
      { id: 10, name: "移动设备", count: 1435, icon: mobile, assetsType: "MOBILE" },
      { id: 11, name: "平板设备", count: 768, icon: tablet, assetsType: "TABLET" },
      { id: 12, name: "监控设备", count: 1287, icon: monitor, assetsType: "MONITOR" },
    ];
  }, []);

  // 当前页显示的设备
  const currentDevices = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return allDevices.slice(startIndex, startIndex + itemsPerPage);
  }, [allDevices, currentPage]);

  // 处理页面变化
  const handlePageChange = (page: number) => {
    if (page === currentPage) return;
    setDirection(page > currentPage ? 1 : -1);
    setCurrentPage(page);
  };

  return (
    <div className="absolute left-[48px] top-[380px]">
      <Card
        headerType={1}
        title="设备类型"
        onMore={() => onShowList({})}
      >
        <div className="flex flex-col relative" style={{ minHeight: "240px"}}>
          <AnimatePresence mode="wait" initial={false}>
            <motion.div
              key={currentPage}
              initial={{ opacity: 0, x: direction * 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -direction * 50 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-3 gap-4 w-full"
            >
              {currentDevices.map((device) => (
                <div
                  key={device.id}
                  className="flex items-center gap-[12px] justify-center bg-[rgba(20,62,127,0.2)] rounded-[4px] py-[8px] cursor-pointer hover:bg-[rgba(57,90,191,0.3)]"
                  onClick={() => onShowList({ assetsType: device.assetsType })}
                >
                  <div className="size-[32px] border border-[rgba(255,255,255,0.2)] flex items-center justify-center">
                  
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-[rgba(255,255,255,0.8)] text-center text-[14px]">
                      {device.name}
                    </div>
                    <div className="text-white font-bold text-[14px]">
                      <ValueNumber dataValue={device.count} />
                    </div>
                  </div>
                </div>
              ))}
            </motion.div>
          </AnimatePresence>
          
          {allDevices.length > itemsPerPage && (
            <div className="flex justify-end items-center absolute bottom-0 right-0">
              <Pagination
                size="small"
                total={allDevices.length}
                current={currentPage}
                pageSize={itemsPerPage}
                onChange={handlePageChange}
                showSizeChanger={false}
                showTotal={(total: number) => <span>共 {total} 条记录</span>}
              />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

export default DeviceType; 