import { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Card from "@/components/Card";
import Container from "../../container";
import ValueNumber from "@/components/ValueNumber";
import { Pagination } from "antd";
import icon33 from "@/assets/2/icon33.png";
import { useRequest } from "ahooks";
import resourcesService from "@/service/resourcesService";
import { useTime } from "@/store/useTime";

function DeviceType() {
  const { onShowList } = Container.useContainer();
  const [currentPage, setCurrentPage] = useState(1);
  const [direction, setDirection] = useState(1); // 1表示向右，-1表示向左
  const itemsPerPage = 9;

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(() =>
    resourcesService.assetsDistributionList({
      startTime,
      endTime,
    }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  // 创建模拟数据
  const allDevices = useMemo(() => {
    return data?.assetsDistributionList ?? [];
  }, [data?.assetsDistributionList]);

  // 当前页显示的设备
  const currentDevices = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return allDevices.slice(startIndex, startIndex + itemsPerPage);
  }, [allDevices, currentPage]);

  // 处理页面变化
  const handlePageChange = (page: number) => {
    if (page === currentPage) return;
    setDirection(page > currentPage ? 1 : -1);
    setCurrentPage(page);
  };

  return (
    <div className="absolute left-[48px] top-[380px]">
      <Card
        headerType={1}
        title="设备类型"
        onMore={() => onShowList({})}
      >
        <div className="flex flex-col relative" style={{ minHeight: "240px"}}>
          <AnimatePresence mode="wait" initial={false}>
            <motion.div
              key={currentPage}
              initial={{ opacity: 0, x: direction * 50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -direction * 50 }}
              transition={{ duration: 0.3 }}
              className="grid grid-cols-3 gap-4 w-full"
            >
              {currentDevices.map((device) => (
                <div
                  key={device.assetsTypeName}
                  className="flex items-center gap-[12px] justify-center bg-[rgba(20,62,127,0.2)] rounded-[4px] py-[8px] cursor-pointer hover:bg-[rgba(57,90,191,0.3)]"
                  onClick={() => onShowList({ assetsType: device.assetsTypeName })}
                >
                  <div className="size-[32px] flex items-center justify-center">
                    <img src={icon33} alt={device.assetsTypeName} className="size-full" />
                  </div>
                  <div className="flex flex-col items-center justify-center">
                    <div className="text-[rgba(255,255,255,0.8)] text-center text-[14px]">
                      {device.assetsTypeName}
                    </div>
                    <div className="text-white font-bold text-[14px]">
                      <ValueNumber dataValue={Number(device?.count) ?? 0} />
                    </div>
                  </div>
                </div>
              ))}
            </motion.div>
          </AnimatePresence>
          
          {allDevices.length > itemsPerPage && (
            <div className="flex justify-end items-center absolute bottom-0 right-0">
              <Pagination
                size="small"
                total={allDevices.length}
                current={currentPage}
                pageSize={itemsPerPage}
                onChange={handlePageChange}
                showSizeChanger={false}
                showTotal={(total: number) => <span>共 {total} 条记录</span>}
              />
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

export default DeviceType; 