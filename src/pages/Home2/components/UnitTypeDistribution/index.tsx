import Card from "@/components/Card";
import Chart from "./components/Chart";
import { useRequest } from "ahooks";
import resourcesService from "@/service/resourcesService";
import { useMemo } from "react";
import { useTime } from "@/store/useTime";
import Container from "../../container";

function UnitTypeDistribution() {
  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      resourcesService.unitAsset({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () => [
      {
        name: "党政机关",
        value: data?.partyCommitteeOrgCount ?? 0,
      },
      {
        name: "重点新闻网站",
        value: data?.publicInstitutionCount ?? 0,
      },
      {
        name: "公共服务平台",
        value: data?.socialGroupCount ?? 0,
      },
      {
        name: "重点行业单位",
        value: data?.stateEnterpriseCount ?? 0,
      },
      {
        name: "民营企业",
        value: data?.privateNonEnterpriseUnitCount ?? 0,
      },
      {
        name: "其他",
        value: data?.otherCount ?? 0,
      },
    ],
    [data]
  );

  const { onShowList } = Container.useContainer();

  return (
    <div className="absolute left-[542px] top-[142px] w-[826px]">
      <Card
        headerType={2}
        title="单位类型分布"
        onMore={() => onShowList({})}
        loading={loading}
      >
        <Chart width={836} height={360} data={list.slice(0, 10)} />
      </Card>
    </div>
  );
}
export default UnitTypeDistribution;
