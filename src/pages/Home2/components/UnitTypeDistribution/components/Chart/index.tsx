import type { ContentType } from "recharts/types/component/Tooltip";
import type {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import type { LinearConfig } from "@/typings";
import { nanoid } from "nanoid";
// import { useRafInterval } from "ahooks";
import { useCallback, useRef, useState } from "react";
import { Bar, BarChart, CartesianGrid, Tooltip, XAxis, YAxis } from "recharts";
import SvgLinearGradient from "@/components/Comp/SvgLinearGradient";
import tipArrow from "@/assets/2/tipArrow.png";
import BgImage from "@/components/BgImage";
import Container from "@/pages/Home2/container";

interface DataItem {
  name: string;

  value: number;
}

interface IProps {
  data: DataItem[];

  width: number;

  height: number;
}

interface ColorConfig {
  top: LinearConfig;
  center: LinearConfig;
}

function Chart(props: IProps) {
  const { data, width, height } = props;

  const { onShowList } = Container.useContainer();
  const customXTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;
    return (
      <g>
        <foreignObject
          style={{
            overflow: "visible",
          }}
          width="1"
          height="1"
          x={x}
          y={y + 5}
        >
          <div className="color-chart">
            <div
              style={{
                width: "fit-content",
                transform: `translate(-50%, 0px) rotate(0deg)`,
                textAlign: "center",
              }}
              className="truncate w-[40px]"
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const customYTick = (props: any) => {
    const {
      x,
      y,
      payload: { value },
    } = props;

    return (
      <g>
        <foreignObject
          width="1"
          height="1"
          x={x}
          y={y}
          style={{
            overflow: "visible",
          }}
        >
          <div className="color-chart">
            <div
              style={{
                width: "max-content",
                transform: `translate(-100%, -50%)`,
                textAlign: "right",
              }}
            >
              {value}
            </div>
          </div>
        </foreignObject>
      </g>
    );
  };

  const filterIdPrefix = useRef(nanoid());

  const colorConfig: ColorConfig = {
    center: {
      angle: 180,
      colorStops: [
        {
          offset: 0,
          color: "rgba(0,155,254,.95)",
        },
        {
          offset: 1,
          color: "rgba(0,155,254,.35)",
        },
      ],
    },
    top: {
      angle: 90,
      colorStops: [
        {
          offset: 0,
          color: "rgba(0,155,254,.55)",
        },
        {
          offset: 1,
          color: "rgba(0,155,254,.55)",
        },
      ],
    },
  };

  const barRef = useRef(new Map<string, number>());

  const CustomBarShape = useCallback((props: any) => {
    const shapeProps = props.shapeProps;
    const { x, y, width, height, name } = shapeProps;

    const skewAngle = 23; // 你的 skew 角度

    const verticalOffset = (width / 2) * Math.tan((skewAngle * Math.PI) / 180);

    const topFill = `url(#${filterIdPrefix.current}-top)`;

    const centerFill = `url(#${filterIdPrefix.current}-center)`;

    barRef.current.set(name, height);

    return (
      <g>
        <g
          style={{
            transform: `skew(0deg, ${skewAngle}deg)`,
            transformOrigin: `${x}px ${y}px`,
            opacity: 1,
          }}
        >
          <rect
            width={width / 2}
            height={height}
            x={x}
            y={y - verticalOffset}
            fill={centerFill}
            stroke="rgba(6,197,255,.85)"
            strokeWidth={0.5}
            style={{
              filter: "url(#filter_front)",
            }}
          ></rect>
          <rect
            width={width / 2}
            height={height}
            x={x + width / 2}
            y={y - width / 2 - verticalOffset}
            fill={centerFill}
            stroke="rgba(6,197,255,.85)"
            strokeWidth={0.5}
            style={{
              transformOrigin: `${x + width}px ${y}px`,
              transform: "skew(0deg, -45deg)",
            }}
          ></rect>
          <rect
            width={width / 2}
            height={width / 2}
            x={x}
            y={y - verticalOffset}
            fill={topFill}
            stroke="rgba(6,197,255,.85)"
            strokeWidth={0.5}
            style={{
              transformOrigin: `${x + width}px ${y - verticalOffset}px`,
              transform: `skew(-45deg) translate(0px, -${width / 2}px)`,
            }}
          ></rect>
        </g>
      </g>
    );
  }, []);

  // 轴单位
  const customizedLabel = useCallback((props) => {
    return (
      <g>
        <foreignObject
          width="1"
          height="1"
          x={props.viewBox.x}
          y={props.viewBox.y}
          style={{
            overflow: "visible",
          }}
        >
          <div
            style={{
              width: "max-content",
              transform: "translate(-20px, -40px)",
            }}
            className="color-chart"
          >
            (单位)
          </div>
        </foreignObject>
      </g>
    );
  }, []);

  const tooltipContent: ContentType<ValueType, NameType> = (props) => {
    let { payload = [], coordinate, label, viewBox } = props ?? {};
    if (payload?.length > 0 && label && viewBox?.height) {
      const barHeight = barRef.current.get(label) ?? 0;
      const value = payload[0].value;

      const xValue = coordinate?.x ?? 1;

      return (
        <div
          className="-translate-x-1/2 flex flex-col items-center"
          style={{
            marginLeft: xValue - 1,
            marginTop: viewBox?.height - barHeight - 44,
          }}
        >
          <div
            className="flex items-center justify-center h-[44px] color-text gap-x-2 bg-[rgba(50,91,174,0.4)] rounded-[8px] overflow-hidden px-6"
            style={{
              boxShadow: "inset 0px 0 20px 0px #5FC1FF",
              backdropFilter: "blur(12px)",
            }}
          >
            <span className="text-[17px]">{label}</span>
            <span
              className="text-[22px]"
              style={{
                fontFamily: "DINCond-Bold",
              }}
            >
              {value}
            </span>
          </div>
          <BgImage url={tipArrow} className="w-[26px] h-[21px] mt-2" />
        </div>
      );
    }

    return null;
  };

  const [defaultIndex, setDefaultIndex] = useState<number | undefined>(
    undefined
  );

  // 悬浮中 暂停轮播
  const [isHovering, setIsHovering] = useState(false);

  // useRafInterval(
  //   () => {
  //     setDefaultIndex((val) => {
  //       if (val === undefined) {
  //         return 0;
  //       }

  //       const total = data.length ?? 0;

  //       return (val + 1) % total;
  //     });
  //   },
  //   isHovering ? undefined : 2500
  // );

  return (
    <div
      className="w-full h-full"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <BarChart
        width={width}
        height={height}
        data={data}
        className="text-[14px]"
        margin={{
          top: 50,
          right: 20,
          left: 20,
          bottom: 10,
        }}
        barSize={20}
      >
        <CartesianGrid vertical={false} stroke="rgba(168, 168, 168, 0.2)" />
        <XAxis
          dataKey="name"
          tick={customXTick}
          tickLine={false}
          axisLine={{
            stroke: "rgba(201, 201, 201, 0.55)",
          }}
        />
        <YAxis
          tickLine={false}
          axisLine={false}
          tick={customYTick}
          width={30}
          label={customizedLabel}
        />
        <defs key={filterIdPrefix.current}>
          <SvgLinearGradient
            id={`${filterIdPrefix.current}-top`}
            config={colorConfig.top}
          />
          <SvgLinearGradient
            id={`${filterIdPrefix.current}-center`}
            config={colorConfig.center}
          />
          <filter id="filter_front">
            <feComponentTransfer>
              <feFuncR type="linear" slope="0.5"></feFuncR>
              <feFuncG type="linear" slope="0.5"></feFuncG>
              <feFuncB type="linear" slope="0.5"></feFuncB>
            </feComponentTransfer>
          </filter>
        </defs>
        <Bar
          dataKey="value"
          barSize={30}
          fill="#1DB7FA"
          shape={(props: any) => {
            return <CustomBarShape shapeProps={props} />;
          }}
          label={false}
          onAnimationEnd={() => {
            // setIsHovering(false);
            setDefaultIndex(0);
          }}
          className="cursor-pointer"
          onClick={(e) => {
            // 跳转到指定数据
            onShowList({
              unitType: e.name,
            });
          }}
        />
        {data.length > 0 && (
          <Tooltip
            cursor={false}
            trigger="hover"
            active={isHovering}
            defaultIndex={defaultIndex}
            content={tooltipContent}
            allowEscapeViewBox={{
              x: true,
              y: true,
            }}
            wrapperStyle={{
              transform: "none",
              width: "max-content",
            }}
            isAnimationActive={false}
          />
        )}
      </BarChart>
    </div>
  );
}
export default Chart;
