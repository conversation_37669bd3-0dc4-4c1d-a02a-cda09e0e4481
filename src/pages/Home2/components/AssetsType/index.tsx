import bg6 from "@/assets/1/bg6.png";
import bg7 from "@/assets/1/bg7.png";
import titleBg from "@/assets/2/titleBg.png";
import BgImage from "@/components/BgImage";
import { nanoid } from "nanoid";
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip } from "recharts";
import type { ContentType } from "recharts/types/component/Tooltip";
import type {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import React, { useCallback, useMemo, useRef } from "react";
import SvgLinearGradient from "@/components/Comp/SvgLinearGradient";
import type { LinearConfig } from "@/typings";
import { formatLinear } from "@/utils";
import { useRequest } from "ahooks";
import resourcesService from "@/service/resourcesService";
import Container from "../../container";
import { useTime } from "@/store/useTime";
import { Spin } from "antd";
import ValueNumber from "@/components/ValueNumber";

interface IProps {
  isSingle: boolean;
}
function TypeRanking(props: IProps) {
  const { isSingle } = props;
  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(() =>
    resourcesService.assetsDistributionList({
      startTime,
      endTime,
    }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const { onShowList } = Container.useContainer();

  const list = useMemo(() => {
    const value = data?.assetsDistributionList ?? [];
    return value.map((val) => ({
      name: val.assetsTypeName,
      value: val.count,
      id:val.assetsType
    }));
  }, [data?.assetsDistributionList]);

  const colorList: LinearConfig[] = useMemo(
    () => [
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(166, 207, 255, 1)",
          },
          {
            offset: 1,
            color: "rgba(166, 207, 255, 0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(44, 81, 250, 1)",
          },
          {
            offset: 1,
            color: "rgba(44, 81, 250, 0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(211,173,247,1)",
          },
          {
            offset: 1,
            color: "rgba(211,173,247,0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(238, 111, 124, 1)",
          },
          {
            offset: 1,
            color: "rgba(238, 111, 124, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(255, 207, 95, 1)",
          },
          {
            offset: 1,
            color: "rgba(255, 207, 95, .75)",
          },
        ],
      },
    ],
    []
  );

  const linearIdRef = useRef(`${nanoid()}_linear_`);

  const getColor = useCallback(
    (index: number) => {
      return colorList[index];
    },
    [colorList]
  );

  const CustomTooltip: ContentType<ValueType, NameType> = ({
    active,
    payload,
  }) => {
    if (active && payload && payload.length) {
      return (
        <div
          className="w-fit justify-center color-text bg-[rgba(50,91,174,0.4)] rounded-[4px] px-5 py-2"
          style={{
            boxShadow: "inset 0px 0 20px 0px #5FC1FF",
            backdropFilter: "blur(12px)",
          }}
        >
          {payload.map((val) => (
            <div className="flex items-center gap-x-3" key={val.dataKey}>
              <span className="text-[16px] w-auto">{val.name}</span>
              <span
                className="text-[22px]"
                style={{
                  fontFamily: "DINCond-Bold",
                  color: val.color,
                }}
              >
                {val.value}
              </span>
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  return (
    <Spin spinning={loading}>
      <BgImage
        url={titleBg}
        className={`w-[452px] h-[60px] flex items-center ${isSingle ? "mt-[32px]" : "mt-[24px]"}`}
      >
        <span className="text-[22px] text-white ml-6">硬件设备</span>
        <span
          className="font-[DINAlternate-Bold] text-[40px] ml-5"
          style={{
            backgroundImage:
              "linear-gradient(0deg, rgba(63, 255, 255, 0.76) 0%, rgba(166, 255, 255, 0.86) 43%,rgba(206, 255, 255, 0.95) 79%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            filter: "drop-shadow(0 0 5px rgba(63, 255, 255, 0.18))", // 模拟外发光
          }}
        >
          <ValueNumber dataValue={data?.assetsAllCount ?? 0} />
        </span>
      </BgImage>

      <div className="flex mt-[24px] w-[390px] items-center">
        <div
          className="size-[180px] relative flex justify-center items-center cursor-pointer"
          onClick={() => onShowList({})}
        >
          <div className="centered">
            <BgImage
              url={bg6}
              className="w-[186px] h-[180px] animate-[rotateCounterClockwise_16s_linear_infinite]"
            />
          </div>
          <div className="centered">
            <BgImage
              url={bg7}
              className="size-[90px] animate-[rotateClockwise_16s_linear_infinite]"
            ></BgImage>
          </div>
          <PieChart
            width={190}
            height={190}
            margin={{
              left: 0,
              right: 0,
              top: 0,
              bottom: 0,
            }}
          >
            {colorList.map((_, index) => {
              const linearId = `${linearIdRef.current}${index}`;
              const colorConfig = getColor(index);
              return (
                <React.Fragment key={linearId}>
                  <defs>
                    <SvgLinearGradient id={linearId} config={colorConfig} />
                  </defs>
                </React.Fragment>
              );
            })}
            <Pie
              data={list}
              cx={95}
              cy={95}
              innerRadius={62}
              outerRadius={76}
              paddingAngle={6} // 饼图内间距
              dataKey="value"
              stroke="none"
              cornerRadius={0} // 圆角
              isAnimationActive={false}
            >
              {list.map((entry, index) => {
                const linearId = `${linearIdRef.current}${index}`;
                return (
                  <React.Fragment key={linearId}>
                    <Cell
                      style={{
                        outline: "none",
                        overflow: "hidden",
                      }}
                      key={`cell-${index}`}
                      fill={`url(#${linearId})`}
                    />
                  </React.Fragment>
                );
              })}
            </Pie>
            <Tooltip
              content={<CustomTooltip />}
              wrapperStyle={{
                width: "max-content",
              }}
            />
          </PieChart>
        </div>
        <div className="flex flex-col flex-1 gap-y-[10px] self-center ml-4">
          {list.map((val, index) => {
            const colorConfig = getColor(index);
            return (
              <div
                key={`legend${index + 1}`}
                className="flex items-center justify-between cursor-pointer"
                onClick={() => onShowList({ assetsType: val.id })}
              >
                <div className="flex items-center">
                  <span
                    className="size-[10px] rounded-full inline-block"
                    style={{
                      background: formatLinear(colorConfig),
                    }}
                  ></span>
                  <span className="ml-4 color-text text-[16px]">
                    {val.name}
                  </span>
                </div>
                <div
                  className="ml text-[20px]"
                  style={{
                    backgroundImage: formatLinear(colorConfig),
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    // fontFamily: "YouSheBiaoTiHei-2",
                  }}
                >
                  {val.value}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </Spin>
  );
}
export default TypeRanking;
