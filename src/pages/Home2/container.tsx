import { createContainer } from "unstated-next";
import { useState } from "react";
import useVisible from "@/hooks/useVisible";

function useContainer() {
  const [selectId, setSelectId] = useState<string>();

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const [params, setParams] = useState<
    Partial<API.Resources.BaseAssetsListParams>
  >({});

  // 打开资产详单弹框列表
  const onShowList = (params: Partial<API.Resources.BaseAssetsListParams>) => {
    setParams(params);
    setTrue();
  };

  return {
    setSelectId,
    selectId,
    open,
    setFalse,
    setTrue,
    visibleKey,
    onShowList,
    params,
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
