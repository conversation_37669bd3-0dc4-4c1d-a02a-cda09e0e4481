import BgImage from "@/components/BgImage";
import bg from "@/assets/index/bg4.webp";
import styles from "./index.module.less";
import { history } from "umi";
import dictInfo from "@/dictInfo";

function CircleList() {
  const data = dictInfo.pageType;

  return (
    <div>
      {data.map((val) => (
        <BgImage
          key={val.value}
          url={bg}
          onClick={() => {
            history.push(val.value);
          }}
          className={`${styles.item} absolute`}
        >
          <div className="text-[#E6F7FF] w-full text-center pt-[12px] text-[26px] font-['Alibaba-PuHuiTi-Bold']">
            {val.label}
          </div>
        </BgImage>
      ))}
    </div>
  );
}
export default CircleList;
