@keyframes animX {
  0% {
    left: 0px;
  }
  100% {
    left: 1440px;
  }
}
@keyframes animY {
  0% {
    top: 0px;
  }
  100% {
    top: 310px;
  }
}
@keyframes scale {
  0% {
    transform: scale(0.5);
    z-index: 0;
  }
  50% {
    transform: scale(0.95);
    z-index: 3;
  }
  100% {
    transform: scale(0.6);
    z-index: 0;
  }
}

.box {
  width: 1440px;
  height: 350px;
  background: red;
}

.item {
  position: absolute;
  color: #fff;
  font-size: 22px;
  display: flex;
  justify-content: center;
  width: 173px;
  height: 221px;
  cursor: pointer;

  &:hover {
    background-image: url("~@/assets/index/bg4.png") !important;
  }

  &:nth-of-type(1){

  }
}

.hovered {
  animation-play-state: paused;
}
