import { useState } from "react";
import BgImage from "@/components/BgImage";
import bg from "@/assets/index/bg3.png";
import styles from "./index.module.less";
import dictInfo from "@/dictInfo";
import { history } from "umi";

function CircleList() {
  const data = dictInfo.pageType;

  const [hovered, setHovered] = useState(false);

  return (
    <div className={styles.box}>
      {data.map((val, index) => (
        <BgImage
          key={index}
          url={bg}
          className={`${styles.item} ${hovered && styles.hovered}`}
          onMouseEnter={() => {
            setHovered(true);
          }}
          onMouseLeave={() => {
            setHovered(false);
          }}
          onClick={() => {
            history.push(val.value);
          }}
        >
          <div
            className="text-[40px] text-[#E6F7FF] w-full text-center pt-[12px]"
            style={{
              fontFamily: "优设标题黑",
            }}
          >
            {val.label}
          </div>
        </BgImage>
      ))}
    </div>
  );
}
export default CircleList;
