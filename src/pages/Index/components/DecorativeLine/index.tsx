import circleBg from "@/assets/index/bg5.png";

function DecorativeLine() {
  return (
    <div className="w-[1920px] h-[1080px] centered pointer-events-none">
      <svg>
        <defs>
          <linearGradient
            id="strokeGradientLine1"
            x1="0%"
            y1="50%"
            x2="100%"
            y2="50%"
            gradientTransform="rotate(-90 0.5 0.5)"
          >
            <stop offset="0%" stop-color="#00c3ff"></stop>
            <stop offset="100%" stop-color="RGBA(37,105,255,0)"></stop>
          </linearGradient>
        </defs>
      </svg>
      <div
        className="w-[1247px] h-[376px] centered"
        style={{
          borderRadius: "50%",
          border: "2px solid transparent",
          filter: "blur(5px)",
        }}
      >
        <svg className="w-[1246px] h-[388px] absolute">
          <ellipse
            cx="623"
            cy="194"
            rx="623"
            ry="194"
            style={{
              fill: "none",
              stroke: `url("#strokeGradientLine1")`,
              strokeWidth: 1,
            }}
          ></ellipse>
        </svg>
      </div>
      <img src={circleBg} />
      <div
        className="w-[1247px] h-[376px] centered"
        style={{
          borderRadius: "50%",
          backgroundImage: `url(${circleBg})`,
          backgroundSize: "cover",
          border: "2px solid transparent",
        }}
      >
        <svg className="w-[1246px] h-[388px] absolute">
          <ellipse
            cx="623"
            cy="194"
            rx="623"
            ry="194"
            style={{
              fill: "none",
              stroke: `url("#strokeGradientLine1")`,
              strokeWidth: 1,
            }}
          ></ellipse>
        </svg>
      </div>
    </div>
  );
}
export default DecorativeLine;
