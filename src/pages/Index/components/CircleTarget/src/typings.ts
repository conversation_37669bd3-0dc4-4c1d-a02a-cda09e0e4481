import type { SingleRatioDirectionEnum } from "@/typings";
import type { TextCommonConfig } from "@/typings/configTypes";

export interface PlacardType {
  /**
   * 宽度
   */
  width: number;

  /**
   * 高度
   */
  height: number;

  /**
   * 图片
   */
  url: string;
}

export interface ImageItemType {
  /**
   * 名称值
   */
  name: string;

  /**
   * 图片
   */
  url: string;

  /**
   * 宽度
   */
  width: number;

  /**
   * 高度
   */
  height: number;

  /**
   * x轴偏移
   */
  offsetX: number;

  /**
   * y轴偏移
   */
  offsetY: number;
}

export interface FigureType {
  /**
   * 是否开启
   */
  show: boolean;

  /**
   * 文本样式
   */
  textBasic: TextCommonConfig;

  /**
   * x轴偏移
   */
  offsetX: number;

  /**
   * y轴偏移
   */
  offsetY: number;

  /**
   * 后缀
   */
  suffix: {
    /**
     * 是否展示
     */
    show: boolean;

    /**
     * 内容
     */
    value: string;

    /**
     * 文本样式
     */
    textBasic: TextCommonConfig;

    /**
     * x轴偏移
     */
    offsetX: number;

    /**
     * y轴偏移
     */
    offsetY: number;
  };
}

export interface NameType {
  /**
   * 是否开启
   */
  show: boolean;

  /**
   * 文本样式
   */
  textBasic: TextCommonConfig;

  /**
   * x轴偏移
   */
  offsetX: number;

  /**
   * y轴偏移
   */
  offsetY: number;
}

export interface ItemConfigType {
  /**
   * 透明度
   */
  opacity: number;

  /**
   * 整体缩放
   */
  zoom?: number;

  /**
   * 垂直偏移
   */
  offsetY?: number;

  /**
   * 标牌
   */
  placard: PlacardType;

  /**
   * 图片列表
   */
  imageList: ImageItemType[];

  /**
   * 数字
   */
  figure: FigureType;

  /**
   * 名称
   */
  name: NameType;
}

export interface Config {
  basicConfig: {
    /**
     * 始终面对相机
     */
    faceCamera: boolean;

    /**
     * 相机位置
     */
    cameraPosition: {
      x: number;
      y: number;
      z: number;
    };

    /**
     * 半径
     */
    radius: number;

    /**
     * 滚动时长
     */
    duration: number;

    /**
     * 自动轮播
     */
    carousel: {
      /**
       * 是否轮播
       */
      show: boolean;

      /**
       * 支持悬停
       */
      hoverSupport: boolean;

      /**
       * 方向
       */
      direction: SingleRatioDirectionEnum;

      /**
       * 间隔时长
       */
      durationInterval: number;

      /**
       * 点击停留
       */
      stayTime: number;
    };
  };
  itemConfig: {
    /**
     * 默认样式
     */
    default: ItemConfigType;

    /**
     * 选中样式
     */
    active: ItemConfigType;
  };
}
