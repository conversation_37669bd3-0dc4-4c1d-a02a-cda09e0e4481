import type React from "react";
import type { ItemConfigType } from "../../typings";

interface IProps {
  config: ItemConfigType;
}

function useItemContainerStyle(props: IProps) {
  const { config } = props;
  return {
    width: config.placard.width,
    height: config.placard.height,
    backgroundImage: `url(${config.placard.url})`,
    backgroundSize: "100% 100%",
    backgroundPosition: "center",
    opacity: config.opacity / 100,
    transform: `scale(${config?.zoom ?? 1}) translate(0px, ${
      config?.offsetY ?? 0
    }px)`,
    display: "flex",
    justifyContent: "center",
    flexDirection: "column",
    alignItems: "center",
  } as React.CSSProperties;
}
export default useItemContainerStyle;
