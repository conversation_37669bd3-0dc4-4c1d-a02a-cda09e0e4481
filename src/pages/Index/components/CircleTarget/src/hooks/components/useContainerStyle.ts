import type React from "react";
import type { Config } from "../../typings";
import { useMemo } from "react";

interface IProps {
  width: number;
  height: number;
  config: Config["basicConfig"];
}

export default (props: IProps) => {
  const { width, height, config } = props;
  const perspectiveValue = height * 1.207105;
  // matrix3d(a1, b1, c1, d1, a2, b2, c2, d2, a3, b3, c3, d3, a4, b4, c4, d4)
  const matrix3dValue = useMemo(() => {
    const { y, z } = config.cameraPosition;
    const c4 = (-Math.sqrt(z * z + y * y)).toFixed(3);
    const c3 = y !== 0 ? Math.atan(z / y) : 0;
    const b3 = Math.cos(c3);
    const a2 = -Math.sin(c3);
    const b2 = Math.cos(c3);
    return `1, 0, 0, 0, 0, ${a2}, ${b2}, 0, 0, ${b3}, ${c3}, 0, 0, 0, ${c4}, 1`;
  }, [config.cameraPosition]);
  return {
    width,
    height,
    transformStyle: "preserve-3d",
    position: "relative",
    color: "white",
    transform: `translateZ(${perspectiveValue}px) matrix3d(${matrix3dValue}) translate(${
      width / 2
    }px, ${height / 2}px)`,
  } as React.CSSProperties;
};
