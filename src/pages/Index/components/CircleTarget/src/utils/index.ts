interface Option {
  radius: number;
  total: number;
}

class CircleTool {
  radius = 700;
  total = 7;
  constructor(option: Option) {
    this.radius = option.radius;
    this.total = option.total;
  }

  /**
   * 根据传入的起止索引和初始角度计算出来起点和终点角度
   * @param currentIndex 当前索引
   * @param targetIndex 点击索引
   * @param angle 当前角度
   * @returns
   */
  getRotateAngle = (
    currentIndex: number,
    targetIndex: number,
    angle: number
  ) => {
    const offset = this.calculateOffset(currentIndex, targetIndex);
    const { total } = this;
    const itemAngle = 360 / total;
    const offsetAngle = itemAngle * offset;
    const angleOne = angle;
    const angleTwo = (angle + offsetAngle) % 360;
    // 右侧数据点击需要判断是否小于-180 小于的话就进行角度转换
    // 左测数据点击需要判断开始位置是否大于180度 大于的话驾驶位置进行转换 结束位置必须满足开始和结束都大于180
    if (offset < 0) {
      if (angleTwo <= -180) {
        return [angleOne + 360, angleTwo + 360];
      }
    } else {
      if (angleOne > 180) {
        return [
          ((angleOne + 180) % 360) - 180,
          angleTwo > 180 ? ((angleTwo + 180) % 360) - 180 : angleTwo,
        ];
      }
    }
    return [angleOne, angleTwo];
  };

  /**
   * 计算偏移跨度
   * @param currentIndex 当前索引
   * @param targetIndex 点击索引
   * @param total 总数
   * @returns
   */
  calculateOffset = (currentIndex: number, targetIndex: number) => {
    const { total } = this;
    let offset = targetIndex - currentIndex;
    // 当前索引大于点击索引的话 在判断当前元素是在左侧还是右侧
    if (currentIndex > targetIndex) {
      const absOffset = Math.abs(offset);
      // 左侧的话需要正数 右侧需要负数
      if (absOffset < total / 2) {
        return -offset;
      } else {
        return -(total + offset);
      }
    } else {
      // 当前索引小于点击索引的话 offset是正数 在判断当前元素是在左侧还是右侧
      // 左侧的话需要正数 右侧需要负数
      if (offset > total / 2) {
        return total - offset;
      } else {
        return -offset;
      }
    }
  };

  /**
   * 计算输入角度的xy值
   * @param degree
   * @returns
   */
  getAngleValues = (degree: number) => {
    const { radius } = this;
    const angle = (degree * Math.PI) / 180; // 将度数转换为弧度
    const x = Math.cos(angle) * radius; // 使用三角函数计算 x 坐标
    const y = Math.sin(angle) * radius; // 使用三角函数计算 y 坐标
    return { x, y };
  };

  convertTo360Degrees = (angle: number) => {
    let result = angle;
    if (result < 0) {
      result = 360 + (result % 360);
    } else if (result >= 360) {
      result = result % 360;
    }
    return result;
  };

  /**
   * 计算起始角度扇区内均分xy值
   * @param startAngle 起点角度
   * @param endAngle 终点角度
   * @param n 均分数
   * @returns
   */
  getDivideSector = (startAngle: number, endAngle: number, n = 100) => {
    const angleIncrement = (endAngle - startAngle) / n; // 计算角度增量
    const points: {
      x: number;
      y: number;
    }[] = []; // 存储每一份的坐标点
    for (let i = 0; i < n; i++) {
      const currentAngle = startAngle + i * angleIncrement; // 计算当前角度
      const values = this.getAngleValues(currentAngle);
      points.push(values); // 将当前坐标点添加到数组中
    }
    return points;
  };
}
export default CircleTool;
