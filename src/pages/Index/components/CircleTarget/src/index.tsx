import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useContainerStyle } from "./hooks";
import { motion } from "framer-motion";
import CircleTool from "./utils";
import { useTimeout } from "ahooks";
import { SingleRatioDirectionEnum } from "@/enum";
import config from "./data";
import bg from "@/assets/index/bg3.png";
import BgImage from "@/components/BgImage";
import styles from "./index.module.less";

function CircleTarget() {
  const width = 1920;

  const height = 1080;

  const data = useMemo(() => {
    return [
      {
        name: "安全态势",
      },
      {
        name: "摸家底",
      },
      {
        name: "感威胁",
      },
      {
        name: "快处置",
      },
      {
        name: "稳保障",
      },
    ];
  }, []);

  const circleTool = useMemo(() => {
    return new CircleTool({
      radius: config.basicConfig.radius,
      total: data.length,
    });
  }, [data.length]);

  const [activeIndex, setActiveIndex] = useState(0);

  const [angleInfo, setAngleInfo] = useState<{ start: number; end: number }[]>(
    []
  );

  const [open, setOpen] = useState(false);

  const getTransformValue = useCallback((x: number, y: number) => {
    return `translate(-50%, -50%) matrix3d(1, 0, 0, 0, 0, -1, 0.12, 0, 0, 0, 1, 0, ${y}, 0, ${x}, 1)`;
  }, []);

  // 容器样式
  const containerStyle = useContainerStyle({
    width,
    height,
    config: config.basicConfig,
  });

  const itemStyle = useCallback(
    (index: number) => {
      let degree = index * (360 / data.length);
      // const degree = angleConfig[index];
      const { x, y } = circleTool.getAngleValues(degree);
      return {
        position: "absolute",
        transform: getTransformValue(x, y),
      } as React.CSSProperties;
    },
    [data.length, circleTool, getTransformValue]
  );

  const animate = useCallback(
    (index: number) => {
      const info = angleInfo.at(index);
      if (!open) {
        return undefined;
      }
      const sectorInfo = circleTool.getDivideSector(
        info!.start,
        info!.end,
        100
      );
      return {
        transform: sectorInfo.map((item) => {
          return getTransformValue(item.x, item.y);
        }),
        transition: {
          duration: config.basicConfig.duration,
        },
      };
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [angleInfo, circleTool, config.basicConfig.duration, getTransformValue]
  );

  const onClick = useCallback(
    (index: number) => {
      setAngleInfo((draft) => {
        const arr = draft.map((item) => {
          const [start, end] = circleTool.getRotateAngle(
            activeIndex,
            index,
            item.end
          );
          return {
            start,
            end,
          };
        });
        console.log(arr, "arrarrarr");
        return arr;
      });
      setActiveIndex(index);
      setOpen(true);
    },
    [activeIndex, circleTool]
  );

  const showCarousel = useCallback(() => {
    if (
      config.basicConfig.carousel.direction ===
      SingleRatioDirectionEnum.Clockwise
    ) {
      const newIndex = (activeIndex + 1) % data.length;
      onClick(newIndex);
    } else {
      const newIndex = (activeIndex - 1 + data.length) % data.length;
      onClick(newIndex);
    }
  }, [activeIndex, data.length, onClick]);

  const timeDelay = useMemo(() => {
    if (config.basicConfig.carousel.show) {
      if (open) {
        return undefined;
      }
      return config.basicConfig.carousel.durationInterval * 1000;
    }
    return undefined;
  }, [open]);

  useTimeout(() => {
    showCarousel();
  }, timeDelay);

  useEffect(() => {
    const newData = data.map((_, index) => {
      return {
        start: 0,
        end: (360 / data.length) * index,
      };
    });
    setAngleInfo(newData);
    console.log(newData, "11");
  }, [data]);

  return (
    <React.Fragment>
      <div
        style={{
          perspective: `${height * 1.207}px`,
        }}
      >
        <div style={containerStyle}>
          {[...data].map((item, index) => {
            return (
              <motion.div
                key={index}
                custom={index}
                style={itemStyle(index)}
                onClick={() => onClick(index)}
                animate={animate(index)}
                onAnimationComplete={() => {
                  if (open) {
                    setOpen(false);
                  }
                }}
              >
                <BgImage
                  url={bg}
                  className={styles.item}
                  style={{
                    opacity: activeIndex === index ? 0.96 : 0.8,
                    transform: `scale(${activeIndex === index ? 0.7 : 1})`,
                    marginTop: activeIndex === index ? 10 : 0,
                  }}
                >
                  <div
                    className="text-[32px] text-[#E6F7FF] w-full text-center pt-[40px]"
                    style={{
                      fontFamily: "优设标题黑",
                    }}
                  >
                    {item.name}
                  </div>
                </BgImage>
              </motion.div>
            );
          })}
        </div>
      </div>
    </React.Fragment>
  );
}

export default CircleTarget;
