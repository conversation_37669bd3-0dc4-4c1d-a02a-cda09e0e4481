import { SingleRatioDirectionEnum, ColorTypeEnum } from "@/enum";
import bg from "@/assets/index/bg3.png";

export const COLOR_TEXT_CONFIG = {
  type: ColorTypeEnum.Pure,
  pure: "#fff",
  linear: {
    angle: 0,
    opacity: 1,
    colorStops: [
      { offset: 0, color: "rgba(24,144,255,0.35)" },
      { offset: 100, color: "rgba(24,144,255,1)" },
    ],
  },
};

/**
 * 文本公共配置
 */
export const TEXT_COMMON_CONFIG = {
  fontFamily: "arial",
  fontSize: 24,
  color: {
    ...COLOR_TEXT_CONFIG,
    pure: "#E6F7FF",
  },
  lineHeight: undefined,
  letterSpacing: 0,
  italic: false,
  bold: false,
};

export const fields = [
  {
    name: "name",
    value: "name",
    desc: "名称",
  },
  {
    name: "value",
    value: "value",
    desc: "值",
  },
];

const config = {
  basicConfig: {
    faceCamera: true,
    cameraPosition: {
      x: 0,
      y: 400,
      z: 2000,
    },
    radius: 870,
    duration: 1,
    carousel: {
      show: true,
      hoverSupport: false,
      direction: SingleRatioDirectionEnum.Clockwise,
      durationInterval: 3,
      stayTime: 1,
    },
  },
  itemConfig: {
    default: {
      opacity: 70,
      placard: {
        width: 240,
        height: 188,
        url: bg,
      },
      imageList: [],
      figure: {
        show: false,
        textBasic: {
          ...TEXT_COMMON_CONFIG,
          fontSize: 48,
          color: {
            ...COLOR_TEXT_CONFIG,
            pure: "#58B0FB",
          },
        },
        offsetX: 0,
        offsetY: -20,
        suffix: {
          show: true,
          value: "件",
          textBasic: {
            ...TEXT_COMMON_CONFIG,
            fontSize: 20,
            color: {
              ...COLOR_TEXT_CONFIG,
              pure: "#58B0FB",
            },
          },
          offsetX: 0,
          offsetY: -20,
        },
      },
      name: {
        show: true,
        textBasic: {
          ...TEXT_COMMON_CONFIG,
          fontFamily: "优设标题黑",
          fontSize: 28,
        },
        offsetX: -6,
        offsetY: -84,
      },
    },
    active: {
      opacity: 100,
      zoom: 0.8,
      offsetY: -20,
      placard: {
        width: 240,
        height: 188,
        url: bg,
      },
      imageList: [
        {
          name: "",
          url: "",
          width: 85,
          height: 85,
          offsetX: 0,
          offsetY: 0,
        },
      ],
      figure: {
        show: false,
        textBasic: {
          ...TEXT_COMMON_CONFIG,
          fontSize: 48,
          color: {
            ...COLOR_TEXT_CONFIG,
            pure: "#00EDFF",
          },
        },
        offsetX: 0,
        offsetY: -20,
        suffix: {
          show: true,
          value: "件",
          textBasic: {
            ...TEXT_COMMON_CONFIG,
            fontSize: 20,
            color: {
              ...COLOR_TEXT_CONFIG,
              pure: "#00EDFF",
            },
          },
          offsetX: 0,
          offsetY: -20,
        },
      },
      name: {
        show: true,
        textBasic: {
          ...TEXT_COMMON_CONFIG,
          fontFamily: "优设标题黑",
          fontSize: 32,
        },
        offsetX: 0,
        offsetY: -84,
      },
    },
  },
};

export default config;
