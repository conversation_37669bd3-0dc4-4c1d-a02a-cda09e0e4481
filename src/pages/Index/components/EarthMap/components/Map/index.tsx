import React, { useMemo, useRef, useState } from "react";
import earthMap from "@/assets/0/earthMap.webp";
import emissiveMap from "@/assets/0/emissiveMap.webp";
import earthClouds from "@/assets/0/earthClouds.webp";
import { THREE_EARTH_RADIUS } from "../../config";
import Atmosphere from "../Atmosphere";
import SwitchScene from "../SwitchScene";
import FlyLine from "../FlyLine";
import EndPoint from "./components/EndPoint";
import * as THREE from "three";
import { useFrame } from "@react-three/fiber";

function Map() {
  const radius = THREE_EARTH_RADIUS;

  // const [basicTexture, emissiveTexture, cloudsTexture] = useTexture([
  //   earthMap,
  //   emissiveMap,
  //   earthClouds,
  // ]);

  const [loadingEnd, setLoadingEnd] = useState(false);

  const basicTexture = useMemo(
    () =>
      new THREE.TextureLoader().load(earthMap, () => {
        setLoadingEnd(true);
      }),
    []
  );

  const emissiveTexture = useMemo(
    () => new THREE.TextureLoader().load(emissiveMap),
    []
  );

  const cloudsTexture = useMemo(
    () => new THREE.TextureLoader().load(earthClouds),
    []
  );

  const earthRef = useRef<THREE.Mesh>(null);

  const groupRef = useRef<THREE.Group>(null);

  const speed = -0.3;

  useFrame(() => {
    // z的幅度值 0.005*speed
    if (groupRef.current) {
      const addValue = 0.005 * speed;
      if (speed > 0) {
        return (groupRef.current.rotation.x += addValue);
      } else if (speed < 0) {
        groupRef.current.rotation.y += addValue;
      }
    }
  });

  if (!loadingEnd) {
    return null;
  }

  return (
    <group ref={groupRef}>
      <group>
        {/* 云层 */}
        <mesh>
          <sphereGeometry args={[radius - 1, 64, 64]} />
          <meshPhongMaterial color="#fff" opacity={0.0} transparent />
        </mesh>
        <mesh renderOrder={20}>
          <sphereGeometry args={[radius + 10, 64, 64]} />
          <meshStandardMaterial
            color={new THREE.Color("#fff")}
            opacity={0.4}
            alphaMap={cloudsTexture}
            depthWrite
            transparent
          />
        </mesh>
      </group>

      <Atmosphere />

      <group>
        <mesh ref={earthRef}>
          {/* 使用球体几何体创建地球，参数分别为半径、水平和垂直的分段数 */}
          <sphereGeometry args={[radius, 64, 64]} />
          <meshStandardMaterial
            map={basicTexture}
            emissiveMap={emissiveTexture}
            emissiveIntensity={4}
            depthTest
            depthWrite
            emissive={new THREE.Color("#fff")}
          />
        </mesh>
      </group>

      <group>
        <FlyLine meshRef={earthRef} />
        <EndPoint earthRef={earthRef} />
      </group>

      <SwitchScene />
    </group>
  );
}
export default Map;
