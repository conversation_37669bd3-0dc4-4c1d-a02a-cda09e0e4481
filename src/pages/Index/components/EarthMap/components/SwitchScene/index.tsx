import { useThree } from "@react-three/fiber";
import { useDeepCompareEffect } from "ahooks";
import type { OrbitControls } from "three-stdlib";
import gsap from "gsap";
import { useState } from "react";

function SwicthScene() {
  const camera = useThree((state) => state.camera);

  const controls = useThree((state) => state.controls as OrbitControls);

  const [animated, setAnimated] = useState(false);

  // 初始化场景
  useDeepCompareEffect(() => {
    // 是否执行过动画
    if (animated === false && camera && controls) {
      const firstView = {
        duration: 1,
        viewId: "viewId",
        camera: {
          position: { x: -168, y: 408, z: -480 },
          target: { x: 0, y: 0, z: 0 },
        },
      };
      const {
        camera: {
          position: { x, y, z },
          target: { x: targetX, y: targetY, z: targetZ },
        },
        duration,
      } = firstView;
      gsap.fromTo(
        controls.target,
        {
          x: 0,
          y: 0,
          z: 0,
          duration: 0,
        },
        {
          x: targetX,
          y: targetY,
          z: targetZ,
          duration,
          ease: "linear",
        }
      );
      gsap.fromTo(
        camera.position,
        {
          x: x, // 初始化整体缩小2倍 呈现从小到大的效果
          y: y * 2,
          z: z,
          duration: 0,
        },
        {
          x,
          y,
          z,
          duration,
          ease: "linear",
          onComplete: () => {
            setAnimated(true);
          },
        }
      );
    }
    return () => {};
  }, [controls]);

  return null;
}
export default SwicthScene;
