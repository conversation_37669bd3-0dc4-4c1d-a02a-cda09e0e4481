import BgImage from "@/components/BgImage";
import bg from "@/assets/index/bg.webp";
import titleBg from "@/assets/index/titleBg.png";
import ReactScaleScreen from "@/components/ReactScaleScreen";
import Title from "@/components/Title";
import circleBg from "@/assets/index/bg5.webp";
import MenuList from "./components/MenuList";
import useLogin from "@/hooks/useLogin";
import EarthMap from "./components/EarthMap";

function IndexPage() {
  useLogin();

  return (
    <div className="w-sreen h-screen relative">
      <BgImage
        className="absolute inset-x-0 inset-y-0 pointer-events-none"
        url={bg}
      ></BgImage>
      <div
        className="absolute inset-x-0 inset-y-0"
        style={{
          background: `linear-gradient(180deg,rgba(6,24,54,.25) 0%,rgba(6,24,54,.45) 100%)`,
        }}
      ></div>
      <ReactScaleScreen>
        <div className="absolute z-[1000] w-[1920px] h-[1080px]">
          <BgImage url={titleBg} className="w-[1920px] h-[134px]">
            <Title className="text-[48px] flex justify-center items-center pt-[20px]">
              广东省网络安全态势感知平台
            </Title>
          </BgImage>
          <EarthMap />
          <BgImage
            url={circleBg}
            className="w-[1478px] h-[366px] x-centered top-[400px] pointer-events-none"
          />
          <MenuList />
        </div>
      </ReactScaleScreen>
    </div>
  );
}
export default IndexPage;
