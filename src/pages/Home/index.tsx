import Statistics from "./components/Statistics";
import SecurityTrend from "./components/SecurityTrend";
import TrafficTrend from "./components/TrafficTrend";
import AttackDistribution from "./components/AttackDistribution";
import ThreatType from "./components/ThreatType";
import Center from "./components/Center";
import React from "react";

function Home1() {
  return (
    <React.Fragment>
      <Statistics />
      {/* 安全趋势分析 */}
      <SecurityTrend />
      {/* 全网流量走势 */}
      <TrafficTrend />
      {/* 攻击源分布 */}
      <AttackDistribution />
      {/* 威胁类型 */}
      <ThreatType />
      <Center />
    </React.Fragment>
  );
}
export default Home1;
