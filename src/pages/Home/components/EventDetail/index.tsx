import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import type { ProColumns } from "@ant-design/pro-table";
import situationService from "@/service/situationService";
import { useTime } from "@/store/useTime";
import type { GetProp } from "antd";
import { useMemo, useState } from "react";

export interface DefaultValueParams {
  /**
   * 攻击IP所属国家
   */
  srcCountry?: string;

  /**
   * 攻击IP所属省
   */
  srcProvince?: string;

  /**
   * 受害所属城市
   */
  dstCity?: string;
}
interface IProps extends CustomModalProps {
  /**
   * 默认筛选条件
   */
  defaultValue?: DefaultValueParams;
}
function EventDetail(props: IProps) {
  const { open, onCancel, defaultValue } = props;

  const columns = useMemo<ProColumns[]>(() => {
    return [
      {
        dataIndex: "dataIndex",
        title: "序号",
        align: "center",
      },
      {
        dataIndex: "eventTime",
        title: "事件时间",
        width: 100,
      },
      {
        dataIndex: "eventType",
        title: "事件类型",
        width: 80,
      },
      {
        dataIndex: "dstIp",
        title: "受害IP",
        width: 100,
      },
      {
        dataIndex: "dstPort",
        title: "受害端口",
        width: 100,
      },
      {
        dataIndex: "unitName",
        title: "受害单位名称",
        width: 120,
      },
      {
        dataIndex: "city",
        title: "受害所属城市",
        width: 140,
      },
      {
        dataIndex: "industryName",
        title: "受害行业名称",
        width: 140,
      },
      {
        dataIndex: "isCritical",
        title: "受害是否关基",
        valueType: "select",
        width: 120,
        valueEnum: {
          0: "否",
          1: "是",
        },
      },
      {
        dataIndex: "srcIp",
        title: "攻击IP",
        width: 100,
      },
      {
        dataIndex: "srcPort",
        title: "攻击端口",
        width: 100,
      },
      {
        dataIndex: "srcCountry",
        title: "攻击所属国家",
        width: 120,
      },
      {
        dataIndex: "srcProvince",
        title: "攻击所属省",
        width: 130,
      },
      {
        dataIndex: "attackCount",
        title: "告警数量",
        width: 100,
      },
    ];
  }, []);

  const searchColumns = useMemo<ProColumns[]>(() => {
    return [
      {
        dataIndex: "eventType",
        key: "eventType",
        title: "威胁类型",
        fieldProps: {
          placeholder: "请输入威胁类型",
        },
      },
      {
        dataIndex: "srcCountry",
        key: "srcCountry",
        title: "攻击IP所属国家",
        fieldProps: {
          placeholder: "请输入攻击IP所属国家",
        },
      },
      {
        dataIndex: "srcProvince",
        key: "srcProvince",
        title: "攻击IP所属省",
        fieldProps: {
          placeholder: "请输入攻击IP所属省",
        },
      },
      {
        dataIndex: "unitName",
        key: "unitName",
        title: "受害单位名称",
        fieldProps: {
          placeholder: "请输入受害单位名称",
        },
      },
      {
        dataIndex: "industryName",
        key: "industryName",
        title: "受害行业名称",
        fieldProps: {
          placeholder: "请输入受害行业名称",
        },
      },
      {
        dataIndex: "dstCity",
        key: "dstCity",
        title: "受害所属城市",
        fieldProps: {
          placeholder: "请输入受害所属城市",
        },
      },
    ];
  }, []);

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: Omit<
      API.Situation.SecurityPostureEventListParams,
      "startTime" | "endTime"
    >
  ) => {
    const res = await situationService.securityPostureEventList({
      startTime,
      endTime,
      ...params,
    });
    return {
      data: res.securityPostureEventDtoList,
      total: res.total,
    };
  };

  const [collapsed, setCollapsed] = useState(false);

  return (
    <ProModal type={2} title="受攻击分布" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        searchColumns={searchColumns}
        form={{
          initialValues: defaultValue,
          labelWidth: 120,
        }}
        search={{
          collapsed,
          onCollapse: (e) => {
            setCollapsed(e);
          },
        }}
        scroll={{
          y: collapsed ? 460 :340,
          x: "max-content",
        }}
      />
    </ProModal>
  );
}

export default EventDetail;
