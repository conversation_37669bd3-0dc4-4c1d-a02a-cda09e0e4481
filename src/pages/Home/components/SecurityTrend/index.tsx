import Card from "@/components/Card";
import useVisible from "@/hooks/useVisible";
import type { Coloumns } from "@/components/BasicLine";
import BasicLine from "@/components/BasicLine";
import { useMemo } from "react";
import ProModal from "@/components/Modal";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import situationService from "@/service/situationService";
import dayjs from "dayjs";
// import MoreComp from "./components/MoreComp";

function SecurityTrend() {
  const [open, { setFalse, setTrue }] = useVisible(false);

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      situationService.securePosture({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () =>
      data?.securePostureDtoList?.map((val) => ({
        ...val,
        name: dayjs(val.time).format("YYYYMMDD"),
      })) ?? [],
    [data?.securePostureDtoList]
  );

  const coloumns = useMemo<Coloumns>(() => {
    return [
      {
        dataKey: "secureScore",
        color: "#F3B67B",
        name: "安全指数",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(243, 182, 123, 0.4)",
            },
            {
              offset: 1,
              color: "rgba(243, 182, 123, 0)",
            },
          ],
        },
      },
    ];
  }, []);

  return (
    <div className="absolute left-[40px] top-[436px]">
      <ProModal type={3} title="安全趋势分析" open={open} onCancel={setFalse}>
        <div className="p-2 mt-4">
          <BasicLine
            data={list}
            xDataKey="name"
            width={1300}
            height={400}
            yAxisWidth={40}
            coloumns={coloumns}
          />
        </div>
      </ProModal>
      <Card
        headerType={1}
        title="安全趋势分析"
        onMore={setTrue}
        loading={loading}
      >
        <BasicLine
          xDataKey="name"
          data={list}
          width={450}
          height={240}
          coloumns={coloumns}
        />
      </Card>
    </div>
  );
}
export default SecurityTrend;
