import iconBg4 from "@/assets/1/bg4.png";
import iconBg5 from "@/assets/1/bg5.png";
import icon1 from "@/assets/0/1.png";
import icon2 from "@/assets/1/3.png";
import icon3 from "@/assets/0/2.png";
import bg1 from "@/assets/0/bg1.png";
import bg2 from "@/assets/0/bg2.png";
import popBg from "@/assets/0/popBg.webp";
import React, { useMemo } from "react";
import lodash from "lodash-es";
import BgImage from "@/components/BgImage";
import { useRequest } from "ahooks";
import synergyService from "@/service/synergyService";
import { useTime } from "@/store/useTime";
import situationService from "@/service/situationService";
import { Popover } from "antd";

function Statistics() {
  const { startTime, endTime } = useTime();

  const { data } = useRequest(
    () =>
      synergyService.criticalOverview({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const { data: scoreData } = useRequest(
    () =>
      situationService.secureScore({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  // 安全评分值
  const scoreValue = useMemo(
    () => Number(scoreData?.secureScore ?? 0),
    [scoreData?.secureScore]
  );

  const angle = useMemo(() => {
    const startAngle = -128;

    const endAngle = 128;

    const angleRange = endAngle - startAngle;

    const percentage = scoreValue / 100;

    const angle = startAngle + percentage * angleRange;

    return angle;
  }, [scoreValue]);

  const getValue = (value: number = 0) => {
    if (value > 10000) {
      return {
        value: lodash.round(value / 10000, 1),
        unit: "万",
      };
    }
    return {
      value,
      unit: undefined,
    };
  };

  const columns = [
    {
      label: "全网攻击",
      value: data?.allAttackNum,
      unit: "万",
      icon: icon1,
      id: "1",
    },
    {
      label: "全网事件",
      value: data?.allEventNum,
      unit: "万",
      icon: icon2,
      id: "2",
    },
  ];

  const ratingList = [
    {
      name: "有害程序事件",
      value1: [
        "计算机病毒事件",
        "特洛伊木马事件",
        "混合攻击程序事件",
        "其它有害程序事件",
      ],
      value2: ["蠕虫事件", "僵尸网络事件", "网页内嵌恶意代码事件"],
    },
    {
      name: "网络攻击事件",
      value1: [
        "拒绝服务攻击事件",
        "漏洞攻击事件",
        "网络钓鱼事件",
        "其他网络攻击事件",
      ],
      value2: ["后门攻击事件", "网络扫描窃听事件", "干扰事件"],
    },
    {
      name: "信息破坏事件",
      value1: ["信息篡改事件", "信息泄漏事件", "信息丢失事件"],
      value2: ["信息假冒事件", "信息窃取事件", "其它信息破坏事件"],
    },
    {
      name: "信息内容安全事件",
      value1: ["违法乱纪内容安全事件", "串联集会内容安全事件"],
      value2: ["舆论煽动内容安全事件", "其他信息内容安全事件"],
    },
    {
      name: "设备设施故障",
      value1: ["软硬件自身故障事件", "人为破坏事故事件"],
      value2: ["外围保障设施故障事件", "其他设备设施故障事件"],
    },
    {
      name: "灾害性事件",
      value1: ["灾害性事件"],
      value2: [],
    },
    {
      name: "其他事件",
      value1: ["安全隐患事件", "高级威胁事件"],
      value2: ["行为否认事件", "可疑活动事件"],
    },
  ];

  return (
    <React.Fragment>
      <div className="absolute left-[52px] top-[160px] flex z-10">
        <BgImage url={bg1} className="size-[234px] relative">
          <div className="centered">
            <Popover
              content={
                <BgImage
                  url={popBg}
                  className="w-[680px] ml-[-10px] mt-[-20px] py-4 pl-[26px] pr-3 text-[16px]"
                >
                  <div className="border border-[rgba(19,78,148,.65)] color-text">
                    <div className="flex items-center h-[54px] bg-[RGBA(19,69,127,.65)] font-bold text-[18px]">
                      <div className="w-[200px] flex items-center justify-center border-r border-[rgba(19,78,148,.65)] h-full">
                        七大类
                      </div>
                      <div className="flex items-center px-7 flex-1 h-full">
                        33小类
                      </div>
                    </div>
                    {ratingList.map((val, index) => (
                      <div
                        className="flex border-t border-[rgba(19,78,148,.65)]"
                        key={`desc${index + 1}`}
                      >
                        <div className="w-[200px] flex items-center justify-center border-r border-[rgba(19,78,148,.65)]">
                          {val.name}
                        </div>
                        <div className="grid grid-cols-2 flex-1 px-7 h-full leading-[30px] py-1">
                          <div>
                            {val.value1.map((item) => (
                              <div key={item}>{item}</div>
                            ))}
                          </div>

                          <div>
                            {val.value2.map((item) => (
                              <div key={item}>{item}</div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </BgImage>
              }
              trigger="hover"
              placement="rightTop"
            >
              <BgImage
                url={bg2}
                className="w-[230px] h-[186px] relative mt-[-10px] flex justify-center cursor-pointer"
              >
                <div className="color-secondary text-[18px] x-centered bottom-[10px]">
                  安全评分
                </div>
                <div className="text-[#E5C16B] text-[32px] font-[DINCond-Bold] x-centered top-[66px]">
                  {scoreValue}
                </div>
                <BgImage
                  url={icon3}
                  className="w-[54px] h-[94px] origin-[center_70%] absolute bottom-[44px]"
                  style={{
                    transform: `rotate(${angle}deg)`,
                    transition: "transform 0.5s",
                  }}
                />
              </BgImage>
            </Popover>
          </div>
        </BgImage>
        <div className="ml-[10px] flex flex-col gap-y-[22px]">
          {columns.map((val) => (
            <div className="flex" key={val.id}>
              <div
                className="w-[106px] h-[106px] flex items-center justify-center"
                style={{
                  backgroundImage: `url(${iconBg4})`,
                }}
              >
                <img src={val.icon} alt="" className="w-[94px] -mt-2" />
              </div>
              <div
                className="w-[114px] h-[106px] text-center"
                style={{
                  backgroundImage: `url(${iconBg5})`,
                }}
              >
                <div className="color-secondary leading-[40px] text-[18px]">
                  {val.label}
                </div>
                <div className="leading-[56px] text-[#E5C16B] text-[32px] font-[DINCond-Bold]">
                  {getValue(val.value).value}
                  {getValue(val.value).unit}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </React.Fragment>
  );
}
export default Statistics;
