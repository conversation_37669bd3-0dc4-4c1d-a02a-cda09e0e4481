import Card from "@/components/Card";
import useVisible from "@/hooks/useVisible";
import type { Coloumns } from "@/components/BasicLine";
import BasicLine from "@/components/BasicLine";
import { useMemo } from "react";
import ProModal from "@/components/Modal";
import { useRequest } from "ahooks";
import situationService from "@/service/situationService";
import { useTime } from "@/store/useTime";
import dayjs from "dayjs";
// import MoreComp from "./components/MoreComp";

function TrafficTrend() {
  const [open, { setFalse, setTrue }] = useVisible(false);

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      situationService.trafficPosture({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () =>
      data?.networkTrafficTrendDtoList?.map((val) => ({
        ...val,
        dateTime: dayjs(val.dateTime).format("YYYYMMDD"),
      })) ?? [],
    [data?.networkTrafficTrendDtoList]
  );

  const coloumns = useMemo<Coloumns>(() => {
    return [
      {
        dataKey: "networkTraffic",
        color: "#F3B67B",
        name: "全网流量",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(243, 182, 123, 0.4)",
            },
            {
              offset: 1,
              color: "rgba(243, 182, 123, 0)",
            },
          ],
        },
      },
      {
        dataKey: "domainTraffic",
        name: "域名流量",
        color: "rgba(43, 128, 255, 1)",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(43, 128, 255, .4)",
            },
            {
              offset: 1,
              color: "rgba(43, 128, 255, 0)",
            },
          ],
        },
      },
      {
        dataKey: "basicTraffic",
        name: "关基流量",
        color: "rgba(4, 205, 244, 1)",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(4, 205, 244, .4)",
            },
            {
              offset: 1,
              color: "rgba(4, 205, 244, 0)",
            },
          ],
        },
      },
    ];
  }, []);

  return (
    <div className="absolute left-[40px] top-[744px] h-[900px] w-[600px]">
      <ProModal type={3} title="安全趋势分析" open={open} onCancel={setFalse}>
        <div className="p-2 mt-5">
          <BasicLine
            data={list}
            xDataKey="dateTime"
            width={1300}
            height={400}
            yAxisWidth={90}
            coloumns={coloumns}
          />
        </div>
      </ProModal>
      <Card
        headerType={1}
        title="全网流量走势"
        onMore={setTrue}
        loading={loading}
      >
        <BasicLine
          xDataKey="dateTime"
          data={list}
          width={450}
          height={240}
          yAxisWidth={40}
          coloumns={coloumns}
        />
      </Card>
    </div>
  );
}
export default TrafficTrend;
