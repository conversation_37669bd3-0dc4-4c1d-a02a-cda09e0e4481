import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import situationService from "@/service/situationService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

interface IProps extends CustomModalProps {
  dataType: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, dataType } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
    },
    {
      dataIndex: "region",
      title: "国家/地区",
    },
    {
      dataIndex: "eventCount",
      title: "事件数量",
    },
    {
      dataIndex: "ratio",
      title: "环比",
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await situationService.attackSourceList({
      startTime,
      endTime,
      dataType,
      ...params,
    });
    return {
      data: res.attackSourceList,
      total: res.total,
    };
  };

  return (
    <ProModal type={2} title="攻击源分布" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
