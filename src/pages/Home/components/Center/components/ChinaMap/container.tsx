import { createContainer } from "unstated-next";
import chinaJson from "./100000.json";
import { ProjectionTypeEnum } from "@/enum";
import useMapData from "./hooks/useMapData";
import useShape from "./hooks/useShape";
import mapBgUrl from "@/assets/0/chinaTexture.webp";
import type * as THREE from "three";
import { useMemo, useRef } from "react";
import { useRequest } from "ahooks";
import situationService from "@/service/situationService";
import { useTime } from "@/store/useTime";

export interface MapConfig {
  /**
   * 层级-地图的拉长长度
   */
  level?: number;

  /**
   * 墨卡托投影转换
   */
  geoProjection: (args: [number, number]) => [number, number];

  /**
   * html缩放比例
   */
  htmlScale: number;

  /**
   * 获取省份中心点
   */
  getProvinceCenter: (adcode: number, name?: string) => number[] | undefined;
}

function useContainer() {
  const projectionType = ProjectionTypeEnum.Mercator;

  const depth = 6;

  const subGroupRef = useRef<THREE.Group>(null);

  const {
    featureData,
    mapPosition,
    handleCalcUv2,
    geoProjection,
    getProvinceCenter,
  } = useMapData({
    projectionType,
    dataJson: JSON.stringify(chinaJson),
    depth,
  });

  const { meshMaterial, sideMaterial } = useShape(mapBgUrl);

  const { startTime, endTime } = useTime();
  const { data, loading } = useRequest(
    () => situationService.attackChinaMap({ startTime, endTime }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const chindData = useMemo(() => {
    return (
      data?.attackChinaMapDtoList.map((val) => {
        const [startLng, startLat] = getProvinceCenter(
          val.srcCode,
          val.srcName
        ) ?? [0, 0];
        return {
          startLng,
          startLat,
          endLng: 113.280637,
          endLat: 23.125178,
          ...val,
        };
      }) ?? []
    );
  }, [data?.attackChinaMapDtoList, getProvinceCenter]);

  return {
    featureData,
    mapPosition,
    meshMaterial,
    handleCalcUv2,
    sideMaterial,
    depth,
    subGroupRef,
    geoProjection,
    getProvinceCenter,
    chindData,
    loading,
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
