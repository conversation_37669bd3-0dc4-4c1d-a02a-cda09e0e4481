import React from "react";
import RenderRunwayLine from "./components/RenderRunwayLine";
import useCoordinateData from "./hooks/useCoordinateData";
import RenderFlowLine from "./components/RenderFlowLine";
import Container from "../../../../container";

function FlyLine() {
  const { chindData } = Container.useContainer();

  const { coordinateData } = useCoordinateData({
    data: chindData,
  });

  return (
    <group>
      {coordinateData?.map((item, index) => {
        return (
          <React.Fragment key={index}>
            <RenderRunwayLine item={item} />
            <RenderFlowLine item={item} />
          </React.Fragment>
        );
      })}
    </group>
  );
}
export default FlyLine;
