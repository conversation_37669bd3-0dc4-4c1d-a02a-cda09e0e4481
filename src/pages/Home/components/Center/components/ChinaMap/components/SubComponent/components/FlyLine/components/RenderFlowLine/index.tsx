import React, { useEffect, useMemo, useRef } from "react";

import {
  BufferGeometry,
  Color,
  Float32BufferAttribute,
  ShaderMaterial,
} from "three";
import type * as THREE from "three";
import gsap from "gsap";
import type { CoordinateItem } from "@/typings";
import useCommon from "../../hooks/useCommon";

interface IProps {
  item: CoordinateItem;
}

function RenderFlowLine(props: IProps) {
  const { item } = props;

  const styleConfig = useMemo(() => {
    return {
      flowLine: {
        color: {
          linear: {
            angle: 0,
            colorStops: [
              { color: "rgba(246,103,8,0.12)", offset: 0 },
              { color: "rgba(255,255,229,1)", offset: 100 },
            ],
            opacity: 1,
          },
          type: "pure",
          pure: "#FFFFFF",
        },
        // 飞线宽度
        width: 34,
        // 飞线长度
        length: 160,
        // 飞线点数量
        divisions: 1000,
      },
      blending: true,
      runwayLine: { color: "rgba(197,130,36,0.3)", show: true },
      transparent: true,
      height: 45,
    };
  }, []);

  const { flowLine } = styleConfig;

  const { getCurvePoint } = useCommon({ divisions: flowLine.divisions });

  const pointsRef = useRef<THREE.Points>(null);

  const animationConfig = { duration: 3, random: false, interval: 0 };

  // 获取当前线的坐标点信息
  const points = useMemo(() => {
    return getCurvePoint(item);
  }, [getCurvePoint, item]);

  // 创建物体
  const geometry = useMemo(() => {
    const indexList = points.map((_, index) => index);
    // 根据坐标点数组创建出一个线状几何体
    const bufferGeometry = new BufferGeometry().setFromPoints(points);
    // 给几何体添加自定义的索引标识 用来后续根据索引设置点的透明度
    bufferGeometry.setAttribute(
      "aIndex",
      new Float32BufferAttribute(indexList, 1)
    );
    return bufferGeometry;
  }, [points]);

  const material = useMemo(() => {
    // 起点颜色
    let color1 = "#ededed";
    return new ShaderMaterial({
      depthTest: false,
      uniforms: {
        // 线条颜色
        uColor: {
          value: new Color(color1),
        },
        // 时间1-1000
        uTime: {
          value: 0,
        },
        // 水滴宽度
        uWidth: {
          value: flowLine.width,
        },
        // 水滴长度
        uLength: {
          value: flowLine.length,
        },
      },
      vertexShader: /*glsl*/ `
        attribute float aIndex; // 内部属性 浮点 当前序号

        uniform float uTime; // 全局变量 浮点 当前时间

        uniform float uWidth; // 全局变量 浮点 当前时间
        
        uniform vec3 uColor; // 全局变量 颜色 设置的颜色

        varying float vSize; // 片元变量（需要传递到片面着色器） 浮点 尺寸

        uniform float uLength; // 全局变量 浮点 线段长度

        void main(){
            vec4 viewPosition = viewMatrix * modelMatrix * vec4(position,1);

            gl_Position = projectionMatrix * viewPosition; // 顶点矩阵变换 设置各个点的位置

            // 当前顶点的位置处于线段长度内 则设置水滴大小
            if(aIndex >= uTime - uLength && aIndex < uTime){
              // 水滴大小根据当前位置慢慢变小
              // p1 uWidth越大水滴越粗
              // vSize = uWidth * ((aIndex - uTime + uLength) / uLength);
              // p2 uWidth越大水滴越细
              vSize = (aIndex + uLength - uTime) / uWidth;
            }
            gl_PointSize = vSize;
        }
      `,
      fragmentShader: /*glsl*/ `
        varying float vSize;
        uniform vec3 uColor;
        void main(){
            // 透明度根据当前大小确定是否展示
            if(vSize<=0.0){
              gl_FragColor = vec4(1,0,0,0);
            }else{
              gl_FragColor = vec4(uColor,1);
            }
        }
      `,
      transparent: true,
      vertexColors: false,
    });
  }, [flowLine.length, flowLine.width]);

  useEffect(() => {
    if (pointsRef.current) {
      gsap.fromTo(
        (pointsRef?.current?.material as any)?.uniforms?.uTime,
        { value: 0 },
        {
          // 实现飞线钻地效果需要让 动画节段数 = 飞线长度 + 飞线点数量
          value: flowLine.length + flowLine.divisions,
          duration: animationConfig.duration,
          repeat: -1,
          delay: animationConfig.interval,
          ease: "none",
          onUpdate: () => {},
        }
      );
    }
  }, [
    animationConfig.duration,
    animationConfig.interval,
    flowLine.divisions,
    flowLine.length,
    styleConfig,
  ]);

  return (
    <React.Fragment>
      <points
        renderOrder={1}
        ref={pointsRef}
        args={[geometry, material]}
      ></points>
    </React.Fragment>
  );
}

export default RenderFlowLine;
