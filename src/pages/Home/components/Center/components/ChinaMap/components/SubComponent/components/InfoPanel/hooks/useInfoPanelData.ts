import { useMemo } from "react";
import Container from "../../../../../container";
import type { DataItemConfig } from "..";

export interface IProps {
  data: DataItemConfig[];
}

function useInfoPanelData(props: IProps) {
  const { data } = props;
  const { geoProjection } = Container.useContainer();

  // 信息面板数据
  const infoPanelData = useMemo(() => {
    // 未配置地图
    if (!geoProjection) return;
    const source = data.map((item) => {
      // 经纬度转平面坐标
      const [x, y] = geoProjection([item.startLng, item.startLat]);
      return {
        ...item,
        x,
        y: -y,
      };
    });
    return source;
  }, [data, geoProjection]);

  return {
    infoPanelData,
  };
}

export default useInfoPanelData;
