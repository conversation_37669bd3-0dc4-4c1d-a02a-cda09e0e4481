import { useMemo } from "react";
import Container from "../../../../../container";
import { get } from "lodash-es";

interface IProps {
  data: {
    startLng: number;
    startLat: number;
    endLng: number;
    endLat: number;
  }[];
}
function useCoordinateData(props: IProps) {
  const { data } = props;

  const { geoProjection } = Container.useContainer();

  const coordinateData = useMemo(() => {
    // 未配置地图
    if (!geoProjection) return;
    const source = data.map((item) => {
      // 经纬度转平面坐标

      const startLng = get(item, "startLng");
      const startLat = get(item, "startLat");
      const endLng = get(item, "endLng");
      const endLat = get(item, "endLat");

      const [x0, y0] = geoProjection([startLng, startLat]);
      const [x1, y1] = geoProjection([endLng, endLat]);
      return {
        x0,
        y0: -y0, // y轴反转
        x1,
        y1: -y1, // y轴反转
      };
    });
    return source;
  }, [data, geoProjection]);

  return {
    coordinateData,
  };
}

export default useCoordinateData;
