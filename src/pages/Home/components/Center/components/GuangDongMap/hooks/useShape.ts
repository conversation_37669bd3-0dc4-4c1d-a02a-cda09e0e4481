import { useMemo } from "react";
import * as THREE from "three";

interface IProps {
  mapBgUrl: string;

  mapBgMargin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}
function useShape(props: IProps) {
  // 顶面颜色贴图
  const { mapBgUrl, mapBgMargin } = props;

  const colorMap = useMemo(() => {
    const texture = new THREE.TextureLoader().load(mapBgUrl);
    // 设置纹理偏移
    // 计算纹理在水平方向上的起始偏移，考虑到左侧边距
    // 计算纹理在垂直方向上的起始偏移，考虑到底部边距
    // 在 Three.js 的纹理坐标系中，纹理的 v 轴（对应于 y 轴）是从上到下的
    const { top, left, right, bottom } = mapBgMargin;
    texture.offset.set(
      -left / (1 + left + right),
      -bottom / (1 + top + bottom)
    );

    // 设置纹理重复
    // 计算纹理在水平方向上的重复次数，考虑到左右边距
    // 计算纹理在垂直方向上的重复次数，考虑到上下边距
    texture.repeat.set(1 + left + right, 1 + top + bottom);
    texture.wrapS = THREE.RepeatWrapping;
    texture.wrapT = THREE.RepeatWrapping;
    // 152版本后通过设置colorSpace来解决颜色变亮的问题
    // https://discourse.threejs.org/t/exr-color-space-it-is-too-bright-now-especially-with-152/53134
    texture.colorSpace = THREE.SRGBColorSpace;
    return texture;
  }, [mapBgMargin, mapBgUrl]);

  // 侧面的材质
  const sideMaterial = useMemo(() => {
    const topColor = "#8DC7EE";

    const topOpacity = 1;

    const bottomColor = "#254E91";

    const bottomOpacity = 1;

    return new THREE.ShaderMaterial({
      side: THREE.DoubleSide,
      uniforms: {
        uTopColor: { value: new THREE.Color(topColor) },
        uBottomColor: { value: new THREE.Color(bottomColor) },
        uTopAlpha: { value: topOpacity },
        uBottomAlpha: { value: bottomOpacity },
      },
      vertexShader: `
        varying vec3 vPosition;
        varying vec2 vUv; // 添加 UV 坐标的 varying 变量
        void main() {
          vPosition = position;
          vUv = uv; // 传递 UV 坐标
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        varying vec3 vPosition;
        uniform vec3 uTopColor;
        uniform vec3 uBottomColor;
        uniform float uTopAlpha;
        uniform float uBottomAlpha;
        varying vec2 vUv; // 接收 UV 坐标 
  
        void main() {
          float computedAlpha = mix(uBottomAlpha, uTopAlpha, vUv.y);
          vec3 gradient = mix(uBottomColor, uTopColor, vUv.y);
          vec3 outgoingLight = gradient; // 注意,这里我们没有外来的光线变量,所以我用gradient作为outgoingLight
          gl_FragColor = vec4(outgoingLight, computedAlpha);
        }
      `,
      depthTest: true,
    });
  }, []);

  // 多边形的材质 地图shape的材质
  const meshMaterial = useMemo(() => {
    const basic = new THREE.MeshStandardMaterial({
      flatShading: true, // 平滑着色
      side: THREE.DoubleSide,
      color: new THREE.Color("#FFFFFF"),
      map: colorMap,
      opacity: 1, // 支持透明度的时候处理颜色的透明度
      transparent: true,
      depthTest: true, // 控制是否对一个物体执行深度测试来确定其是否被其他物体遮挡。
    });

    return basic;
  }, [colorMap]);

  return {
    meshMaterial,
    sideMaterial,
  };
}
export default useShape;
