import { Html } from "@react-three/drei";
import * as THREE from "three";
import { memo, useMemo } from "react";
import icon1 from "@/assets/0/icon1.png";
import icon2 from "@/assets/0/icon2.png";
import icon3 from "@/assets/0/icon3.png";
import icon4 from "@/assets/0/icon4.png";
import { MapLevelEnum } from "@/enum";
import Container from "../../../../container";

interface IProps {
  x: number; // x坐标
  y: number; // y坐标
  name: string; // 区域名称
  adcode: number; // 区域编码
  index: number; // 当前所在列表索引
}
function RenderName(props: IProps) {
  const { x, y, name, adcode, index } = props;

  const { depth, guangDongMapData } = Container.useContainer();

  // 计算标签的层级，基于模型的深度和基础标签层级
  const labelLevel = useMemo(() => {
    return MapLevelEnum.BASIC_LABEL_LEVEL + depth;
  }, [depth]);

  const htmlScale = useMemo(() => 7.6, []);

  const position = useMemo(() => {
    return new THREE.Vector3(x, -y, labelLevel);
  }, [x, y, labelLevel]);

  const value = useMemo(
    () => guangDongMapData.get(adcode)?.attackCount ?? 0,
    [adcode, guangDongMapData]
  );

  const iconUrl = useMemo(() => {
    if (value >= 4500) {
      return icon4;
    } else if (value >= 3000) {
      return icon3;
    } else if (value > 1500) {
      return icon2;
    } else {
      return icon1;
    }
  }, [value]);

  return (
    <group>
      <Html
        key={adcode ?? index}
        name="nameHtml"
        sprite // 将 Html 组件设置为精灵模式。当 sprite 为 true 时，Html 内容将始终面向摄像机，类似于 3D 场景中的精灵。
        transform // 启用三维变换
        pointerEvents="none" // 禁用指针事件
        scale={[htmlScale, htmlScale, htmlScale]} // 设置标签的缩放比例，为了确保标签在三维场景中的大小适中
        position={position} // 设置标签的位置
      >
        <div className="flex flex-col items-center mt-[-30px]">
          <div className="bg-[rgb(16,39,74)] border border-white px-[10px] h-[30px] flex items-center">
            {name}
          </div>
          <img src={iconUrl} className="w-[40px] h-[40px] relative mt-1" />
          {/* <span className="ml-[-8px]">{name}</span> */}
        </div>
      </Html>
    </group>
  );
}
export default memo(RenderName);
