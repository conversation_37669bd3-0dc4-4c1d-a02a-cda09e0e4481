import * as THREE from "three";
import { Canvas } from "@react-three/fiber";
import ScaleCanvas from "@/components/Map/ScaleCanvas";
import {
  OrbitControls,
  PerspectiveCamera,
  useProgress,
  // Stats,
} from "@react-three/drei";
import BasicMap from "./components/BasicMap";
import Container from "./container";
import { useMemo } from "react";
import React from "react";
import { Spin } from "antd";
import BgImage from "@/components/BgImage";
import bg from "@/assets/0/guangdongBg.jpg";
import Title from "@/components/Title";

function GuangDongMap() {
  const width = 1920;
  const height = 1080;

  const progress = useProgress();

  const spinning = useMemo(() => progress.progress < 100, [progress.progress]);

  return (
    <React.Fragment>
      <BgImage
        url={bg}
        className="absolute centered w-[2060px] h-[1080px] pointer-events-none"
      />
      <div className="absolute">
        <Canvas
          gl={{
            //  设备像素比 不同硬件设备的屏幕window.devicePixelRatio的值可能不同 设置是为了适应不同的硬件设备屏幕 min是为了防止设备像素比过大导致渲染器内存溢出
            pixelRatio: Math.min(window.devicePixelRatio, 2),
            // 色调映射
            toneMapping: THREE.NoToneMapping,
          }}
          className="absolute x-centered ml-8 top-[30px]"
          style={{
            // pointerEvents: "auto",
            userSelect: "none",
            opacity: spinning ? 0 : 1,
          }}
        >
          <ScaleCanvas width={width} height={height} />
          <PerspectiveCamera
            near={1}
            far={1000}
            makeDefault
            up={[0, 1, 0]}
            fov={46}
            position={[0, 300, 0]}
          />
          <group rotation={[-Math.PI / 1.5, 0, 0]}>
            {/* 地图 */}
            <BasicMap />
          </group>

          <ambientLight color={"#FFFFFF"} intensity={3} />
          <OrbitControls
            makeDefault
            enableDamping={false}
            enablePan={false}
            enableRotate={false}
            enableZoom={false}
            zoomSpeed={0.2}
            maxZoom={0.1}
          />
          {/* <Stats /> */}
        </Canvas>
      </div>
      <div className="absolute bottom-[130px] right-[540px] bg-[#1B263B] border border-[#192C42] w-[220px] h-[100px] px-4 flex flex-col">
        <div className="absolute size-[4px] bg-[#293443] left-0 top-0"></div>
        <div className="absolute size-[4px] bg-[#293443] right-0 top-0"></div>
        <div className="absolute size-[4px] bg-[#293443] left-0 bottom-0"></div>
        <div className="absolute size-[4px] bg-[#293443] right-0 bottom-0"></div>
        <div className="color-text text-[20px] mt-[14px]">
          <Title>热力值</Title>
        </div>
        <div className="grid grid-cols-4 h-[10px] mt-1">
          <div className="bg-[#00BAFF]"></div>
          <div className="bg-[#00FF8A]"></div>
          <div className="bg-[#FF7200]"></div>
          <div className="bg-[#FA0001]"></div>
        </div>
        <div className="flex h-[10px] color-text mt-[2px]">
          <div>0</div>
          <div className="ml-[18px]">1500</div>
          <div className="ml-[10px]">3000</div>
          <div className="ml-[6px]">4500</div>
          <div className="ml-[15px]">∞</div>
        </div>
      </div>
      <Spin spinning={spinning} className="x-centered z-[100] top-[400px]" />
    </React.Fragment>
  );
}
export default () => {
  return (
    <Container.Provider>
      <GuangDongMap />
    </Container.Provider>
  );
};
