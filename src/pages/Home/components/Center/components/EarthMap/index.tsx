import bg from "@/assets/0/earthBg.webp";
import BgImage from "@/components/BgImage";
import * as THREE from "three";
import { Canvas } from "@react-three/fiber";
import ScaleCanvas from "./components/ScaleCanvas";
import {
  PerspectiveCamera,
  OrbitControls,
  useProgress,
} from "@react-three/drei";
import Map from "./components/Map";
import React, { useMemo } from "react";
import Container from "./container";
import { Spin } from "antd";

function EarthMap() {
  const width = 1920;
  const height = 1080;

  const positionValue = new THREE.Vector3(-168, 408*2, -480);

  const progress = useProgress();

  const { loading } = Container.useContainer();

  const spinning = useMemo(
    () => progress.progress < 100 || loading,
    [loading, progress.progress]
  );

  return (
    <React.Fragment>
      <BgImage
        url={bg}
        className="absolute centered w-[2026px] h-[1080px] pointer-events-none"
      />
      <Canvas
        className="absolute x-centered top-[140px]"
        // 对应 WebGLRenderer 渲染器的参数
        gl={{
          // 抗锯齿
          antialias: true,
          //  设备像素比 不同硬件设备的屏幕window.devicePixelRatio的值可能不同 设置是为了适应不同的硬件设备屏幕 min是为了防止设备像素比过大导致渲染器内存溢出
          pixelRatio: Math.min(window.devicePixelRatio, 2),
          // 色调映射
          toneMapping: 4,
          alpha: true,
        }}
        style={{
          pointerEvents: "auto",
          userSelect: "none",
        }}
      >
        <ScaleCanvas width={width} height={height} />
        <PerspectiveCamera
          near={1}
          far={99999}
          position={positionValue}
          up={[0, 1, 0]}
          fov={45}
          matrixAutoUpdate
          matrixWorldNeedsUpdate
          makeDefault
        ></PerspectiveCamera>
        <OrbitControls
          enabled
          enableDamping={false}
          enablePan={false}
          // enableRotate={false}
          enableZoom={false}
          makeDefault
          zoomSpeed={0.2}
          maxZoom={0.1}
        />
        {/* 地球 */}
        <Map />
        {/* 环境光 */}
        <ambientLight
          position={[0, 0, 10]}
          intensity={2.8}
          color={new THREE.Color("#fff")}
        />
      </Canvas>
      <Spin spinning={spinning} className="centered z-[100] top-[100px]" />
    </React.Fragment>
  );
}
export default () => {
  return (
    <Container.Provider>
      <EarthMap />
    </Container.Provider>
  );
};
