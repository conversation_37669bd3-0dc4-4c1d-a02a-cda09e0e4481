import { getCubicBezierCenterPoint, lngLatToXYZ } from "@/utils";
import { get } from "lodash-es";
import { useMemo } from "react";
import { THREE_EARTH_RADIUS } from "../../../config";
import { Vector3 } from "three";

interface IProps {
  data: {
    startLng: number;
    startLat: number;
    endLng: number;
    endLat: number;
  }[];
}

export interface CoordinateItemType {
  startVector: Vector3;
  endVector: Vector3;
  v1: Vector3;
  v2: Vector3;
}

function useCoordinateData(props: IProps) {
  const { data } = props;

  const coordinateData = useMemo(() => {
    const source = data.map((item) => {
      const startLng = get(item, "startLng");
      const startLat = get(item, "startLat");
      const endLng = get(item, "endLng");
      const endLat = get(item, "endLat");
      const start = lngLatToXYZ(startLng, startLat, THREE_EARTH_RADIUS);
      const end = lngLatToXYZ(endLng, endLat, THREE_EARTH_RADIUS);
      const startVector = new Vector3(start.x, start.y, start.z);
      const endVector = new Vector3(end.x, end.y, end.z);

      const { v1, v2 } = getCubicBezierCenterPoint(startVector, endVector);

      return {
        startVector,
        endVector,
        v1,
        v2,
      };
    });
    return source;
  }, [data]);

  return {
    coordinateData,
  };
}

export default useCoordinateData;
