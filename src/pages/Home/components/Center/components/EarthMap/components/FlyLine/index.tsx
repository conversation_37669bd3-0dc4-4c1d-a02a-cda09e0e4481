import React from "react";
import RenderRunwayLine from "./components/RenderRunwayLine";
import useCoordinateData from "./hooks/useCoordinateData";
import RenderFlowLine from "./components/RenderFlowLine";
import { lngLatToXYZ } from "@/utils";
import { Html } from "@react-three/drei";
import * as THREE from "three";
import { THREE_EARTH_RADIUS } from "../../config";
import pointImage from "@/assets/0/point.png";
import Container from "../../container";

interface IProps {
  meshRef: React.RefObject<THREE.Object3D<THREE.Object3DEventMap>>;
}
function FlyLine(props: IProps) {
  const { meshRef } = props;
  const radius = THREE_EARTH_RADIUS;

  const { list } = Container.useContainer();

  const { coordinateData } = useCoordinateData({
    data: list,
  });

  return (
    <group>
      {coordinateData?.map((item, index) => {
        return (
          <React.Fragment key={index}>
            <RenderRunwayLine item={item} />
            <RenderFlowLine item={item} />
          </React.Fragment>
        );
      })}
      {list?.map((val, index) => {
        const lng = val.startLng;
        const lat = val.startLat;
        const { x, y, z } = lngLatToXYZ(lng, lat, radius);
        const coordVec3 = new THREE.Vector3(x, y, z).normalize();
        const meshNormal = new THREE.Vector3(0, 0, 1);
        const quaternion = new THREE.Quaternion();
        quaternion.setFromUnitVectors(meshNormal, coordVec3);

        return (
          <group position={[x, y, z]} key={val.code ?? `earth-name${index}`}>
            <Html
              className="flex flex-col items-center w-max"
              position={[0, 0, 0]}
              transform
              sprite
              style={{
                transform: "translate(0,26px)",
              }}
              scale={[7, 7, 7]}
              occlude={[meshRef]}
            >
              <img src={pointImage} className="size-[50px] opacity-80 pointer-events-none" />
              <div
                className="text-[rgba(255,255,255,0.8)] text-[34px]"
                style={{
                  fontFamily: "YouSheBiaoTiHei-2",
                }}
              >
                {val.name}
              </div>
            </Html>
          </group>
        );
      })}
    </group>
  );
}
export default FlyLine;
