import React, { useMemo } from "react";
import {
  CatmullRomCurve3,
  CubicBezierCurve3,
  Mesh,
  MeshBasicMaterial,
  TubeGeometry,
} from "three";
import { transformColor } from "@/utils";
import * as THREE from "three";
import type { CoordinateItemType } from "../../hooks/useCoordinateData";

interface IProps {
  item: CoordinateItemType;
}

function RenderRunwayLine(props: IProps) {
  const { item } = props;

  const styleConfig = {
    width: 0.14,
    blending: false,
    runwayLine: { color: "#d74f12", show: true },
    transparent: true,
    height: 45,
  };

  const curvePointArray = useMemo(() => {
    const { startVector, endVector, v1, v2 } = item;
    // 使用3次贝塞尔曲线来绘制曲线
    const lineCurve = new CubicBezierCurve3(startVector, v1, v2, endVector);
    // 获取曲线上的点 暂时定为获取1000个点 后续绘制流线动画的时候更好的计算
    return lineCurve.getPoints(1000);
  }, [item]);

  const { runwayLine, transparent, width } = styleConfig;

  const curve = useMemo(() => {
    const points = curvePointArray;
    return new CatmullRomCurve3(points);
  }, [curvePointArray]);

  const lineMesh = useMemo(() => {
    const lineColor = transformColor(runwayLine.color);
    const tubeGeometry = new TubeGeometry(
      curve, // 一个由基类Curve继承而来的3D路径。
      256, // 组成这一管道的分段数，默认值为64。
      width * 2, // 管道半径。宽度比设置的大一点是为了让流线从管道内部穿过
      8, // 管道横截面的分段数目，默认值为8。
      false // 管道的两端是否闭合。
    );
    const material = new MeshBasicMaterial({
      color: lineColor.value,
      transparent,
      opacity: 0.6,
      depthTest: true, // 不进行深度检测 在鼠标悬浮的时候
      side: THREE.FrontSide,
    });
    const mesh = new Mesh(tubeGeometry, material);
    return mesh;
  }, [curve, runwayLine.color, transparent, width]);

  return (
    <React.Fragment>
      <primitive object={lineMesh} />
    </React.Fragment>
  );
}

export default RenderRunwayLine;
