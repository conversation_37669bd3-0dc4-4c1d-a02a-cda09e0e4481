import BgImage from "@/components/BgImage";
import React, { useState } from "react";
import tabBg from "@/assets/0/tabBg.png";
import tabBgActive from "@/assets/0/tabActiveBg.png";
import dictInfo from "@/dictInfo";
import { MapTypeEnum } from "@/enum";
import styles from "./index.module.less";
import EarthMap from "./components/EarthMap";
import GuangDongMap from "./components/GuangDongMap";
import ChinaMap from "./components/ChinaMap";

function Center() {
  const [mapType, setMapType] = useState(MapTypeEnum.World);

  return (
    <React.Fragment>
      <div className="flex gap-x-[32px] x-centered top-[160px] z-10">
        {dictInfo.mapType.map((val) => (
          <BgImage
            key={val.value}
            url={mapType === val.value ? tabBgActive : tabBg}
            className={`w-[200px] h-[58px] flex items-center justify-center text-[22px] tracking-[3px] font-bold cursor-pointer ${styles.tabItem}`}
            style={{
              color: mapType === val.value ? "#fff" : "#C5D0D4",
            }}
            onClick={() => setMapType(val.value)}
          >
            {val.label}
          </BgImage>
        ))}
      </div>
      {mapType === MapTypeEnum.China && <ChinaMap />}
      {mapType === MapTypeEnum.World && <EarthMap />}
      {mapType === MapTypeEnum.Guangdong && <GuangDongMap />}
    </React.Fragment>
  );
}
export default Center;
