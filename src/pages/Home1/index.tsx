import Statistics from "./components/Statistics";
import RealTimeEvents from "./components/RealTimeEvents";
import NotificationFrequency from "./components/NotificationFrequency";
import FeedbackRateRanking from "./components/FeedbackRateRanking";
import EventNotification from "./components/EventNotification";
import EventListModal from "./components/EventListModal";
import Container from "./container";
import EventDetailModal from "./components/EventDetailModal";

function Home1() {
  return (
    <Container.Provider>
      <Statistics />
      {/* 通报次数Top5 */}
      <NotificationFrequency />
      {/* 地市处置率排行 */}
      <FeedbackRateRanking />
      {/* 事件通报 */}
      <EventNotification />
      {/* 应急指挥实时事件 */}
      <RealTimeEvents />
      {/* 实时事件列表弹框 */}
      <EventListModal />
      {/* 事件详情弹框 */}
      <EventDetailModal />
    </Container.Provider>
  );
}
export default Home1;
