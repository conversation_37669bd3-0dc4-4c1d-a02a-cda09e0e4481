import Statistics from "./components/Statistics";
import RealTimeEvents from "./components/RealTimeEvents";
import FeedbackRateRanking from "./components/FeedbackRateRanking";
import ProtectedObject from "./components/ProtectedObject";
import EventNotification from "./components/EventNotification";
import EventListModal from "./components/EventListModal";
import Container from "./container";
import EventDetailModal from "./components/EventDetailModal";

function Home1() {
  return (
    <Container.Provider>
      <Statistics />
      {/* 通报次数Top5 - 已隐藏，由重点防护对象替代 */}
      {/* <NotificationFrequency /> */}
      {/* 重点防护对象 - 替代通报次数Top5的位置 */}
      <ProtectedObject />
      {/* 地市处置率排行 */}
      <FeedbackRateRanking />
      {/* 事件通报 */}
      <EventNotification />
      {/* 应急指挥实时事件 */}
      <RealTimeEvents />
      {/* 实时事件列表弹框 */}
      <EventListModal />
      {/* 事件详情弹框 */}
      <EventDetailModal />
    </Container.Provider>
  );
}
export default Home1;
