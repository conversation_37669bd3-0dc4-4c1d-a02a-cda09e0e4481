import BgImage from "@/components/BgImage";
import bg from "@/assets/1/mainBg.webp";
import { useRequest } from "ahooks";
import managementService from "@/service/managementService";
import { useTime } from "@/store/useTime";

function EventNotification() {
  const { startTime, endTime } = useTime();

  const { data } = useRequest(
    () =>
      managementService.threeLevelInfo({
        startTime,
        endTime,
      }),
    { refreshDeps: [startTime, endTime] }
  );

  return (
    <BgImage
      url={bg}
      className="absolute top-[150px] x-centered w-[1545px] h-[864px] pointer-events-none"
    >
      <div className="w-[184px] h-[34px] flex items-center gap-x-2 justify-center absolute top-[330px] left-[446px]">
        <span className="text-[16px] text-[rgba(255,255,255,.7)]">
          事件指令
        </span>
        <span className="text-white text-[22px] font-[DINCond-Bold]">
          {data?.eventInstructionCount ?? 0}
        </span>
      </div>
      <div className="w-[184px] h-[34px] flex items-center gap-x-2 justify-center absolute top-[338px] left-[680px]">
        <span className="text-[16px] text-[rgba(255,255,255,.7)]">
          风险指令
        </span>
        <span className="text-white text-[22px] font-[DINCond-Bold]">
          {data?.riskInstruction ?? 0}
        </span>
      </div>
      <div className="w-[184px] h-[34px] flex items-center gap-x-2 justify-center absolute top-[330px] left-[900px]">
        <span className="text-[16px] text-[rgba(255,255,255,.7)]">
          协同指令
        </span>
        <span className="text-white text-[22px] font-[DINCond-Bold]">
          {data?.collaborationInstruction ?? 0}
        </span>
      </div>
      <div className="w-[154px] h-[34px] flex items-center gap-x-2 justify-center absolute top-[628px] left-[462px]">
        <span className="text-[16px] text-[rgba(255,255,255,.7)]">处置</span>
        <span className="text-white text-[22px] font-[DINCond-Bold]">
          {data?.disposeOfCount ?? 0}
        </span>
      </div>
      <div className="w-[150px] h-[34px] flex items-center gap-x-2 justify-center absolute top-[653px] left-[696px]">
        <span className="text-[16px] text-[rgba(255,255,255,.7)]">通报</span>
        <span className="text-white text-[22px] font-[DINCond-Bold]">
          {data?.notificationCount ?? 0}
        </span>
      </div>
      <div className="w-[150px] h-[34px] flex items-center gap-x-2 justify-center absolute top-[628px] left-[918px]">
        <span className="text-[16px] text-[rgba(255,255,255,.7)]">预警</span>
        <span className="text-white text-[22px] font-[DINCond-Bold]">
          {data?.forewarningCount ?? 0}
        </span>
      </div>
    </BgImage>
  );
}
export default EventNotification;
