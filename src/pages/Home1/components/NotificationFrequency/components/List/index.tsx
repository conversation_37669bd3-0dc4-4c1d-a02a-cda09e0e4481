import { motion } from "framer-motion";
import useListSize from "@/hooks/useListSize";
import useListAnimate from "@/hooks/useListAnimate";
import {
  TableAnimationConnectModeEnum,
  TableAnimationModeEnum,
  TableAnimationTypeEnum,
} from "@/enum";
import ListItem from "../ListItem";

interface IProps {
  data: {
    value: number;

    name: string;
  }[];

  name?: string;

  type: string;

  // 当前页码
  current: number;

  maxValue: number;
}
function List(props: IProps) {
  const { data = [], name, type, current,maxValue } = props;

  const headerHeight = 38;

  const height = 260;

  const size = 5;

  const rowGap = 2;

  const width = 440;

  const { itemHeight, listHeight } = useListSize({
    headerHeight,
    height,
    size,
    rowGap,
  });

  const animationConfig = {
    show: false, // 是否显示动画
    type: TableAnimationTypeEnum.Single, // 类型
    connectMode: TableAnimationConnectModeEnum.Continuous, // 衔接方式
    animateMode: TableAnimationModeEnum.Flip, // 动画形式
    interval: 3, //
    backgroundFixed: false,
  };

  const {
    controls,
    firstControls,
    dataIndexList,
    showAnimate,
    beforeIndexList,
  } = useListAnimate({
    data,
    size,
    rowGap,
    listHeight,
    itemHeight,
    animationConfig,
  });

  return (
    <div
      style={{
        width,
        height,
      }}
      className="relative -top-2"
    >
      <div
        className="flex color-secondary items-center gap-x-3"
        style={{
          height: headerHeight,
        }}
      >
        <div className="w-[66px]">序号</div>
        <div className="flex-1">{name}名称</div>
        <div>次数</div>
      </div>
      {/* 列表 */}
      <div
        className="overflow-hidden relative px-[8px] ml-[-20px]"
        style={{
          height: listHeight,
          width: width + 40, // 为了进度图标能显示出来
        }}
      >
        <motion.div
          className="absolute"
          style={{
            width: width + 24,
            height: listHeight,
          }}
          key={JSON.stringify(data)}
          animate={controls}
        >
          {showAnimate && (
            <motion.div animate={firstControls}>
              {beforeIndexList?.map((val) => (
                <ListItem
                  key={`before${val}`}
                  index={val}
                  rowGap={rowGap}
                  item={data[val]}
                  max={maxValue}
                  showAnimate={false}
                  type={type}
                  current={current}
                  size={size}
                  itemHeight={itemHeight}
                />
              ))}
            </motion.div>
          )}
          {dataIndexList.map((val) => (
            <ListItem
              key={`listItem${val + 1}`}
              index={val}
              rowGap={rowGap}
              item={data[val]}
              max={maxValue}
              type={type}
              current={current}
              size={size}
              itemHeight={itemHeight}
            />
          ))}
        </motion.div>
      </div>
    </div>
  );
}
export default List;
