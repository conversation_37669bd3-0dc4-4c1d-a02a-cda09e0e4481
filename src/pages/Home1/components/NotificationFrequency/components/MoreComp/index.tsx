import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import managementService from "@/service/managementService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

interface IProps extends CustomModalProps {
  name?: string;

  type: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, name, type } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "name",
      title: name,
    },
    {
      dataIndex: "count",
      title: "通报次数",
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await managementService.notifyCountTop5({
      startTime,
      endTime,
      type,
      ...params,
    });
    return {
      data: res.notifyCountDtoList,
      success: true,
      total: res.total,
    };
  };

  return (
    <ProModal title={`${name}通报次数`} open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
