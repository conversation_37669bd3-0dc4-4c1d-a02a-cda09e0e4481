import Card from "@/components/Card";
import { useMemo, useState } from "react";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import List from "./components/List";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import managementService from "@/service/managementService";
import { Pagination } from "antd";

function NotificationFrequency() {
  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { startTime, endTime } = useTime();

  const [activeKey, setActiveKey] = useState("1");

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const { data, loading } = useRequest(
    () =>
      // managementService.notifyCountTop5({
      //   startTime,
      //   endTime,
      //   page: current,
      //   size: pageSize,
      //   type: activeKey,
      // }),
      // 10条假数据
      Promise.resolve({
        notifyCountDtoList: [
          { name: "地市1", count: 10 },
          { name: "地市2", count: 20 },
          { name: "地市3", count: 30 },
        ],
      }),
    {
      refreshDeps: [startTime, endTime, activeKey, pageSize, current],
    }
  );

  const list = useMemo(
    () =>
      data?.notifyCountDtoList?.map((val) => ({
        name: val.name,
        value: val.count
      })) ?? [],
    [data?.notifyCountDtoList]
  );

  const items = useMemo(() => {
    return [
      {
        key: "1",
        label: "地市",
      },
      {
        key: "2",
        label: "行业",
      },
    ];
  }, []);

  const name = useMemo(
    () => items.find((val) => val.key === activeKey)?.label,
    [activeKey, items]
  );

  const total = useMemo(() => {
    return data?.total ?? 0;
  }, [data?.total]);

  const maxValue = useMemo(() => {
    return data?.valueBig??0;
  }, [data?.valueBig]);

  return (
    <div className="absolute left-[46px] top-[382px]">
      <Card
        headerType={1}
        title="通报次数Top5"
        tabsProps={{
          items,
          activeKey,
          onChange: setActiveKey,
        }}
        bodyClass="!py-1"
        loading={loading}
        onMore={setTrue}
      >
        <List
          data={list}
          key={JSON.stringify(list)}
          name={name}
          type={activeKey}
          current={current}
          maxValue={maxValue}
        />
        {total > pageSize && (
          <div className="flex justify-end items-center">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, pageSize) => {
                setCurrent(page);
                setPageSize(pageSize);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
      <MoreComp
        open={open}
        onCancel={setFalse}
        key={visibleKey}
        name={name}
        type={activeKey}
      />
    </div>
  );
}
export default NotificationFrequency;
