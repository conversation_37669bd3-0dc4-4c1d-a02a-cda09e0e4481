import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import managementService from "@/service/managementService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { useMemo } from "react";

interface IProps extends CustomModalProps {
  type: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, type } = props;

  const isNotificationCount = type === "0";

  const columns: ProColumns[] = useMemo(() => {
    if (isNotificationCount) {
      return [
        {
          dataIndex: "dataIndex",
          title: "序号",
          align: "center",
          width: 100,
        },
        {
          title: "地市",
          dataIndex: "name",
        },
        {
          dataIndex: "value",
          title: "通报数",
        },
      ];
    }

    return [
      {
        dataIndex: "dataIndex",
        title: "序号",
        align: "center",
        width: 100,
      },
      {
        title: "地市",
        dataIndex: "cityOrIndustryName",
      },
      {
        dataIndex: "feedbackEventCount",
        title: type === "1" ? "已处置事件数" : "已响应事件数",
      },
      {
        dataIndex: "eventCount",
        title: "通报事件数",
      },
      {
        dataIndex: "feedbackRate",
        width: 320,
        title:
          type === "1" ? "处置率（%，未超过5天）" : "响应率（%，未超过24小时）",
      },
    ];
  }, [type, isNotificationCount]);

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    if (isNotificationCount) {
      const res = await managementService.notifyCountTop5({
        startTime,
        endTime,
        ...params,
        type: "1", // 地市类型
      });
      
      const data = res.notifyCountDtoList?.map((item) => ({
        name: item.name,
        value: item.count,
      })) || [];
      
      return {
        data,
        total: res.total,
      };
    }

    const res = await managementService.feedbackRateRank({
      startTime,
      endTime,
      ...params,
      type,
    });
    return {
      data: res.feedbackRateRankDtoList,
      total: res.totalCount,
    };
  };

  const getTitle = () => {
    if (isNotificationCount) {
      return "地市通报次数排行";
    }
    return type === "1" ? "地市处置率排行" : "地市响应率排行";
  };

  return (
    <ProModal title={getTitle()} open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
