import { motion } from "framer-motion";
import useListSize from "@/hooks/useListSize";
import useListAnimate from "@/hooks/useListAnimate";
import {
  TableAnimationConnectModeEnum,
  TableAnimationModeEnum,
  TableAnimationTypeEnum,
} from "@/enum";
import ListItem from "../ListItem";

interface NotificationCountItem {
  name: string;
  value: number;
}

interface IProps {
  data: NotificationCountItem[];
  type: string; // 1 政党 2 媒体 3 公共服务 4 重点行业
  maxValue?: number;
}

function List(props: IProps) {
  const { data, type } = props;
  const headerHeight = 38;

  const height = 256;

  const size = 5;

  const rowGap = 12;

  const width = 440;

  const { itemHeight, listHeight } = useListSize({
    headerHeight,
    height,
    size,
    rowGap,
  });

  const animationConfig = {
    show: false, // 是否显示动画
    type: TableAnimationTypeEnum.Single, // 类型
    connectMode: TableAnimationConnectModeEnum.Continuous, // 衔接方式
    animateMode: TableAnimationModeEnum.Flip, // 动画形式
    interval: 3, //
    backgroundFixed: false,
  };

  const {
    controls,
    firstControls,
    dataIndexList,
    showAnimate,
    beforeIndexList,
  } = useListAnimate({
    data,
    size,
    rowGap,
    listHeight,
    itemHeight,
    animationConfig,
  });

  const getHeaderTitle = () => {
    switch (type) {
      case "1":
        return "政党";
      case "2":
        return "媒体";
      case "3":
        return "公共服务";
      case "4":
        return "重点行业";
      default:
        return "防护对象";
    }
  };

  return (
    <div
      style={{
        width,
        height,
      }}
      className="relative -top-2"
    >
      <div
        className="flex color-secondary items-center gap-x-3 px-3"
        style={{
          height: headerHeight,
        }}
      >
        <div className="flex-1 text-sm">防护对象</div>
        <div className="w-[60px] text-sm">通报次数</div>
      </div>
      {/* 列表 */}
      <div
        className="overflow-hidden relative"
        style={{
          height: listHeight,
        }}
      >
        <motion.div
          className="absolute"
          style={{
            width,
            height: listHeight,
          }}
          animate={controls}
        >
          {showAnimate && (
            <motion.div animate={firstControls}>
              {beforeIndexList?.map((val) => (
                <ListItem
                  key={`before${val}`}
                  index={val}
                  rowGap={rowGap}
                  item={data[val]}
                  showAnimate={false}
                  itemHeight={itemHeight}
                  type={type}
                  maxValue={props.maxValue}
                />
              ))}
            </motion.div>
          )}
          {dataIndexList.map((val) => (
            <ListItem
              key={`listItem${val + 1}`}
              index={val}
              rowGap={rowGap}
              item={data[val]}
              itemHeight={itemHeight}
              type={type}
              maxValue={props.maxValue}
            />
          ))}
        </motion.div>
      </div>
    </div>
  );
}
export default List;
