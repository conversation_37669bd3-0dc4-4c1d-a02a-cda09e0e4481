import { motion } from "framer-motion";
import { useMemo } from "react";

interface NotificationCountItem {
  name: string;
  value: number;
}

interface IProps {
  index: number;
  rowGap: number;
  itemHeight: number;
  item: NotificationCountItem;
  type: string;
  maxValue?: number;
  showAnimate?: boolean;
}

function ListItem(props: IProps) {
  const {
    index,
    rowGap,
    itemHeight,
    item,
    type,
    maxValue = 100,
    showAnimate = true,
  } = props;
  
  // 获取名称和显示值
  const objectName = item.name;
  const displayValue = item.value;
  
  // 计算宽度百分比
  const widthPercentage = ((displayValue as number) / maxValue) * 100;

  const animate = useMemo(() => {
    return {
      width: `${widthPercentage}%`,
      transition: {
        duration: showAnimate ? 1 : 0,
      },
    };
  }, [widthPercentage, showAnimate]);



  const barStyle = useMemo(() => {
    const colors = [
      "linear-gradient(90deg, #00BFFF 0%, #1E90FF 100%)", // 蓝色
      "linear-gradient(90deg, #FF6347 0%, #FF4500 100%)", // 橙红色
      "linear-gradient(90deg, #32CD32 0%, #228B22 100%)", // 绿色
      "linear-gradient(90deg, #FFD700 0%, #FFA500 100%)", // 金色
      "linear-gradient(90deg, #9370DB 0%, #8A2BE2 100%)", // 紫色
      "linear-gradient(90deg, #20B2AA 0%, #008B8B 100%)", // 青色
    ];
    return {
      background: colors[index % colors.length],
    };
  }, [index]);

  const valueStyle = useMemo(() => {
    return {
      color: "#FFFFFF",
    };
  }, []);

  return (
    <div
      className="flex items-center"
      style={{
        height: itemHeight,
        marginBottom: rowGap,
      }}
    >
      <div className="flex-1 flex items-center">
        <div className="w-[60px] truncate text-white leading-tight">{objectName}</div>
        <div className="h-[6px] flex-1 flex items-center bg-[rgba(255,255,255,0.1)] relative rounded-[3px] overflow-hidden">
          <motion.div
            className="h-full rounded-[3px]"
            style={barStyle}
            animate={animate}
          ></motion.div>
        </div>
        <div
          className="text-white w-[50px] text-center font-bold"
          style={{
            fontFamily: "DINCond-Bold",
            ...valueStyle,
          }}
        >
          {displayValue}
        </div>
      </div>
    </div>
  );
}

export default ListItem;
