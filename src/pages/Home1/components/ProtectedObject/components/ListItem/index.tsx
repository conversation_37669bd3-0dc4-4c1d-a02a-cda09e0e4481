import { motion } from "framer-motion";
import { useMemo } from "react";

interface NotificationCountItem {
  name: string;
  value: number;
}

interface IProps {
  index: number;
  rowGap: number;
  itemHeight: number;
  item: NotificationCountItem;
  type: string;
  maxValue?: number;
  showAnimate?: boolean;
}

function ListItem(props: IProps) {
  const {
    index,
    rowGap,
    itemHeight,
    item,
    type,
    maxValue = 100,
    showAnimate = true,
  } = props;
  
  // 获取名称和显示值
  const objectName = item.name;
  const displayValue = item.value;
  
  // 计算宽度百分比
  const widthPercentage = ((displayValue as number) / maxValue) * 100;

  const animate = useMemo(() => {
    return {
      width: `${widthPercentage}%`,
      transition: {
        duration: showAnimate ? 1 : 0,
      },
    };
  }, [widthPercentage, showAnimate]);

  const indexStyle = useMemo(() => {
    if (index === 0) {
      return {
        background: "linear-gradient(180deg, #FFD700 0%, #FFA500 100%)",
        color: "#000",
      };
    }
    if (index === 1) {
      return {
        background: "linear-gradient(180deg, #C0C0C0 0%, #A9A9A9 100%)",
        color: "#000",
      };
    }
    if (index === 2) {
      return {
        background: "linear-gradient(180deg, #CD7F32 0%, #B8860B 100%)",
        color: "#000",
      };
    }
    return {
      background: "linear-gradient(180deg, #4A90E2 0%, #357ABD 100%)",
      color: "#fff",
    };
  }, [index]);

  const barStyle = useMemo(() => {
    if (index === 0) {
      return {
        background: "linear-gradient(90deg, #FFD700 0%, #FFA500 100%)",
      };
    }
    if (index === 1) {
      return {
        background: "linear-gradient(90deg, #C0C0C0 0%, #A9A9A9 100%)",
      };
    }
    if (index === 2) {
      return {
        background: "linear-gradient(90deg, #CD7F32 0%, #B8860B 100%)",
      };
    }
    return {
      background: "linear-gradient(90deg, #4A90E2 0%, #357ABD 100%)",
    };
  }, [index]);

  const valueStyle = useMemo(() => {
    if (index === 0) {
      return {
        background: "linear-gradient(180deg, #FFD700 0%, #FFA500 100%)",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        backgroundClip: "text",
      };
    }
    if (index === 1) {
      return {
        background: "linear-gradient(180deg, #C0C0C0 0%, #A9A9A9 100%)",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        backgroundClip: "text",
      };
    }
    if (index === 2) {
      return {
        background: "linear-gradient(180deg, #CD7F32 0%, #B8860B 100%)",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        backgroundClip: "text",
      };
    }
    return {
      color: "#4A90E2",
    };
  }, [index]);

  return (
    <div
      className="flex items-center gap-x-2"
      style={{
        height: itemHeight,
        marginBottom: rowGap,
      }}
    >
      <div className="flex-1 h-full px-2 flex items-center gap-x-4 border border-[rgba(197,208,212,.15)]">
        <div
          className="w-[66px] min-w-[66px] h-[26px] color-text flex items-center justify-center"
          style={{
            ...indexStyle,
          }}
        >
          Top {index + 1 < 10 ? 0 : ""}
          {index + 1}
        </div>
        <div className="w-[50px] truncate">{objectName}</div>
        <div className="h-[8px] flex-1 flex items-center bg-[#232E40] relative rounded-[3px] overflow-hidden">
          <motion.div
            className="h-full rounded-[3px]"
            style={barStyle}
            animate={animate}
          ></motion.div>
        </div>
      </div>
      <div
        className="text-white text-[18px] w-[68px] h-full items-center justify-center flex"
        style={{
          fontFamily: "DINCond-Bold",
          ...valueStyle,
        }}
      >
        {displayValue}
      </div>
    </div>
  );
}

export default ListItem;
