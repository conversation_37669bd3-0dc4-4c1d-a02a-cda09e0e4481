import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { useMemo } from "react";

interface IProps extends CustomModalProps {
  type: string;
}

function MoreComp(props: IProps) {
  const { open, onCancel, type } = props;

  const columns: ProColumns[] = useMemo(() => {
    return [
      {
        dataIndex: "dataIndex",
        title: "序号",
        align: "center",
        width: 100,
      },
      {
        title: getTypeLabel(type),
        dataIndex: "name",
      },
      {
        dataIndex: "value",
        title: "通报次数",
      },
    ];
  }, [type]);



  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    // 模拟数据
    const mockData = getMockData(type);

    const data = mockData.notifyCountDtoList?.map((item) => ({
      name: item.name,
      value: item.count,
    })) || [];

    return {
      data,
      total: mockData.total,
    };
  };

  function getMockData(type: string) {
    switch (type) {
      case "1":
        return {
          notifyCountDtoList: [
            { name: "教育部", count: 100 },
            { name: "工信部", count: 80 },
            { name: "农业部", count: 20 },
            { name: "发改委", count: 40 },
            { name: "网信部", count: 30 },
            { name: "运输部", count: 24 },
          ],
          total: 6,
        };
      case "2":
        return {
          notifyCountDtoList: [
            { name: "人民日报", count: 85 },
            { name: "新华社", count: 72 },
            { name: "央视网", count: 65 },
            { name: "光明日报", count: 58 },
            { name: "经济日报", count: 45 },
          ],
          total: 5,
        };
      case "3":
        return {
          notifyCountDtoList: [
            { name: "政务服务网", count: 95 },
            { name: "公积金中心", count: 78 },
            { name: "社保局", count: 66 },
            { name: "税务局", count: 54 },
            { name: "工商局", count: 42 },
          ],
          total: 5,
        };
      case "4":
        return {
          notifyCountDtoList: [
            { name: "电力系统", count: 88 },
            { name: "金融机构", count: 76 },
            { name: "通信运营商", count: 69 },
            { name: "交通运输", count: 52 },
            { name: "水利设施", count: 38 },
          ],
          total: 5,
        };
      default:
        return { notifyCountDtoList: [], total: 0 };
    }
  }

  function getTypeLabel(type: string) {
    switch (type) {
      case "1":
        return "政党";
      case "2":
        return "媒体";
      case "3":
        return "公共服务";
      case "4":
        return "重点行业";
      default:
        return "防护对象";
    }
  }

  function getApiType(type: string) {
    switch (type) {
      case "1":
        return "3"; // 政党机关类型
      case "2":
        return "4"; // 媒体类型
      case "3":
        return "5"; // 公共服务类型
      case "4":
        return "6"; // 重点行业类型
      default:
        return "3";
    }
  }

  const getTitle = () => {
    return `${getTypeLabel(type)}通报次数排行`;
  };

  return (
    <ProModal title={getTitle()} open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
