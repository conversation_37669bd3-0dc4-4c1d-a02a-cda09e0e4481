import { memo } from "react";
import Container from "../../container";
import Comp from "./components/Comp";

function EventDetailModal() {
  const { detailOpen, setDetailFalse, detailVisibleKey, notifyId } =
    Container.useContainer();

  return (
    <Comp
      key={detailVisibleKey}
      open={detailOpen}
      onCancel={setDetailFalse}
      notifyId={notifyId}
    />
  );
}
export default memo(EventDetailModal);
