import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import dictInfo from "@/dictInfo";
import managementService from "@/service/managementService";
import { useRequest } from "ahooks";
import { Descriptions } from "antd";
import type { DescriptionsProps } from "antd";
import saveAs from "file-saver";
import { useMemo } from "react";

interface IProps extends CustomModalProps {
  notifyId?: string;
}

function Comp(props: IProps) {
  const { notifyId, onCancel, open } = props;

  const { data } = useRequest(
    () => managementService.eventNotifyInfo(notifyId!),
    {
      ready: open,
      refreshDeps: [notifyId],
    }
  );

  const levelName = useMemo(
    () =>
      dictInfo.realtimeEventLevel.find((val) => val.value === data?.eventLevel)
        ?.label,
    [data?.eventLevel]
  );

  const items: DescriptionsProps["items"] = [
    {
      key: "1",
      label: "所属区域",
      children: data?.area,
    },
    {
      key: "2",
      label: "事件等级",
      children: levelName,
    },
    {
      key: "3",
      label: "事件详情",
      children: data?.eventDetail,
      span: 2,
    },
    {
      key: "4",
      label: "事件描述",
      children: data?.eventDesc,
      span: 2,
    },
    {
      key: "5",
      label: "附件",
      children: (
        <div>
          {data?.eventAttachmentList?.map((val, index) => (
            <span
              key={`eventAttachment${index}`}
              className="text-[#395ABF] cursor-pointer"
              onClick={() => {
                saveAs(val.attachmentFile, `${val.filename}.${val.fileType}`);
              }}
            >
              {val.filename}.{val.fileType}
            </span>
          ))}
        </div>
      ),
      span: 2,
    },
    {
      key: "5",
      label: "处置情况",
      span: 2,
      children: (
        <div className="grid gap-y-3">
          {data?.processInfoDtoList?.map((val, index) => (
            <div key={`processInfoDto${index}`}>
              <div>
                <span className="mr-3 text-[#395ABF]">{val.processTime}</span>
                {val.processName}
              </div>
              <div className="text-[#395ABF] leading-8">
                {val.disposalOpinion}
              </div>
              {val.attachmentList?.map((val, fileIndex) => (
                <div
                  key={`attachmentList${index}-${fileIndex}`}
                  className="text-[#395ABF] leading-8 cursor-pointer"
                  onClick={() => {
                    saveAs(
                      val.attachmentFile,
                      `${val.filename}.${val.fileType}`
                    );
                  }}
                >
                  {val.filename}.{val.fileType}
                </div>
              ))}
            </div>
          ))}
        </div>
      ),
    },
  ];

  return (
    <ProModal
      type={2}
      title={`${data?.eventName} 事件通报`}
      open={open}
      onCancel={onCancel}
    >
      <Descriptions
        labelStyle={{
          width: 100,
          justifyContent: "flex-end",
        }}
        column={2}
        className="py-6 px-6 text-[30px]"
        items={items}
      />
    </ProModal>
  );
}
export default Comp;
