import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import dictInfo from "@/dictInfo";
import type { EventTypeEnum } from "@/enum";
import Container from "@/pages/Home1/container";
import managementService from "@/service/managementService";
import { useTime } from "@/store/useTime";
import { ProFormDateRangePicker, ProFormSelect, ProFormTreeSelect, QueryFilter } from "@ant-design/pro-components";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";
import { memo } from "react";

interface IProps extends CustomModalProps {
  /**
   * 查询类型
   */
  type?: EventTypeEnum;

  /**
   * 地市名称
   */
  city?: string;

  /**
   * 行业名称
   */
  industry?: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, ...rest } = props;

  const { onShowDetail, params } = Container.useContainer();

  const columns: ProColumns<API.Management.RealtimeEventDto>[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
    },
    {
      dataIndex: "foundTime",
      title: "事件发生时间",
      width: 200,
    },
    {
      dataIndex: "unitName",
      title: "单位名称",
    },
    {
      dataIndex: "eventType",
      title: "事件类型",
    },
    {
      dataIndex: "eventLevel",
      title: "事件等级",
      valueType: "select",
      fieldProps: {
        options: dictInfo.realtimeEventLevel,
      },
    },
    {
      dataIndex: "eventStatus",
      title: "事件状态",
      valueType: "select",
      fieldProps: {
        options: dictInfo.realtimeEventStatus,
      },
    },
    {
      dataIndex: "unitIndustry",
      title: "单位行业",
    },
    {
      dataIndex: "eventArea",
      title: "事件所属区域",
    },
    {
      dataIndex: "notifyId",
      title: "操作",
      render: (_, record) => {
        return (
          <a
            className="text-[#395ABF]"
            onClick={() => {
              onShowDetail(record.notifyId);
            }}
          >
            详情
          </a>
        );
      },
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    listParams: API.PageParams
  ) => {
    const res = await managementService.realtimeEventList({
      startTime,
      endTime,
      ...listParams,
      ...rest,
      ...params,
    });
    return {
      data: res.realtimeEventDtoList ?? [],
      total: res.total,
    };
  };

  return (
    <ProModal type={2} title="应急指挥实时事件" open={open} onCancel={onCancel}>
      <QueryFilter defaultCollapsed split labelWidth={120}>
        <ProFormSelect name="name" label="单位行业" />
        <ProFormDateRangePicker name="time" label="事件发生时间" />
        <ProFormSelect name="type" label="事件类型" />
        <ProFormSelect name="level" label="事件等级" />
        <ProFormSelect name="status" label="事件状态" />
        <ProFormTreeSelect name="tree" label="事件所属区域" />
      </QueryFilter>
      {/* <div className="flex items-center mb-4">
        {tabList?.map((val) => (
          <div
            key={val.value}
            onClick={() =>
              setParams({
                type: val.value,
              })
            }
            className={`${params.type === val.value ? "bg-[#165184] color-text" : "bg-[#27476A] color-secondary"} border border-[#3678BC] w-[90px] h-[36px] flex items-center justify-center cursor-pointer hover:bg-[#165184] color-text`}
          >
            {val.label}
          </div>
        ))}
      </div> */}
      <ProTable
        columns={columns}
        request={open ? loadList : undefined}
        params={params}
        scroll={{
          y: 480,
        }}
      />
    </ProModal>
  );
}

export default memo(MoreComp);
