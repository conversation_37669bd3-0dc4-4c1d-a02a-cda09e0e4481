import bg6 from "@/assets/1/bg6.png";
import bg7 from "@/assets/1/bg7.png";
import BgImage from "@/components/BgImage";
import { nanoid } from "nanoid";
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip } from "recharts";
import type { ContentType } from "recharts/types/component/Tooltip";
import type {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import React, { useCallback, useMemo, useRef } from "react";
import SvgLinearGradient from "@/components/Comp/SvgLinearGradient";
import type { LinearConfig } from "@/typings";
import { formatLinear } from "@/utils";

interface IProps {
  data: API.Management.EventTypeRankDto[];

  // 图表放大的倍数
  scale?: number;
}
function Comp(props: IProps) {
  const { data, scale = 1 } = props;

  const colorList: LinearConfig[] = useMemo(
    () => [
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(166, 207, 255, 1)",
          },
          {
            offset: 1,
            color: "rgba(166, 207, 255, 0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(44, 81, 250, 1)",
          },
          {
            offset: 1,
            color: "rgba(44, 81, 250, 0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(211,173,247,1)",
          },
          {
            offset: 1,
            color: "rgba(211,173,247,0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(238, 111, 124, 1)",
          },
          {
            offset: 1,
            color: "rgba(238, 111, 124, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(255, 207, 95, 1)",
          },
          {
            offset: 1,
            color: "rgba(255, 207, 95, .75)",
          },
        ],
      },
    ],
    []
  );

  const linearIdRef = useRef(`${nanoid()}_linear_`);

  const getColor = useCallback(
    (index) => {
      return colorList[index];
    },
    [colorList]
  );

  const CustomTooltip: ContentType<ValueType, NameType> = ({
    active,
    payload,
  }) => {
    if (active && payload && payload.length) {
      return (
        <div
          className="w-fit justify-center color-text bg-[rgba(50,91,174,0.4)] rounded-[4px] px-5 py-2"
          style={{
            boxShadow: "inset 0px 0 20px 0px #5FC1FF",
            backdropFilter: "blur(12px)",
          }}
        >
          {payload.map((val) => (
            <div className="flex items-center gap-x-3" key={val.dataKey}>
              <span className="text-[16px] w-auto">{val.name}</span>
              <span
                className="text-[22px]"
                style={{
                  fontFamily: "DINCond-Bold",
                  color: val.color,
                }}
              >
                {val.value}
              </span>
            </div>
          ))}
        </div>
      );
    }

    return null;
  };

  return (
    <div className="flex mt-2">
      <div
        className="size-[228px] relative flex justify-center items-center"
        style={{
          transform: `scale(${scale})`,
        }}
      >
        <div className="centered">
          <img
            src={bg6}
            className="w-[228px] animate-[rotateCounterClockwise_16s_linear_infinite]"
          />
        </div>
        <div className="centered">
          <BgImage
            url={bg7}
            className="size-[112px] animate-[rotateClockwise_16s_linear_infinite]"
          ></BgImage>
        </div>
        <PieChart
          width={190}
          height={190}
          margin={{
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
          }}
        >
          {colorList.map((_, index) => {
            const linearId = `${linearIdRef.current}${index}`;
            const colorConfig = getColor(index);
            return (
              <React.Fragment key={linearId}>
                <defs>
                  <SvgLinearGradient id={linearId} config={colorConfig} />
                </defs>
              </React.Fragment>
            );
          })}
          <Pie
            data={data}
            cx={95}
            cy={95}
            innerRadius={76}
            outerRadius={90}
            paddingAngle={6} // 饼图内间距
            dataKey="eventCount"
            nameKey="eventType"
            stroke="none"
            cornerRadius={0} // 圆角
            isAnimationActive={false}
          >
            {data.map((entry, index) => {
              const linearId = `${linearIdRef.current}${index}`;
              return (
                <React.Fragment key={linearId}>
                  <Cell
                    style={{
                      outline: "none",
                      overflow: "hidden",
                    }}
                    key={`cell-${index}`}
                    fill={`url(#${linearId})`}
                  />
                </React.Fragment>
              );
            })}
          </Pie>
          <Tooltip
            content={<CustomTooltip />}
            wrapperStyle={{
              width: "max-content",
            }}
          />
        </PieChart>
      </div>
      <div
        className="flex flex-col flex-1 gap-y-3 self-center"
        style={{
          marginLeft: `${16 + 228 * (scale - 1)}px`,
        }}
      >
        {data.map((val, index) => {
          const colorConfig = getColor(index);
          return (
            <div
              key={`legend${index + 1}`}
              className="flex items-center justify-between"
            >
              <div className="flex items-center">
                <span
                  className="size-[10px] rounded-full inline-block"
                  style={{
                    background: formatLinear(colorConfig),
                  }}
                ></span>
                <span className="ml-3 color-text text-[15px]">
                  {val.eventType}
                </span>
              </div>
              <div
                className="text-[20px]"
                style={{
                  backgroundImage: formatLinear(colorConfig),
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  // fontFamily: "YouSheBiaoTiHei-2",
                }}
              >
                {val.eventCount}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
export default Comp;
