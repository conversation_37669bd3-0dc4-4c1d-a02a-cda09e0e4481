import Card from "@/components/Card";
import Comp from "./components/Comp";
import ProModal from "@/components/Modal";
import { useBoolean, useRequest } from "ahooks";
import managementService from "@/service/managementService";
import { useTime } from "@/store/useTime";
import { useMemo } from "react";

function TypeRanking() {
  const [open, { setFalse, setTrue }] = useBoolean(false);

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      managementService.eventTypeRank({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () => data?.eventTypeRankDtoList ?? [],
    [data?.eventTypeRankDtoList]
  );

  return (
    <div className="absolute left-[40px] top-[740px] h-[240px]">
      <ProModal
        type={1}
        title="应急指挥事件类型排行"
        open={open}
        onCancel={setFalse}
      >
        <div className="flex justify-center items-center h-[500px]">
          <div className="w-[600px]">
            <Comp data={list} scale={1.4} />
          </div>
        </div>
      </ProModal>
      <Card
        headerType={1}
        title="应急指挥事件类型排行"
        onMore={setTrue}
        loading={loading}
      >
        <Comp data={list} />
      </Card>
    </div>
  );
}
export default TypeRanking;
