import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import dictInfo from "@/dictInfo";
import synergyService from "@/service/synergyService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
    },
    {
      title: "危险级别",
      key: "eventLevel",
      dataIndex: "eventLevel",
      valueType: "select",
      width: 100,
      fieldProps: {
        options: dictInfo.eventLevel,
      },
    },
    {
      title: "单位",
      dataIndex: "unitName",
      key: "unitName",
      width: 260,
    },
    {
      title: "攻击Ip所属区域",
      dataIndex: "attackIpArea",
      key: "attackIpArea",
    },
    {
      title: "攻击Ip",
      dataIndex: "attackIp",
      key: "attackIp",
    },
    {
      title: "受攻击Ip",
      dataIndex: "attackedIp",
      key: "attackedIp",
    },
    {
      title: "告警次数",
      dataIndex: "attackNum",
      key: "attackNum",
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await synergyService.attackedIpList({
      startTime,
      endTime,
      ...params,
    });
    return {
      total: res.total,
      data: res.attackedIpDtoList ?? [],
    };
  };

  return (
    <ProModal type={2} title="受攻击IP列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
