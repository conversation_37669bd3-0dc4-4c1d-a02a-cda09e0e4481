import Card from "@/components/Card";
import List from "./components/List";
import { useRequest } from "ahooks";
import { useMemo, useState } from "react";
import { useTime } from "@/store/useTime";
import Container from "../../container";
import managementService from "@/service/managementService";
import { Pagination } from "antd";

function UnderAttackedIp() {
  const { onShowList } = Container.useContainer();

  const { startTime, endTime } = useTime();

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const { data, loading } = useRequest(
    () =>
      managementService.realtimeEventList({
        startTime,
        endTime,
        page: current,
        size: pageSize,
      }),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
    }
  );

  const list = useMemo(() => {
    return data?.realtimeEventDtoList ?? [];
  }, [data?.realtimeEventDtoList]);

  const total = useMemo(() => data?.total ?? 0, [data?.total]);

  return (
    <div className="absolute top-[130px] right-[30px]">
      <Card
        headerType={1}
        title="应急指挥实时事件"
        onMore={() => onShowList({})}
        loading={loading}
      >
        <List data={list} />
        {total > pageSize && (
          <div className="flex justify-end items-center mt-4">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, pageSize) => {
                setCurrent(page);
                setPageSize(pageSize);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}
export default UnderAttackedIp;
