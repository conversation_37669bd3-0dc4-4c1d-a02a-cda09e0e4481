import ionBg1 from "@/assets/1/bg1.png";
import iconBg2 from "@/assets/1/bg2.webp";
import iconBg4 from "@/assets/1/bg4.png";
import iconBg5 from "@/assets/1/bg5.png";
import icon1 from "@/assets/1/1.png";
import icon2 from "@/assets/1/2.png";
import icon3 from "@/assets/1/3.png";
import bg8 from "@/assets/1/8.png";
import React, { useCallback } from "react";
import { useRequest } from "ahooks";
import managementService from "@/service/managementService";
import { useTime } from "@/store/useTime";
import { Spin } from "antd";
import { EventTypeEnum } from "@/enum";
import Container from "../../container";
import BgImage from "@/components/BgImage";

function Statistics() {
  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    async () =>
      managementService.eventNotifyOverview({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const { onShowList } = Container.useContainer();

  const onDetail = useCallback(
    (value: EventTypeEnum) => {
      onShowList({
        type: value,
      });
    },
    [onShowList]
  );

  return (
    <React.Fragment>
      <div className="absolute left-[58px] top-[124px] w-[440px] h-[248px]">
        <Spin spinning={loading}>
          {/* <div className="flex items-center relative mt-1">
            <div
              className="w-[138px] h-[124px] ml-[-20px] mt-[-10px] bg-cover text-center"
              style={{
                backgroundImage: `url(${ionBg1})`,
              }}
            >
              <img
                className="inline-block w-[70px] animate-[moveOpacity_2s_ease-in-out_infinite]"
                src={icon1}
                alt=""
              />
            </div>
            <div>
              <BgImage
                className="w-[334px] absolute top-0 h-[116px] flex items-center justify-center flex-col cursor-pointer"
                url={iconBg2}
                onClick={() => onDetail(EventTypeEnum.All)}
              >
                <div className="text-[24px] color-secondary]">通报事件总数</div>
                <div className="text-[50px] color-secondary leading-[46px] color-text font-[DINCond-Bold]">
                  {data?.eventNotifyCount}
                </div>
              </BgImage>
            </div>
          </div> */}
          <div className="h-[136px]" style={{ background: `url(${bg8}) no-repeat center center / 100% 100%` }}>
            <div className="flex items-center justify-center">
              <div className="text-[24px] color-secondary">通报事件总数</div>
              <div className="text-[50px] color-secondary leading-[46px] color-text font-[DINCond-Bold]">
                {data?.eventNotifyCount}
              </div>
            </div>
          </div>
          <div className="flex mt-[20px] gap-x-2">
            <div
              className="flex cursor-pointer"
              onClick={() => onDetail(EventTypeEnum.Responded)}
            >
              <div
                className="w-[106px] h-[106px] flex items-center justify-center"
                style={{
                  backgroundImage: `url(${iconBg4})`,
                }}
              >
                <img src={icon2} alt="" className="w-[94px] -mt-2" />
              </div>
              <div
                className="w-[114px] h-[106px] text-center"
                style={{
                  backgroundImage: `url(${iconBg5})`,
                }}
              >
                <div className="color-secondary leading-[40px]">响应率</div>
                <div className="color-text leading-[56px] text-[32px] font-[DINCond-Bold]">
                  {data?.feedbackRate}%
                </div>
              </div>
            </div>
            <div
              className="flex cursor-pointer"
              onClick={() => onDetail(EventTypeEnum.Dealt)}
            >
              <div
                className="w-[106px] h-[106px] flex items-center justify-center"
                style={{
                  backgroundImage: `url(${iconBg4})`,
                }}
              >
                <img src={icon3} alt="" className="w-[94px] -mt-2" />
              </div>
              <div
                className="w-[114px] h-[106px] text-center"
                style={{
                  backgroundImage: `url(${iconBg5})`,
                }}
              >
                <div className="color-secondary leading-[40px]">处置率</div>
                <div className="color-text leading-[56px] text-[32px] font-[DINCond-Bold]">
                  {data?.archiveRate}%
                </div>
              </div>
            </div>
          </div>
        </Spin>
      </div>
    </React.Fragment>
  );
}
export default Statistics;
