import { createContainer } from "unstated-next";
import { useState } from "react";
import useVisible from "@/hooks/useVisible";
import { EventTypeEnum } from "@/enum";

interface Params {
  /**
   * 查询类型
   */
  type?: EventTypeEnum;

  /**
   * 地市名称
   */
  city?: string;

  /**
   * 行业名称
   */
  industry?: string;
}
function useContainer() {
  const [notifyId, setNotifyId] = useState<string>();

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const [params, setParams] = useState<Params>({
    type: EventTypeEnum.All,
  });

  const [
    detailOpen,
    {
      setFalse: setDetailFalse,
      setTrue: setDetailTrue,
      visibleKey: detailVisibleKey,
    },
  ] = useVisible(false);

  // 打开实时事件弹框列表
  const onShowList = (params: Params) => {
    setParams(params);
    setTrue();
  };

  // 打开实时事件详情
  const onShowDetail = (value: string) => {
    setNotifyId(value);
    setDetailTrue();
  };

  return {
    setNotifyId,
    notifyId,
    open,
    setFalse,
    setTrue,
    visibleKey,
    onShowList,
    params,
    detailOpen,
    setDetailFalse,
    detailVisibleKey,
    onShowDetail,
    setParams
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
