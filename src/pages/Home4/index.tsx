import React from "react";
// import Overview from "./components/Overview";
import AttackType from "./components/AttackType";
import ThreatSituation from "./components/ThreatSituation";
import ThreatenIp from "./components/ThreatenIp";
import AttackDistribution from "./components/AttackDistribution";
import UnderAttackDistribution from "./components/UnderAttackDistribution";
import LoopholeInfo from "./components/LoopholeInfo";
import EventInfo from "./components/EventInfo";
import Center from "./components/Center";

function Home4() {
  return (
    <React.Fragment>
      {/* 攻击类型分布 */}
      <AttackType />
      {/* 受攻击分布 */}
      <UnderAttackDistribution />
      {/* 攻击源分布 */}
      <AttackDistribution />
      {/* 威胁IP */}
      <ThreatenIp />
      {/* 漏洞信息 */}
      <LoopholeInfo />
      {/* 事件信息 */}
      <EventInfo />
      {/* 攻击次数态势 */}
      <ThreatSituation />
      {/* 威胁概括 */}
      {/* <Overview /> */}
      <Center />
    </React.Fragment>
  );
}
export default Home4;
