import Card from "@/components/Card";
import List from "@/components/List";
import useVisible from "@/hooks/useVisible";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import { useMemo, useState } from "react";
import { useRequest } from "ahooks";
import { useTime } from "@/store/useTime";
import situationService from "@/service/situationService";
import type { DefaultValueParams } from "../EventDetail";
import EventDetail from "../EventDetail";

function AttackDistribution() {
  const [activeKey, setActiveKey] = useState("1");

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      situationService.attackSourceList({
        startTime,
        endTime,
        page: 1,
        size: 20,
        dataType: activeKey,
      }),
    {
      refreshDeps: [startTime, endTime, activeKey],
    }
  );

  const list = useMemo(
    () =>
      data?.attackSourceList?.map((val, index) => ({
        ...val,
        id: index,
      })) ?? [],
    [data?.attackSourceList]
  );

  const columns: TableProps["columns"] = [
    {
      title: "序号",
      key: "dataIndex",
      dataIndex: "dataIndex",
      render: (_, __, index) => index + 1,
      width: 60,
      align: "center",
      className: "mr-4",
    },
    {
      title: "国家/地区",
      dataIndex: "region",
      key: "region",
      width: 140,
    },
    {
      title: "事件数量",
      dataIndex: "eventCount",
      key: "eventCount",
    },
    {
      title: "环比",
      dataIndex: "ratio",
      key: "ratio",
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const [
    detailOpen,
    { setFalse: detailSetFalse, setTrue: detailSetTrue, visibleKey: detailKey },
  ] = useVisible(false);

  const [params, setParams] = useState<DefaultValueParams>({
    srcCountry: undefined,
    srcProvince: undefined,
  });

  return (
    <div className="absolute left-[46px] top-[742px] h-[240px]">
      <MoreComp
        open={open}
        onCancel={setFalse}
        key={visibleKey}
        dataType={activeKey}
      />
      <EventDetail
        open={detailOpen}
        onCancel={detailSetFalse}
        key={detailKey}
        defaultValue={params}
      />
      <Card
        headerType={1}
        title="攻击源分布"
        onMore={setTrue}
        loading={loading}
        tabsProps={{
          items: [
            {
              key: "1",
              label: "境内",
            },
            {
              key: "2",
              label: "境外",
            },
          ],
          activeKey,
          onChange: setActiveKey,
        }}
      >
        <div className="mt-1">
          <List
            width={452}
            height={236}
            data={list}
            size={5}
            // onClick={(record: API.Situation.AttackSourceDto) => {
            //   if (activeKey === "1") {
            //     setParams({
            //       srcProvince: record.region,
            //     });
            //   } else if (activeKey === "2") {
            //     setParams({
            //       srcCountry: record.region,
            //     });
            //   }
            //   detailSetTrue();
            // }}
            columns={columns}
            rowKey="id"
          />
        </div>
      </Card>
    </div>
  );
}
export default AttackDistribution;
