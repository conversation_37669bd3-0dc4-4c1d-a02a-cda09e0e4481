import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import synergyService from "@/service/synergyService";
import { useMemo } from "react";

function LoopholeInfo() {
  const { startTime, endTime } = useTime();

  const columns: TableProps["columns"] = [
    {
      title: "漏洞名称",
      dataIndex: "vulName",
      key: "vulName",
    },
    {
      title: "漏洞类型",
      dataIndex: "vulTypeName",
      key: "vulTypeName",
      width: 100,
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { loading, data } = useRequest(
    () =>
      synergyService.vulInfoList({
        startTime,
        endTime,
        page: 1,
        size: 20,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () => data?.vulInfoListDtoList ?? [],
    [data?.vulInfoListDtoList]
  );

  return (
    <div className="absolute top-[436px] right-[46px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} loading={loading} title="漏洞信息" onMore={setTrue}>
        <List width={440} height={238} data={list} size={5} columns={columns} />
      </Card>
    </div>
  );
}
export default LoopholeInfo;
