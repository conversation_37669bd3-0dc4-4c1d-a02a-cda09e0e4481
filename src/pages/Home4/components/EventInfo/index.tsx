import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import { Pagination } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import synergyService from "@/service/synergyService";
import { useMemo, useState } from "react";

function EventInfo() {
  const { startTime, endTime } = useTime();

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const columns: TableProps["columns"] = [
    {
      title: "事件名称",
      dataIndex: "eventName",
      key: "eventName",
    },
    {
      title:"涉事单位",
      dataIndex:"involved",
      key:"involved"
    },
    {
      title: "事件类型",
      dataIndex: "eventType",
      key: "eventType",
    },
    {
      title: "事件等级",
      dataIndex: "eventLevel",
      key: "eventLevel",
      width: 100,
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { loading, data } = useRequest(
    () =>
      synergyService.eventInfoList({
        startTime,
        endTime,
        page: current,
        size: pageSize,
      }),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
    }
  );

  const list = useMemo(
    () => data?.eventInfoListDtoLit ?? [],
    [data?.eventInfoListDtoLit]
  );

  const total = useMemo(
    () => data?.total ?? 0,
    [data?.total]
  );

  return (
    <div className="absolute top-[742px] right-[46px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="事件信息" loading={loading} onMore={setTrue}>
        <List width={440} height={238} data={list} size={pageSize} columns={columns} />
        {total > pageSize && (
          <div className="flex justify-end items-center">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, size) => {
                setCurrent(page);
                setPageSize(size);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}
export default EventInfo;
