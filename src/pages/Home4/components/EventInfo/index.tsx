import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import synergyService from "@/service/synergyService";
import { useMemo } from "react";

function EventInfo() {
  const { startTime, endTime } = useTime();

  const columns: TableProps["columns"] = [
    {
      title: "事件名称",
      dataIndex: "eventName",
      key: "eventName",
    },
    {
      title: "事件类型",
      dataIndex: "eventType",
      key: "eventType",
    },
    {
      title: "事件等级",
      dataIndex: "eventLevel",
      key: "eventLevel",
      width: 100,
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { loading, data } = useRequest(
    () =>
      synergyService.eventInfoList({
        startTime,
        endTime,
        page: 1,
        size: 20,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () => data?.eventInfoListDtoLit ?? [],
    [data?.eventInfoListDtoLit]
  );

  return (
    <div className="absolute top-[742px] right-[46px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="事件信息" loading={loading} onMore={setTrue}>
        <List width={440} height={238} data={list} size={5} columns={columns} />
      </Card>
    </div>
  );
}
export default EventInfo;
