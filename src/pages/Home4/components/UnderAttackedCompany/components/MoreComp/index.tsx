import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import synergyService from "@/service/synergyService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      title: "排名",
      key: "dataIndex",
      dataIndex: "dataIndex",
      width: 100,
      align: "center",
    },
    {
      title: "单位/系统名称",
      dataIndex: "unitName",
      key: "unitName",
      width: 400,
    },
    {
      title: "受攻击IP数",
      dataIndex: "attackedIpNum",
      key: "attackedIpNum",
    },
    {
      title: "攻击IP数",
      dataIndex: "attackIpNum",
      key: "attackIpNum",
    },
    {
      title: "攻击所属国家数",
      dataIndex: "attackedCountryNum",
      key: "attackedCountryNum",
    },
    {
      title: "告警次数",
      dataIndex: "attackNum",
      key: "attackNum",
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await synergyService.attackedUniNameList({
      startTime,
      endTime,
      ...params,
    });
    return {
      data: res.attackedUniNameDtoList,
      total: res.total,
    };
  };

  return (
    <ProModal type={2} title="受攻击单位列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
