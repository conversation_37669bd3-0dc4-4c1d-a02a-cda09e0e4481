import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useRequest } from "ahooks";
import { useTime } from "@/store/useTime";
import synergyService from "@/service/synergyService";
import { useMemo } from "react";

function UnderAttackedCompany() {
  const columns: TableProps["columns"] = [
    {
      title: "排名",
      key: "dataIndex",
      dataIndex: "dataIndex",
      width: 70,
      align: "center",
    },
    {
      title: "单位/系统名称",
      dataIndex: "unitName",
      key: "unitName",
      width: 280,
    },
    {
      title: "受攻击IP数",
      dataIndex: "attackedIpNum",
      key: "attackedIpNum",
    },
    {
      title: "攻击IP数",
      dataIndex: "attackIpNum",
      key: "attackIpNum",
    },
    {
      title: "攻击所属国家数",
      dataIndex: "attackedCountryNum",
      key: "attackedCountryNum",
      width:140
    },
    {
      title: "告警次数",
      dataIndex: "attackNum",
      key: "attackNum",
    },
  ];

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      synergyService.attackedUniNameList({
        startTime,
        endTime,
        page: 1,
        size: 10,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () => data?.attackedUniNameDtoList ?? [],
    [data?.attackedUniNameDtoList]
  );

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  return (
    <div className="absolute top-[130px] right-[48px]">
      <MoreComp key={visibleKey} open={open} onCancel={setFalse} />

      <Card
        headerType={4}
        title="受攻击单位 Top10"
        onMore={setTrue}
        loading={loading}
      >
        <div className="mt-[-2px]">
          <List
            width={848}
            height={238}
            data={list}
            size={5}
            columns={columns}
          />
        </div>
      </Card>
    </div>
  );
}
export default UnderAttackedCompany;
