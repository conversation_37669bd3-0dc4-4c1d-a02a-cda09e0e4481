import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import dictInfo from "@/dictInfo";
import synergyService from "@/service/synergyService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns<API.Synergy.ThreatIpListDto>[] = [
    {
      dataIndex: "dataIndex",
      title: "序号",
      align: "center",
      width: 100,
    },
    {
      dataIndex: "threatIp",
      title: "单位名称",
    },
    {
      dataIndex: "threatLevel",
      title: "威胁等级",
    },
    {
      dataIndex: "threatType",
      title: "主要威胁类型",
    },
    {
      dataIndex: "findTime",
      title: "最早发现时间",
    },
    {
      dataIndex: "dealResult",
      title: "处置结果",
      width: 140,
      render: (_, record) => {
        const item = dictInfo.dealResult.find(
          (val) => val.value === record.dealResult
        );
        return item?.label;
      },
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await synergyService.threatInfoList({
      ...params,
      startTime,
      endTime,
    });

    return {
      data: res.threatIpListDtoList ?? [],
      total: res.total,
    };
  };

  return (
    <ProModal type={2} title="威胁IP列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
