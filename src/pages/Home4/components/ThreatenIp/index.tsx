import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import type { DealResultEnum } from "@/enum";
import dictInfo from "@/dictInfo";
import { useRequest } from "ahooks";
import synergyService from "@/service/synergyService";
import { useMemo } from "react";

function ThreatenIp() {
  const { startTime, endTime } = useTime();

  const columns: TableProps["columns"] = [
    {
      title: "威胁IP",
      dataIndex: "threatIp",
      key: "threatIp",
    },
    {
      title: "威胁等级",
      dataIndex: "threatLevel",
      key: "threatLevel",
    },
    {
      title: "处置结果",
      dataIndex: "dealResult",
      key: "dealResult",
      width: 100,
      render: (value: DealResultEnum) => {
        const item = dictInfo.dealResult.find((val) => val.value === value);
        return item?.label;
      },
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { loading, data } = useRequest(
    () =>
      synergyService.threatInfoList({
        startTime,
        endTime,
        page: 1,
        size: 20,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () => data?.threatIpListDtoList ?? [],
    [data?.threatIpListDtoList]
  );

  return (
    <div className="absolute top-[130px] right-[46px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="威胁IP" loading={loading} onMore={setTrue}>
        <List width={440} height={238} data={list} size={5} columns={columns} />
      </Card>
    </div>
  );
}
export default ThreatenIp;
