import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import { Pagination } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import type { DealResultEnum } from "@/enum";
import dictInfo from "@/dictInfo";
import { useRequest } from "ahooks";
import synergyService from "@/service/synergyService";
import { useMemo, useState } from "react";

function ThreatenIp() {
  const { startTime, endTime } = useTime();

  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  const columns: TableProps["columns"] = [
    {
      title: "威胁IP",
      dataIndex: "threatIp",
      key: "threatIp",
    },
    {
      title: "威胁等级",
      dataIndex: "threatLevel",
      key: "threatLevel",
    },
    {
      title: "处置结果",
      dataIndex: "dealResult",
      key: "dealResult",
      width: 100,
      render: (value: DealResultEnum) => {
        const item = dictInfo.dealResult.find((val) => val.value === value);
        return item?.label;
      },
    },
  ];

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { loading, data } = useRequest(
    () =>
      synergyService.threatInfoList({
        startTime,
        endTime,
        page: current,
        size: pageSize,
      }),
    {
      refreshDeps: [startTime, endTime, current, pageSize],
    }
  );

  const list = useMemo(
    () => data?.threatIpListDtoList ?? [],
    [data?.threatIpListDtoList]
  );

  const total = useMemo(
    () => data?.total ?? 0,
    [data?.total]
  );

  return (
    <div className="absolute top-[130px] right-[46px]">
      <MoreComp open={open} onCancel={setFalse} key={visibleKey} />
      <Card headerType={1} title="威胁IP" loading={loading} onMore={setTrue}>
        <List width={440} height={238} data={list} size={pageSize} columns={columns} />
        {total > pageSize && (
          <div className="flex justify-end items-center">
            <Pagination
              size="small"
              total={total}
              current={current}
              pageSize={pageSize}
              onChange={(page, size) => {
                setCurrent(page);
                setPageSize(size);
              }}
              showSizeChanger={false}
              showTotal={(total: number) => <span>共 {total} 条记录</span>}
            />
          </div>
        )}
      </Card>
    </div>
  );
}
export default ThreatenIp;
