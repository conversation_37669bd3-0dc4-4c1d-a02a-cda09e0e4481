// import BgImage from "@/components/BgImage";
// import bg from "@/assets/0/chinaBg.webp";
import * as THREE from "three";
import { Canvas } from "@react-three/fiber";
import ScaleCanvas from "../EarthMap/components/ScaleCanvas";
import {
  OrbitControls,
  PerspectiveCamera,
  useProgress,
  // Stats,
} from "@react-three/drei";
import BasicMap from "./components/BasicMap";
import SubComponent from "./components/SubComponent";
import Container from "./container";
import { useMemo } from "react";
import React from "react";
import { Spin } from "antd";

function ChinaMap() {
  const width = 1920;
  const height = 1080;

  const progress = useProgress();

  const { loading } = Container.useContainer();

  const spinning = useMemo(() => progress.progress < 100, [progress.progress]);

  return (
    <React.Fragment>
      {/* <BgImage
        url={bg}
        className="absolute x-centered w-[1920px] h-[1080px] pointer-events-none top-[20px]"
      /> */}
      <Canvas
        gl={{
          //  设备像素比 不同硬件设备的屏幕window.devicePixelRatio的值可能不同 设置是为了适应不同的硬件设备屏幕 min是为了防止设备像素比过大导致渲染器内存溢出
          pixelRatio: Math.min(window.devicePixelRatio, 2),
          // 色调映射
          toneMapping: THREE.NoToneMapping,
        }}
        className="absolute x-centered top-[50px]"
        style={{
          pointerEvents: "auto",
          userSelect: "none",
          opacity: spinning ? 0 : 1,
        }}
      >
        <ScaleCanvas width={width} height={height} />
        <PerspectiveCamera
          near={1}
          far={1000}
          makeDefault
          up={[0, 1, 0]}
          fov={48}
          position={[5.549160639145102, 300.0007435701363, 6.826569595770394]}
        />
        <group rotation={[-Math.PI / 1.6, 0, 0]}>
          {/* 地图 */}
          <BasicMap />
          <SubComponent />
        </group>
        <ambientLight color={"#FFFFFF"} intensity={3} />
        <OrbitControls
          // enabled={false}
          target={
            new THREE.Vector3(
              5.549156030361328,
              0.0007435702872999797,
              6.826269617837598
            )
          }
          makeDefault
          enableDamping={false}
          enablePan={false}
          enableRotate={false}
          enableZoom={false}
          zoomSpeed={0.2}
          maxZoom={0.1}
        />
        {/* <Stats /> */}
      </Canvas>
      <Spin
        spinning={spinning || loading}
        className="centered z-[100] top-[100px]"
      />
    </React.Fragment>
  );
}
export default () => {
  return (
    <Container.Provider>
      <ChinaMap />
    </Container.Provider>
  );
};
