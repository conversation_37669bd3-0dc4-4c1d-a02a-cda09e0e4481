import Container from "../../container";
import FlyLine from "./components/FlyLine";
import * as THREE from "three";
import InfoPanel from "./components/InfoPanel";
import EndPoint from "./components/EndPoint";
import mapBg from "@/assets/0/chinaBg.png";
import { useTexture } from "@react-three/drei";

function SubComponent() {
  const { depth, subGroupRef, mapPosition } = Container.useContainer();

  const subPostion = new THREE.Vector3(mapPosition.x, mapPosition.y, 0);

  const texture = useTexture(mapBg);

  // 原始宽高比是
  const ratio = 1920 / 1100;
  const width = 486;

  return (
    <group position={[0, 0, depth]} ref={subGroupRef}>
      {/* 飞线 */}
      <group
        position={subPostion}
        userData={{
          needInteractive: true,
        }}
      >
        <FlyLine />
      </group>
      <group
        position={subPostion}
        userData={{
          needInteractive: true,
        }}
      >
        <InfoPanel />
        <EndPoint />
      </group>
      <group position={[0, 0, -depth]}>
        <group renderOrder={-1}>
          <mesh position={[-12, 6, 0]} rotation={[0, -0, 0]}>
            <planeGeometry args={[width, width / ratio]} attach="geometry" />
            <meshBasicMaterial transparent map={texture} />
          </mesh>
        </group>
      </group>
    </group>
  );
}
export default SubComponent;
