import pointImage from "@/assets/0/pointCircle.png";
import { lngLatToXYZ } from "@/utils";
import { Html, useTexture } from "@react-three/drei";
import * as THREE from "three";
import { useMemo, useRef } from "react";
import { useFrame } from "@react-three/fiber";
import Container from "../../../../container";
import React from "react";

function EndPoint() {
  const [pointTexture] = useTexture([pointImage]);

  const endPoint = {
    lng: 113.280637,
    lat: 23.125178,
  };

  const { lng, lat } = endPoint;

  const { geoProjection } = Container.useContainer();

  const [x, y] = geoProjection([lng, lat]);

  const meshRef = useRef<THREE.Mesh>(null);

  const scaleRef = useRef(1.0);

  useFrame(() => {
    if (meshRef.current) {
      scaleRef.current += 0.007;

      const scale = 8 * scaleRef.current;
      meshRef.current.scale.set(scale, scale, scale);

      // Update opacity
      const material = meshRef.current.material;
      if (material instanceof THREE.MeshBasicMaterial) {
        if (scaleRef.current <= 1.5) {
          material.opacity = (scaleRef.current - 1) * 2;
        } else if (scaleRef.current > 1.5 && scaleRef.current <= 2) {
          material.opacity = 1 - (scaleRef.current - 1.5) * 2;
        } else {
          scaleRef.current = 1.0;
        }
      }
    }
  });

  return (
    <React.Fragment>
      <mesh ref={meshRef} scale={[1, 1, 1]} position={[x, -y, 5]}>
        <planeGeometry args={[0.8, 0.8]} />
        <meshBasicMaterial
          // color={new THREE.Color("yellow")}
          map={pointTexture}
          transparent
          depthWrite={false}
          // blending={THREE.AdditiveBlending}
        />
      </mesh>
      <group position={[x, -y, 1.1]}>
        <Html
          className="flex flex-col items-center w-max"
          position={[0, 0, 0]}
          transform
          sprite
          style={{
            transform: "translate(0,22px)",
          }}
          scale={[7, 7, 7]}
        >
          <div
            className="text-[rgba(255,255,255,0.8)] text-[22px]"
            style={{
              fontFamily: "YouSheBiaoTiHei-2",
            }}
          >
            广东
          </div>
        </Html>
      </group>
    </React.Fragment>
  );
}
export default EndPoint;
