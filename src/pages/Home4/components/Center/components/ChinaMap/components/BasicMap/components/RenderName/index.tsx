import { Html } from "@react-three/drei";
import * as THREE from "three";
import Container from "../../../../container";
import { memo, useMemo } from "react";
import lodash from "lodash-es";
import { useMemoizedFn } from "ahooks";
import { useTextCommonStyle } from "@/DataV/Config/Text/hooks";
import { MAP_LEVEL } from "../../../../enum";

interface IProps {
  x: number; // x坐标
  y: number; // y坐标
  name: string; // 区域名称
  adcode: string; // 区域编码
  index: number; // 当前所在列表索引
}
function RenderName(props: IProps) {
  const { x, y, name, adcode, index } = props;
  const { config, namesRef, htmlScale } = Container.useContainer();
  const {
    styleConfig: {
      regionNameConfig: { textFormat, textBasic },
    },
  } = config;

  const labelStyle = useTextCommonStyle({
    config: textBasic,
  });

  // 计算标签的层级，基于模型的深度和基础标签层级
  const labelLevel = useMemo(() => {
    return MAP_LEVEL.BASIC_LABEL_LEVEL;
  }, []);

  // label样式处理
  const getStyleValue = useMemoizedFn((cityName: string) => {
    const formatText = lodash.keyBy(textFormat, "value");
    const format = formatText[cityName];
    const transformValue = format
      ? `translate(${format?.offsetX}px, ${format?.offsetY}px)`
      : undefined;

    return {
      ...labelStyle,
      transform: transformValue,
    } as React.CSSProperties;
  });

  const position = useMemo(() => {
    return new THREE.Vector3(x, -y, labelLevel);
  }, [x, y, labelLevel]);

  return (
    <group>
      <Html
        key={adcode ?? index}
        style={{
          ...getStyleValue(name),
        }}
        ref={(ref) => {
          namesRef.current[index] = ref!;
        }}
        name="nameHtml"
        sprite={false} // 将 Html 组件设置为精灵模式。当 sprite 为 true 时，Html 内容将始终面向摄像机，类似于 3D 场景中的精灵。
        transform // 启用三维变换
        pointerEvents="none" // 禁用指针事件
        scale={[htmlScale, htmlScale, htmlScale]} // 设置标签的缩放比例，为了确保标签在三维场景中的大小适中
        position={position} // 设置标签的位置
      >
        {name}
      </Html>
    </group>
  );
}
export default memo(RenderName);
