/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useCallback, useRef, useState } from "react";
import RenderMarkerItem from "./components/RenderMarkerItem";
import useInfoPanelData from "./hooks/useInfoPanelData";
import { isNil } from "lodash-es";
import { useRafInterval, useMemoizedFn } from "ahooks";
import Container from "../../../../container";

export interface DataItemConfig extends API.Situation.AttackChinaMapDto {
  startLng: number;
  startLat: number;
  endLng: number;
  endLat: number;
}

function SubBoundaryMapInfoPanel() {
  const { chindData } = Container.useContainer();

  const { infoPanelData } = useInfoPanelData({
    data: chindData,
  });

  const markerRef = useRef<HTMLDivElement[]>([]);
  const selectMarkerRef = useRef<number | undefined>(undefined);
  const [interval, setInterval] = useState<number | undefined>(5000);

  const onClick = useMemoizedFn((index: number) => {
    // 获取当前选中的ref
    const selectRef = markerRef.current[index];
    // 轮播初始化时可能找不到子元素
    if (selectRef) {
      // 面板选中样式替换
      selectRef.style.display = "flex";
      // 如果存在已激活则重置已激活的样式
      if (!isNil(selectMarkerRef.current)) {
        const selectPanelRef = markerRef.current[selectMarkerRef.current];
        selectPanelRef.style.display = "none";
      }
    }
    // 设置当前激活的key
    selectMarkerRef.current = index;
  });

  // 轮播index
  const showCarousel = useCallback(() => {
    if (infoPanelData) {
      const selectIndex = isNil(selectMarkerRef.current)
        ? 0
        : selectMarkerRef.current + 1;
      const newIndex = selectIndex % infoPanelData?.length;
      onClick(newIndex);
    }
  }, [infoPanelData, onClick]);

  // 轮播动画
  const clear = useRafInterval(showCarousel, interval);

  return (
    <React.Fragment>
      <group name="infoPanel">
        {infoPanelData?.map((item, index) => {
          const { x, y } = item;
          return (
            <React.Fragment key={index}>
              {x && y && (
                <RenderMarkerItem
                  key={index}
                  item={item}
                  index={index}
                  markerRef={markerRef}
                  selectMarkerRef={selectMarkerRef}
                  intervalInfo={{ interval, setInterval, clear }}
                />
              )}
            </React.Fragment>
          );
        })}
      </group>
    </React.Fragment>
  );
}

export default SubBoundaryMapInfoPanel;
