import { useMemo, memo } from "react";
import * as THREE from "three";
import Container from "../../../../container";
import React from "react";

interface IProps {
  adcode: number; // 区域编码
  name: string; // 区域名称
  shape: THREE.Shape;
}
function RenderShape(props: IProps) {
  const { shape } = props;
  const { meshMaterial, handleCalcUv2, sideMaterial, depth } =
    Container.useContainer();

  const geometryValue = useMemo(() => {
    // ExtrudeGeometry 用于从一个或多个 THREE.Shape 对象创建一个挤出的几何体，即在二维形状的基础上添加深度。
    const geometry = new THREE.ExtrudeGeometry(shape, {
      depth, // 深度
      bevelEnabled: false, // 是否斜角
    });
    handleCalcUv2(geometry);
    return geometry;
  }, [depth, handleCalcUv2, shape]);

  const shapeMesh = useMemo(() => {
    const topMaterial = meshMaterial.clone();
    const mesh = new THREE.Mesh(geometryValue, [topMaterial, sideMaterial]);
    // mesh.receiveShadow = true;
    return mesh;
  }, [geometryValue, meshMaterial, sideMaterial]);

  // 最外层的mesh 用来处理悬浮和点击的事件操作
  const hoverClickMesh = useMemo(() => {
    const material = new THREE.MeshBasicMaterial({
      opacity: 0,
      transparent: true,
      visible: false,
    });
    const sideMaterial = material.clone();
    const mesh = new THREE.Mesh(geometryValue, [material, sideMaterial]);
    mesh.name = "meshShape";
    return mesh;
  }, [geometryValue]);

  return (
    <React.Fragment>
      <primitive object={shapeMesh} />
      {/* 专门用这个来处理点击和悬浮的顶面混合颜色渲染 这样混合效果最佳 并且能保证透明度为0时 悬浮也有效果 */}
      <primitive object={hoverClickMesh} />
    </React.Fragment>
  );
}
export default memo(RenderShape);
