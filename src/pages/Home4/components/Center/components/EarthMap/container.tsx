import situationService from "@/service/situationService";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import { useMemo } from "react";
import worldJson from "@/assets/json/world.json";
import { createContainer } from "unstated-next";

function useContainer() {
  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () => situationService.attackWorldMap({ startTime, endTime }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const worldMapData = useMemo(() => {
    const map = new Map<string, number[]>();
    worldJson.features.forEach((val) => {
      map.set(val.properties.name, val.properties.centroid);
    });
    return map;
  }, []);

  const list = useMemo(() => {
    // const value = [
    //   {
    //     attackCount:10,
    //     srcName:"新加坡"
    //   },
    //   {
    //     attackCount: 10,
    //     srcName: "尼日尔",
    //   },
    //   {
    //     attackCount: 10,
    //     srcName: "波兰",
    //   },
    // ];
    return (
      data?.attackWorldMapDtoList?.map((val) => {
        const [startLng, startLat] = worldMapData.get(val.srcName) ?? [0, 0];
        return {
          startLng,
          startLat,
          endLng: 113.280637,
          endLat: 23.125178,
          name: val.srcName,
          code: val.srcCode,
        };
      }) ?? []
    );
  }, [data?.attackWorldMapDtoList, worldMapData]);

  return {
    worldMapData,
    list,
    loading,
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
