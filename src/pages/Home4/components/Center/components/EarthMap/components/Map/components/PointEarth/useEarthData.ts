import { useEffect, useRef, useState } from "react";
function useEarthData(url: string) {
  const [value, setValue] = useState<ImageData>();
  const [loading, setLoading] = useState(true);
  const imageRef = useRef<HTMLImageElement>();

  useEffect(() => {
    const imageDom = document.createElement("img");
    imageDom.src = url;
    imageRef.current = imageDom;
    imageDom.onload = () => {
      const { width, height } = imageDom;
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      canvas.width = width;
      canvas.height = height;
      context?.drawImage(imageDom, 0, 0, width, height);
      const imageData = context?.getImageData(0, 0, width, height);
      setValue(imageData);
      setLoading(false);
    };
  }, [url]);

  // const isLandByUV = (c: number, f: number) => {
  //   // value 是图片的context?.getImageData(0, 0, width, height);
  //   if (!imageRef.current) {
  //     return false;
  //   }
  //   const n = parseInt((imageRef.current.width * c).toString()); // 根据横纵百分比计算图象坐标系中的坐标
  //   const o = parseInt((imageRef.current.height * f).toString()); // 根据横纵百分比计算图象坐标系中的坐标
  //   return value?.data[4 * (o * value.width + n)] === 255; // 查找底图中对应像素点的rgba值并判断
  // };

  const isLandByUV = (lon: number, lat: number) => {
    if (!imageRef.current || !value) {
      return false;
    }

    const lonNormalized = (lon + 180) / 360;
    const latNormalized = (lat + 90) / 180;

    const n = Math.floor(imageRef.current.width * lonNormalized);
    const o = Math.floor(imageRef.current.height * (1 - latNormalized)); // 注意我们这里使用了1- latNormalized，因为图像的y轴是从上到下的，而纬度是从下到上的

    return value.data[4 * (o * value.width + n)] === 255;
  };

  return {
    isLandByUV,
    earthImgData: value,
    loading,
  };
}
export default useEarthData;
