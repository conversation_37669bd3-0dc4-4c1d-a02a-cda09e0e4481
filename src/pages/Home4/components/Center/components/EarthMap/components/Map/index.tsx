import React, { useMemo, useRef, useState } from "react";
import earthMap from "@/assets/0/earthMap4.webp";
import emissiveMap from "@/assets/0/emissiveMap.webp";
import earthClouds from "@/assets/0/earthClouds.webp";
import { THREE_EARTH_RADIUS } from "../../config";
import Atmosphere from "../Atmosphere";
import SwitchScene from "../SwitchScene";
import FlyLine from "../FlyLine";
import EndPoint from "./components/EndPoint";
import * as THREE from "three";
import PointEarth from "./components/PointEarth";

function Map() {
  const radius = THREE_EARTH_RADIUS;

  // const [basicTexture, emissiveTexture, cloudsTexture] = useTexture([
  //   earthMap,
  //   emissiveMap,
  //   earthClouds,
  // ]);

  const [loadingEnd, setLoadingEnd] = useState(false);

  const basicTexture = useMemo(
    () =>
      new THREE.TextureLoader().load(earthMap, () => {
        setLoadingEnd(true);
      }),
    []
  );

  const emissiveTexture = useMemo(
    () => new THREE.TextureLoader().load(emissiveMap),
    []
  );

  const cloudsTexture = useMemo(
    () => new THREE.TextureLoader().load(earthClouds),
    []
  );

  const earthRef = useRef<THREE.Mesh>(null);

  if (!loadingEnd) {
    return null;
  }

  return (
    <React.Fragment>
     

      <Atmosphere />

      <mesh>
          <sphereGeometry args={[radius - 1, 64, 64]} />
          <meshPhongMaterial
            color="#fff"
            opacity={0.0}
            transparent
            side={THREE.FrontSide}
          />
        </mesh>

      <mesh renderOrder={10}>
        <PointEarth radius={radius + 1} />
      </mesh>

     

      <group>
        <mesh ref={earthRef}>
          {/* 使用球体几何体创建地球，参数分别为半径、水平和垂直的分段数 */}
          <sphereGeometry args={[radius, 64, 64]} />
          <meshStandardMaterial
            map={basicTexture}
            opacity={0.8}
            transparent
            emissiveMap={emissiveTexture}
            emissiveIntensity={0}
            depthTest
            depthWrite
            emissive={new THREE.Color("#fff")}
          />
        </mesh>
      </group>


      <group>
        <FlyLine meshRef={earthRef} />
        <EndPoint earthRef={earthRef} />
      </group>

      <group>
        {/* 云层 */}
        <mesh>
          <sphereGeometry args={[radius - 1, 64, 64]} />
          <meshPhongMaterial color="#fff" opacity={0.0} transparent />
        </mesh>
        <mesh renderOrder={1}>
          <sphereGeometry args={[radius + 10, 64, 64]} />
          <meshStandardMaterial
            color={new THREE.Color("#fff")}
            opacity={0.3}
            alphaMap={cloudsTexture}
            depthWrite={false}
            depthTest={true}
            transparent
            // side={THREE.DoubleSide}
          />
        </mesh>
      </group>

      <SwitchScene />
    </React.Fragment>
  );
}
export default Map;
