import pointImage from "@/assets/0/pointCircle.png";
import { lngLatToXYZ } from "@/utils";
import { Html, useTexture } from "@react-three/drei";
import * as THREE from "three";
import { THREE_EARTH_RADIUS } from "../../../../config";
import { useMemo, useRef } from "react";
import { useFrame } from "@react-three/fiber";

interface IProps {
  earthRef: React.RefObject<THREE.Object3D<THREE.Object3DEventMap>>;
}
function EndPoint(props: IProps) {
  const { earthRef } = props;
  const [pointTexture] = useTexture([pointImage]);

  const radius = THREE_EARTH_RADIUS;

  const endPoint = {
    lng: 113.280637,
    lat: 23.125178,
  };

  const { lng, lat } = endPoint;

  const { x, y, z } = lngLatToXYZ(lng, lat, radius);

  const quaternion = useMemo(() => {
    const coordVec3 = new THREE.Vector3(x, y, z).normalize();
    const meshNormal = new THREE.Vector3(0, 0, 1);
    const value = new THREE.Quaternion();
    value.setFromUnitVectors(meshNormal, coordVec3);
    return value;
  }, [x, y, z]);

  const meshRef = useRef<THREE.Mesh>(null);

  const scaleRef = useRef(1.0);

  useFrame(() => {
    if (meshRef.current) {
      scaleRef.current += 0.007;

      const scale = 8 * scaleRef.current;
      meshRef.current.scale.set(scale, scale, scale);

      // Update opacity
      const material = meshRef.current.material;
      if (material instanceof THREE.MeshBasicMaterial) {
        if (scaleRef.current <= 1.5) {
          material.opacity = (scaleRef.current - 1) * 2;
        } else if (scaleRef.current > 1.5 && scaleRef.current <= 2) {
          material.opacity = 1 - (scaleRef.current - 1.5) * 2;
        } else {
          scaleRef.current = 1.0;
        }
      }
    }
  });

  const { x: nameX, y: nameY, z: nameZ } = lngLatToXYZ(lng, lat, radius + 0.2);

  return (
    <group>
      <mesh
        ref={meshRef}
        scale={[10, 10, 10]}
        position={[x, y, z]}
        quaternion={quaternion}
      >
        <planeGeometry args={[1.5, 1.5]} />
        <meshBasicMaterial
          // color={new THREE.Color("yellow")}
          map={pointTexture}
          transparent
          depthWrite={false}
          // blending={THREE.AdditiveBlending}
        />
      </mesh>
      <group position={[nameX, nameY, nameZ]}>
        <Html
          className="flex flex-col items-center w-max"
          position={[0, 0, 0]}
          transform
          sprite
          style={{
            transform: "translate(0,42px)",
          }}
          scale={[7, 7, 7]}
          occlude={[earthRef]}
        >
          <div
            className="text-[rgba(255,255,255,0.8)] text-[40px]"
            style={{
              fontFamily: "YouSheBiaoTiHei-2",
            }}
          >
            广东
          </div>
        </Html>
      </group>
    </group>
  );
}
export default EndPoint;
