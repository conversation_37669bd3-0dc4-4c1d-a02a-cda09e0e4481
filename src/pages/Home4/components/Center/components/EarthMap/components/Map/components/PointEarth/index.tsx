import { useMemo } from "react";
import * as THREE from "three";
import { useTexture } from "@react-three/drei";
import React from "react";
import dotImage from "@/assets/map/dot.png";
import earthImage from "@/assets/map/earthSpec.jpg";
import useEarthData from "./useEarthData";

function PointEarth(props: { radius: number }) {
  const { radius } = props;

  const pointColor = "#0891EA";
  const pointSize = 4;
  const pointOpacity = 0.3;
  const pointCount = 100;

  const dotTexture = useTexture(dotImage);
  const { loading, isLandByUV } = useEarthData(earthImage);

  const material = useMemo(() => {
    return new THREE.PointsMaterial({
      size: pointSize,
      color: new THREE.Color(pointColor),
      side: THREE.DoubleSide,
      map: dotTexture,
      opacity: pointOpacity,
      transparent: true,
      depthWrite: false,
      depthTest: true,
      // blending: THREE.AdditiveBlending,
      sizeAttenuation: true,
    });
  }, [dotTexture, pointColor, pointSize, pointOpacity]);

  const pointGeometry = useMemo(() => {
    if (loading) {
      return null;
    }

    const bufferGeom = new THREE.BufferGeometry();
    const positions = [] as number[];
    const spherical = new THREE.Spherical();
    spherical.radius = radius;

    // 点的位置及数据

    const DEG_TO_RAD = Math.PI / 180;
    const CIRCUMFERENCE = 30;

    function getPointFromLatLon(
      lat,
      lon,
      radius,
      resultVector = new THREE.Vector3()
    ) {
      const phi = (90 - lat) * DEG_TO_RAD;
      const theta = (lon + 180) * DEG_TO_RAD;

      return resultVector.set(
        -radius * Math.sin(phi) * Math.cos(theta),
        radius * Math.cos(phi),
        radius * Math.sin(phi) * Math.sin(theta)
      );
    }

    for (let lat = -90; lat <= 90; lat += 180 / pointCount) {
      const segmentLength =
        Math.cos(Math.abs(lat) * DEG_TO_RAD) * CIRCUMFERENCE * Math.PI * 2 * 2;

      for (let i = 0; i < segmentLength; i++) {
        const lon = (360 * i) / segmentLength - 180;
        const point = getPointFromLatLon(lat, lon, radius);

        if (isLandByUV(lon, lat)) {
          positions.push(point.x, point.y, point.z);
        }
      }
    }

    bufferGeom.setAttribute(
      "position",
      new THREE.BufferAttribute(new Float32Array(positions), 3)
    );
    bufferGeom.computeBoundingSphere();

    return bufferGeom;
  }, [isLandByUV, loading, radius, pointCount]);

  return (
    <React.Fragment>
      <mesh>
        {pointGeometry && (
          <points
            renderOrder={99} // 设置一个较高的值
            args={[pointGeometry, material]}
          />
        )}
      </mesh>
    </React.Fragment>
  );
}

export default PointEarth;
