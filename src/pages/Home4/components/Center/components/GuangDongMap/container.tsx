import { createContainer } from "unstated-next";
import mapJson from "@/assets/json/440000.json";
import { ProjectionTypeEnum } from "@/enum";
import useMapData from "./hooks/useMapData";
import useShape from "./hooks/useShape";
import mapBgUrl from "@/assets/4/mapTexture.webp";
import type * as THREE from "three";
import { useMemo, useRef } from "react";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import situationService from "@/service/situationService";

export interface MapConfig {
  /**
   * 层级-地图的拉长长度
   */
  level?: number;

  /**
   * 墨卡托投影转换
   */
  geoProjection: (args: [number, number]) => [number, number];

  /**
   * html缩放比例
   */
  htmlScale: number;

  /**
   * 获取省份中心点
   */
  getProvinceCenter: (adcode: number, name?: string) => number[] | undefined;
}

function useContainer() {
  const projectionType = ProjectionTypeEnum.Mercator;

  const depth = 6;

  const subGroupRef = useRef<THREE.Group>(null);

  const infoPanelRefs = useRef<HTMLDivElement[]>([]);

  const {
    featureData,
    mapPosition,
    handleCalcUv2,
    geoProjection,
    getProvinceCenter,
  } = useMapData({
    projectionType,
    dataJson: JSON.stringify(mapJson),
    depth,
  });

  const mapBgMargin = useMemo(
    () => ({
      left: -0.002,
      right: -0.0,
      top: -0.144,
      bottom: -0.09,
    }),
    []
  );

  const { meshMaterial, sideMaterial } = useShape({
    mapBgUrl,
    mapBgMargin,
  });

  const { endTime, startTime } = useTime();

  const { data,loading:dataLoading } = useRequest(
    () => situationService.attackGuangDongMap({ startTime, endTime }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const guangDongMapData = useMemo(() => {
    const map = new Map<number, API.Situation.AttackGuangDongMapDto>();
    data?.attackGuangDongMapDtoList?.forEach((val) => {
      map.set(val.dstCode, val);
    });
    return map;
  }, [data?.attackGuangDongMapDtoList]);

  return {
    featureData,
    mapPosition,
    meshMaterial,
    handleCalcUv2,
    sideMaterial,
    depth,
    subGroupRef,
    geoProjection,
    getProvinceCenter,
    infoPanelRefs,
    guangDongMapData,
    dataLoading
  };
}

const Container = createContainer(useContainer);

export type ContainerType = typeof useContainer;

export { useContainer };

export default Container;
