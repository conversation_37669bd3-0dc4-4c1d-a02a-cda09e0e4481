import { Shape } from "three";
import { geoMercator, geoEquirectangular } from "d3-geo";
import { useCallback, useMemo, useRef } from "react";
import type { AllGeoJSON } from "@turf/turf";
import { bbox, centroid } from "@turf/turf";
import { MapLevelEnum, ProjectionTypeEnum } from "@/enum";
import * as THREE from "three";
import { parseJSON, transFromGeoJSON, transformProvinceName } from "@/utils";
import type { GeoMercatorParams } from "@/typings";

interface ShapeItem {
  shape: Shape;
  points: number[];
  key: string;
}
interface IProps {
  dataJson?: string;
  projectionType: ProjectionTypeEnum; // 投影方式
  depth: number; // 地图深度
}
function useMapData(props: IProps) {
  const { dataJson, projectionType, depth } = props;

  const mapData = useMemo(
    () => transFromGeoJSON(parseJSON(dataJson) as GeoMercatorParams["data"]),
    [dataJson]
  );

  const mercatorScale = useMemo(() => {
    // 获取包围盒
    const [minLng, minLat, maxLng, maxLat] = bbox(mapData as AllGeoJSON);
    // 计算包围盒尺寸
    const bboxWidth = maxLng - minLng;
    const bboxHeight = maxLat - minLat;

    // 假设 parentBoxSize 是父容器（如中国地图）的尺寸
    // 下面的值是通过bbox(mapData); 把mapData设置为中国地图的数据后计算得到的 所以直接写死
    const parentBoxWidth = 61.59331500000002;
    const parentBoxHeight = 50.16610713;

    // 计算缩放比例
    const scaleX = parentBoxWidth / bboxWidth;
    const scaleY = parentBoxHeight / bboxHeight;
    // 默认基础缩放比例 190 是为了初始化比例好看
    const basicScale = 190;
    const scale = Math.min(scaleX, scaleY) * basicScale;
    return scale;
  }, [mapData]);

  const mercatorCenter = useMemo(() => {
    const value = mapData
      ? centroid(mapData as AllGeoJSON).geometry.coordinates
      : [0, 0];
    return value as [number, number];
  }, [mapData]);

  const projectionTool = useMemo(() => {
    // 地图的投影方式
    // geoEquirectangular - WGS84 等距离投影
    // geoMercator - 墨卡托投影
    const geoProjectionTranslate = [0, 0] as [number, number];
    const projectionFn =
      projectionType === ProjectionTypeEnum.WGS84
        ? geoEquirectangular
        : geoMercator;
    return projectionFn()
      .center(mercatorCenter)
      .scale(mercatorScale)
      .translate(geoProjectionTranslate);
  }, [mercatorCenter, mercatorScale, projectionType]);

  const geoProjection = useCallback(
    (args: [number, number]) => {
      const value = projectionTool(args)!;
      return value;
    },
    [projectionTool]
  );

  // 用于地图颜色贴图 uv的处理
  const boundingBoxData = useMemo(() => {
    if (mapData === undefined) return undefined;
    // 使用 @turf/bbox 计算地图数据的包围盒
    const [minLng, minLat, maxLng, maxLat] = bbox(mapData as AllGeoJSON);

    // 将包围盒的角点坐标转换为墨卡托投影坐标
    const [vector1X, vector1Y] = geoProjection([minLng, minLat]);
    const [vector2X, vector2Y] = geoProjection([maxLng, maxLat]);
    const vector1 = new THREE.Vector3(vector1X, -vector1Y, 0);
    const vector2 = new THREE.Vector3(vector2X, -vector2Y, 0);

    // 创建一个 THREE.Box3 对象来表示包围盒
    const boundingBox = new THREE.Box3();
    boundingBox.setFromPoints([vector1, vector2]);

    // 获取包围盒的尺寸
    const boxSize = new THREE.Vector3();
    const center = new THREE.Vector3();

    boundingBox.getSize(boxSize);
    boundingBox.getCenter(center);

    return {
      boxSize,
      center,
      min: boundingBox.min,
      max: boundingBox.max,
    };
  }, [mapData, geoProjection]);

  const mapPosition = useMemo(() => {
    const x = boundingBoxData?.center.x ?? 0;
    const y = boundingBoxData?.center.y ?? 0;
    return new THREE.Vector3(-x, -y, MapLevelEnum.BASIC_LEVEL);
  }, [boundingBoxData?.center.x, boundingBoxData?.center.y]);

  // 处理贴图uv
  const handleCalcUv2 = useCallback(
    (geometry: THREE.ExtrudeGeometry) => {
      if (boundingBoxData) {
        const { boxSize, min } = boundingBoxData;
        const { x: width, y: height } = boxSize;
        const { x: minX, y: minY } = min;
        const positionAttribute = geometry.attributes.position;
        const uvAttribute = geometry.attributes.uv;

        const count = geometry.groups[0].count;
        for (let i = 0; i < count; i++) {
          const x = positionAttribute.getX(i);
          const y = positionAttribute.getY(i);

          const u = (x - minX) / width;
          const v = (y - minY) / height;

          uvAttribute.setXY(i, u, v);
        }

        const normals = geometry.attributes.normal;

        // 处理侧面贴图的uv 问题
        geometry.groups.forEach((group) => {
          const extrudeDepth = depth; // 深度
          for (let i = group.start; i < group.start + group.count; i++) {
            const ny = normals.getY(i);
            const nz = normals.getZ(i);

            // 假设挤出方向是沿着 Z 轴
            if (Math.abs(nz) < 0.9) {
              // 这是侧面
              const x = positionAttribute.getX(i);
              const y = positionAttribute.getY(i);
              const z = positionAttribute.getZ(i);

              let u, v;
              if (Math.abs(ny) > 0.9) {
                // 判断法线的方向m 法线向量通常是单位向量（长度为 1）。如果一个单位向量的某个分量大于 0.9，这意味着该向量主要在该轴方向上。
                // 这个阈值可以根据需要进行调整。如果您的几何体非常规则，并且法线精确对齐轴向，您可能使用更接近 1 的值（例如 0.95 或 0.99）。相反，如果您需要更多的灵活性来处理稍微倾斜的面，您可以使用一个较小的值（例如 0.85）。
                // 侧面垂直于 Y 轴
                u = (x % extrudeDepth) / extrudeDepth;
                v = z / extrudeDepth;
              } else {
                // 侧面垂直于 X 轴
                u = (y % extrudeDepth) / extrudeDepth;
                v = z / extrudeDepth;
              }
              uvAttribute.setXY(i, u, v); // 设置侧面的 UV 坐标
            }
          }
        });

        uvAttribute.needsUpdate = true; // 标记 UV 需要更新
        geometry.computeVertexNormals(); //  重新计算顶点法线
      }
    },
    [boundingBoxData, depth]
  );

  //   创建一个缓存，用于存储已计算的 geoProjection列表的 结果 用于优化速度
  const projectionListMapRef = useRef<Map<string, [number, number][]>>(
    new Map()
  );

  // 批量处理 geoProjection 的计算
  const geoProjectionList = useCallback(
    (coordinates: [number, number][]) => {
      // projectionType不同的投影方式应该不同 防止缓存的key相同
      const key = coordinates.join(",") + projectionType;
      if (projectionListMapRef.current.get(key)) {
        return projectionListMapRef.current.get(key)!;
      }
      const value = coordinates.map((coord) => {
        return geoProjection(coord);
      });
      projectionListMapRef.current.set(key, value!);
      return value;
    },
    [geoProjection, projectionType]
  );

  const featureData = useMemo(() => {
    if (!mapData?.features) return [];
    const list = mapData.features.map((feature) => {
      const featureCentroid = centroid(feature.geometry as any).geometry
        .coordinates as [number, number];
      const [x, y] = geoProjection(featureCentroid);
      const cityPosition = {
        x,
        y,
      };
      const shapeList = feature.geometry.coordinates.flatMap<ShapeItem>(
        (multiPolygon: any, multiPolygonIndex: number) =>
          multiPolygon.map((polygon: [number, number][], index: number) => {
            const projectedPoints = geoProjectionList(polygon);
            const shape = new Shape();
            const points: number[] = [];
            projectedPoints.forEach(([x, y], index) => {
              if (index === 0) {
                shape.moveTo(x, -y);
              }
              shape.lineTo(x, -y);
              points.push(x, -y, 0);
            });
            shape.autoClose = true;
            const key = `${feature.properties?.adcode}-${multiPolygonIndex}-${index}`;
            return { shape, points, key };
          })
      );
      return { ...feature, cityPosition, shapeList };
    });
    return list;
  }, [geoProjection, geoProjectionList, mapData?.features]);

  // 获取省份中心点
  const getProvinceCenter = useCallback(
    (adcode?: number, name?: string) => {
      const item = mapData?.features.find((val) => {
        return (
          val.properties?.adcode === adcode ||
          val.properties?.name === name ||
          val.properties?.name === `${name}省` ||
          val.properties?.name === `${name}市` ||
          val.properties?.name === transformProvinceName(name)
        );
      });
      if (name === "中国") {
        return [104.199224, 36.395686];
      }
      if (item) {
        return (item.properties?.centroid || item.properties?.center) as [
          number,
          number,
        ];
      }
      return undefined;
    },
    [mapData?.features]
  );

  return {
    featureData,
    geoProjection,
    handleCalcUv2,
    getProvinceCenter,
    mapPosition,
  };
}

export default useMapData;
