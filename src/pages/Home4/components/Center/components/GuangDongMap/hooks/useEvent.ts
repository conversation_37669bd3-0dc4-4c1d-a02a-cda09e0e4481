import type { ThreeEvent } from "@react-three/fiber";
import { useMemoizedFn } from "ahooks";
import Container from "../container";
import * as THREE from "three";
import { gsap } from "gsap";
import { useRef } from "react";

interface SubOptions {
  name: string;
  adcode: string;
  zValue: number; // z轴值 抬升高度
}

function useEvent() {
  // 当前点击选中的区域 adcode
  const selectGroupRef = useRef<THREE.Object3D | undefined>();

  const { depth, subGroupRef, infoPanelRefs } = Container.useContainer();

  // 当前选中的区域的 adcode
  const getSelectAdcode = useMemoizedFn(() => {
    if (selectGroupRef.current) {
      return selectGroupRef.current?.userData.adcode as string;
    }
    return undefined;
  });

  const onLoopSubGroup = useMemoizedFn(
    (group: THREE.Object3D[], options: SubOptions) => {
      const { name, adcode, zValue } = options;
      for (const val of group) {
        // 只处理group类型
        if (val.type === "Group") {
          if (
            val.name &&
            (val.name.includes(`${name}-subGroup`) ||
              val.name.includes(`${adcode}-subGroup`))
          ) {
            gsap.to(val.position, {
              duration: 0.5,
              z: zValue,
            });
            continue;
          }
          if (val.children.length > 0) {
            // 递归
            onLoopSubGroup(val.children, options);
          }
        }
      }
    }
  );

  // 鼠标悬停
  const onPointerEnter = useMemoizedFn((event: ThreeEvent<PointerEvent>) => {
    const {
      eventObject: { userData },
      eventObject,
    } = event;
    event.stopPropagation();
    // console.log(event, eventObject, "111");
    // const a=userData.cityPosition;
    // 当前选中的区域的 adcode 如果已经选中就不再处理
    if (userData.adcode === getSelectAdcode()) return;
    // 当前选中的区域的 索引
    const index = userData?.index as number;
    // 抬升高度和是否抬升
    // 抬升的高度
    const upthrowHeight = 4;
    // 高亮配置和是否高亮
    const highLight = {
      lineColor: "#3C698F", // 顶线颜色
      lineHeight: 0, // 顶线粗细
      surfaceColor: "#39A0EB", // 顶面颜色
      surfaceOpactiy: 0.2, // 顶面透明度
    };
    const { lineColor, lineHeight, surfaceColor, surfaceOpactiy } = highLight;
    // 抬升设置
    const scaleZ = 1 + upthrowHeight / depth;
    gsap.to(eventObject.scale, {
      duration: 0.5,
      z: scaleZ,
    });

    // 处理子组件相同省份位置抬高
    if (subGroupRef.current) {
      for (const val of subGroupRef.current.children) {
        if (val.userData.needInteractive) {
          // 需要交互的子组件 needInteractive 注释见子组件列表 SubChildren
          onLoopSubGroup(val.children, {
            name: userData.name,
            adcode: userData.adcode,
            zValue: upthrowHeight,
          });
        }
      }
    }
    // 高亮设置

    for (const val of eventObject.children) {
      // shape 设置顶面颜色
      // 根据名称区分是 shape 还是 line topLine还是bottomLine
      if (val.name === "meshShape") {
        const topMaterial = (val as THREE.Mesh)
          ?.material[0] as THREE.MeshBasicMaterial;
        topMaterial.color = new THREE.Color(surfaceColor);
        topMaterial.visible = true;
        topMaterial.opacity = surfaceOpactiy;
      }
      if (val.name === "topLine") {
        const line = val.children[0] as THREE.Line;
        const lineGeometry = line.material as THREE.LineBasicMaterial;
        lineGeometry.color = new THREE.Color(lineColor);
        lineGeometry.linewidth = lineHeight;
      }
      if (val.name === "infoPanel") {
        infoPanelRefs.current[index].style.display = "block";
      }
    }
  });

  const onReset = useMemoizedFn((eventObject: THREE.Object3D) => {
    // 抬升恢复
    gsap.to(eventObject.scale, {
      duration: 0.5,
      z: 1,
    });
    const {
      userData: { adcode, name, index },
    } = eventObject;
    if (subGroupRef.current) {
      // 处理子组件group整体抬高恢复
      gsap.to(subGroupRef.current.position, {
        duration: 0.5,
        z: depth,
      });
      // 处理子组件相同省份位置抬高
      if (subGroupRef.current) {
        for (const val of subGroupRef.current.children) {
          if (val.userData.needInteractive) {
            // 需要交互的子组件 needInteractive 注释见子组件列表 SubChildren
            onLoopSubGroup(val.children, {
              adcode,
              name,
              zValue: 0,
            });
          }
        }
      }
    }

    // 高亮恢复
    for (const val of eventObject.children) {
      // shape 设置顶面颜色
      // 根据名称区分是 shape 还是 line topLine还是bottomLine
      if (val.name === "meshShape") {
        const topMaterial = (val as THREE.Mesh)
          ?.material[0] as THREE.MeshBasicMaterial;
        topMaterial.visible = false;
      }
      if (val.name === "infoPanel") {
        infoPanelRefs.current[index].style.display = "none";
      }
    }
  });

  // 鼠标离开 相当于reset 重置
  const onPointerLeave = useMemoizedFn((event: ThreeEvent<PointerEvent>) => {
    event.stopPropagation();
    const {
      eventObject: { userData },
      eventObject,
    } = event;
    // 当前选中的区域的 索引
    // const index = userData?.index as number;
    // 当前选中的区域的 adcode 如果已经选中就不再处理
    if (userData.adcode === getSelectAdcode()) return;
    onReset(eventObject);
  });

  // 清除选中效果
  const onClear = useMemoizedFn((event: MouseEvent) => {
    event.stopPropagation();
    if (selectGroupRef.current) {
      onReset(selectGroupRef.current);
      selectGroupRef.current = undefined;
    }
  });

  return {
    onPointerEnter,
    onPointerLeave,
    onClear,
  };
}
export default useEvent;
