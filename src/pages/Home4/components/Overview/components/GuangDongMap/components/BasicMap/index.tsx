import { memo, useMemo, useRef, useState } from "react";
import RenderShape from "./components/RenderShape";
import React from "react";
import Container from "../../container";
import useEvent from "../../hooks/useEvent";
import RenderName from "./components/RenderName";
import bg from "@/assets/0/popBg.png";
import type * as THREE from "three";
import { Html } from "@react-three/drei";
import BgImage from "@/components/BgImage";
import { useInterval } from "ahooks";

function BasicMap() {
  const { featureData, mapPosition, guangDongMapData, dataLoading } =
    Container.useContainer();

  const panelRef = useRef<THREE.Group<THREE.Object3DEventMap>>(null);

  const { onPointerEnter, onPointerLeave } = useEvent();

  const infoDom = useRef<HTMLDivElement>(null);

  const [selectAdCode, setSelectAdCode] = useState<number>();

  const [selectName, setSelectName] = useState<string>();

  const count = useMemo(() => {
    if (selectAdCode) {
      return guangDongMapData.get(selectAdCode)?.attackCount ?? 0;
    }
    return 0;
  }, [guangDongMapData, selectAdCode]);

  const currentIndex = useRef(-1);

  const groupRef = useRef<THREE.Group>(null);

  // 暂停
  const [pause, setPause] = useState(false);

  const nextProvince = () => {
    const nextIndex = (currentIndex.current + 1) % featureData.length;
    const nextVal = featureData[nextIndex];
    const adcode = nextVal.properties?.adcode;
    const name = nextVal.properties?.name;
    setSelectAdCode(adcode);
    setSelectName(name);
    if (panelRef.current && infoDom.current) {
      infoDom.current.style.display = "block";

      if (nextVal.cityPosition.x > 0) {
        const value = nextVal.cityPosition.x * 0.55;
        panelRef.current.position.x =
          nextVal.cityPosition.x - (value > 10 ? 10 : value);
      } else {
        panelRef.current.position.x = nextVal.cityPosition.x;
      }
      panelRef.current.position.y = -nextVal.cityPosition.y - 5.5;
    }
    currentIndex.current = nextIndex;
  };

  useInterval(
    nextProvince,
    dataLoading || pause ? undefined : 3000 // 每3秒切换一次
  );

  return (
    <React.Fragment>
      <group position={mapPosition} renderOrder={1}>
        {featureData.map((val, index) => {
          const adcode = val.properties?.adcode;
          const name = val.properties?.name;
          return (
            <group
              key={adcode ?? index}
              ref={groupRef}
              onPointerEnter={(event) => {
                onPointerEnter(event);
                const {
                  eventObject: { userData },
                } = event;
                const adcode = userData?.adcode as number;
                const name = userData?.name as string;
                setSelectAdCode(adcode);
                setSelectName(name);
                if (infoDom.current) {
                  infoDom.current.style.display = "block";
                }
                setPause(true);
              }}
              onPointerMove={(event) => {
                if (panelRef.current && infoDom.current) {
                  panelRef.current.position.x = event.point.x;
                  const value = event.point.y * 0.6;
                  panelRef.current.position.y = -event.point.y - value;
                }
              }}
              onPointerLeave={(event) => {
                onPointerLeave(event);
                if (infoDom.current) {
                  infoDom.current.style.display = "none";
                }
                setPause(false);
              }}
              userData={{
                adcode,
                name,
                index,
                cityPosition: val.cityPosition,
              }}
            >
              {/* 遍历每个特征的多边形坐标，并为每个多边形渲染线条 */}
              {val.shapeList.map((item) => {
                return (
                  <React.Fragment key={item.key}>
                    {/* 面 */}
                    <RenderShape
                      shape={item.shape}
                      adcode={adcode}
                      name={name}
                    />
                  </React.Fragment>
                );
              })}

              <RenderName
                index={index}
                x={val.cityPosition.x}
                y={val.cityPosition.y}
                name={name}
                adcode={adcode}
              />
            </group>
          );
        })}
      </group>
      <group ref={panelRef} position={[0, 0, 30]}>
        <Html
          position={[0, 0, 0]}
          transform
          sprite
          scale={[7, 7, 7]}
          pointerEvents="none"
          ref={infoDom}
          style={{
            display: "none",
          }}
        >
          <BgImage
            className="w-[328px] h-[164px] absolute left-0 top-0"
            url={bg}
            style={{
              transform: `translate(10%,-100%)`,
            }}
          >
            <div className="color-text text-[20px] w-[130px] text-center mt-3">
              {selectName}
            </div>
            <div className="color-secondary flex items-center justify-between px-[60px] h-[106px]">
              <span className="text-[20px]">告警数量</span>
              <span
                className="text-[36px] color-text"
                style={{
                  fontFamily: "DINCond-Bold",
                }}
              >
                {count}
              </span>
            </div>
          </BgImage>
        </Html>
      </group>
    </React.Fragment>
  );
}
export default memo(BasicMap);
