import { useTexture } from "@react-three/drei";
import mapBg from "@/assets/4/mapBg.webp";

export default () => {
  const texture = useTexture(mapBg);

  return (
    <group renderOrder={-1}>
      <mesh position={[0, -12, 0]} rotation={[0, -0.04, 0]}>
        <planeGeometry args={[462, 260]} attach="geometry" />
        <meshBasicMaterial transparent map={texture} />
      </mesh>
    </group>
  );
};
