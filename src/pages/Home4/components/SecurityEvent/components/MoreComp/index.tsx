import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import synergyService from "@/service/synergyService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

function MoreComp(props: CustomModalProps) {
  const { open, onCancel } = props;

  const columns: ProColumns[] = [
    {
      title: "排名",
      key: "dataIndex",
      dataIndex: "dataIndex",
      width: 60,
      align: "center",
    },
    {
      title: "最新告警时间",
      dataIndex: "lastAlarmTime",
      key: "lastAlarmTime",
    },
    {
      title: "事件类型",
      dataIndex: "eventType",
      key: "eventType",
    },
    {
      title: "被攻击企业",
      dataIndex: "attackedCompany",
      key: "attackedCompany",
    },
    {
      title: "受害者IP",
      dataIndex: "victimIp",
      key: "victimIp",
    },
    {
      title: "攻击者IP",
      dataIndex: "attackerIp",
      key: "attackerIp",
    },
    {
      title: "攻击者位置",
      dataIndex: "attackerLocation",
      key: "attackerLocation",
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await synergyService.criticalEventList({
      startTime,
      endTime,
      ...params,
    });
    return {
      data: res.criticalEventDtoList ?? [],
      total: res.total,
    };
  };

  return (
    <ProModal type={2} title="安全事件列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
