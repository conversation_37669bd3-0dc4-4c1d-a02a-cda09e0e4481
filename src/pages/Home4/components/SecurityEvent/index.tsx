import Card from "@/components/Card";
import List from "@/components/List";
import type { TableProps } from "antd";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import synergyService from "@/service/synergyService";
import { useMemo } from "react";

function UnderAttackedIp() {
  const columns: TableProps["columns"] = [
    {
      title: "排名",
      key: "dataIndex",
      dataIndex: "dataIndex",
      width: 50,
      align: "center",
    },
    {
      title: "最新告警时间",
      dataIndex: "lastAlarmTime",
      key: "lastAlarmTime",
      width: 180,
    },
    {
      title: "事件类型",
      dataIndex: "eventType",
      key: "eventType",
      width: 80,
    },
    {
      title: "被攻击企业",
      dataIndex: "attackedCompany",
      key: "attackedCompany",
    },
    {
      title: "受害者IP",
      dataIndex: "victimIp",
      key: "victimIp",
    },
    {
      title: "攻击者IP",
      dataIndex: "attackerIp",
      key: "attackerIp",
    },
    {
      title: "攻击者位置",
      dataIndex: "attackerLocation",
      key: "attackerLocation",
    },
  ];

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      synergyService.criticalEventList({
        startTime,
        endTime,
        page: 1,
        size: 10,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () => data?.criticalEventDtoList ?? [],
    [data?.criticalEventDtoList]
  );

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  return (
    <div className="absolute top-[720px] left-[48px]">
      <MoreComp key={visibleKey} open={open} onCancel={setFalse} />
      <Card
        headerType={4}
        title="安全事件列表"
        onMore={setTrue}
        loading={loading}
      >
        <div className="mt-2">
          <List
            width={848}
            height={238}
            data={list}
            size={5}
            columns={columns}
          />
        </div>
      </Card>
    </div>
  );
}
export default UnderAttackedIp;
