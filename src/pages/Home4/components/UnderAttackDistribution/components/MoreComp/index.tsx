import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import type { ProColumns } from "@ant-design/pro-table";
import situationService from "@/service/situationService";
import { useTime } from "@/store/useTime";
import type { GetProp } from "antd";
import { useMemo } from "react";

interface IProps extends CustomModalProps {
  dataType: string;

  name: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, dataType, name } = props;

  const columns: ProColumns[] = useMemo(() => {
    return [
      {
        dataIndex: "dataIndex",
        title: "序号",
        align: "center",
      },
      {
        dataIndex: "name",
        title: `${name}名称`,
      },
      {
        dataIndex: "eventCount",
        title: "事件数量",
      },
    ];
  }, [name]);

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    const res = await situationService.attackedSourceRespList({
      startTime,
      endTime,
      dataType,
      ...params,
    });
    return {
      data: res.attackedSourceDtoList,
      total: res.total,
    };
  };

  return (
    <ProModal type={1} title="受攻击分布" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 380,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
