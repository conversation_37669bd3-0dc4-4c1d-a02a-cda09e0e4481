import Card from "@/components/Card";
import useVisible from "@/hooks/useVisible";
import MoreComp from "./components/MoreComp";
import { useMemo, useState } from "react";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import situationService from "@/service/situationService";
import type { BarColumns } from "@/components/BasicBar";
import BasicBar from "@/components/BasicBar";

function UnderAttackDistribution() {
  const [activeKey, setActiveKey] = useState("1");

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      situationService.attackedSourceRespList({
        startTime,
        endTime,
        page: 1,
        size: 20,
        dataType: activeKey,
      }),
    {
      refreshDeps: [startTime, endTime, activeKey],
    }
  );

  const name = useMemo(() => {
    switch (activeKey) {
      case "1":
        return "城市";

      case "2":
        return "行业";

      default:
        return "";
    }
  }, [activeKey]);

  const list = useMemo(
    () =>
      data?.attackedSourceDtoList?.map((val, index) => ({
        ...val,
        id: index,
      })) ?? [],
    [data?.attackedSourceDtoList]
  );

  const columns = useMemo<BarColumns>(() => {
    return [
      {
        dataKey: "formatEventCount",
        color: "#F3B67B",
        name: "攻击次数",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "#2390C4",
            },
            {
              offset: 1,
              color: "#2441AC",
            },
          ],
        },
      },
    ];
  }, []);

  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  return (
    <div className="absolute left-[46px] top-[436px]">
      <MoreComp
        dataType={activeKey}
        open={open}
        onCancel={setFalse}
        key={visibleKey}
        name={name}
      />
      <Card
        headerType={1}
        title="告警分布"
        loading={loading}
        tabsProps={{
          items: [
            {
              key: "1",
              label: "城市",
            },
            {
              key: "2",
              label: "行业",
            },
          ],
          activeKey,
          onChange: setActiveKey,
        }}
        onMore={setTrue}
      >
        <div className="mt-1">
          <BasicBar
            data={list}
            width={450}
            height={220}
            xDataKey="name"
            columns={columns}
            title={`(单位)：${data?.formatType}`}
          />
        </div>
      </Card>
    </div>
  );
}
export default UnderAttackDistribution;
