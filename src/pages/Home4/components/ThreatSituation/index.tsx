import Card from "@/components/Card";
import BasicLine from "@/components/BasicLine";
import type { Coloumns } from "@/components/BasicLine";
import { useMemo } from "react";
import useVisible from "@/hooks/useVisible";
import ProModal from "@/components/Modal";
import { useTime } from "@/store/useTime";
import { useRequest } from "ahooks";
import synergyService from "@/service/synergyService";

function ThreatSituation() {
  const coloumns = useMemo<Coloumns>(() => {
    return [
      {
        dataKey: "attackNum",
        name: "全网攻击",
        color: "rgba(43, 128, 255, 1)",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(43, 128, 255, .4)",
            },
            {
              offset: 1,
              color: "rgba(43, 128, 255, 0)",
            },
          ],
        },
      },
      {
        dataKey: "eventNum",
        name: "全网事件",
        color: "rgba(4, 205, 244, 1)",
        area: {
          angle: 180,
          colorStops: [
            {
              offset: 0,
              color: "rgba(4, 205, 244, .4)",
            },
            {
              offset: 1,
              color: "rgba(4, 205, 244, 0)",
            },
          ],
        },
      },
    ];
  }, []);

  const { startTime, endTime } = useTime();

  const { data, loading } = useRequest(
    () =>
      synergyService.criticalPosture({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () =>
      data?.criticalPostureDtoList ?? [],
    [data?.criticalPostureDtoList]
  );

  const [open, { setTrue, setFalse }] = useVisible(false);

  return (
    <div className="absolute top-[742px] left-[540px]">
      <ProModal title="威胁态势" type={3} open={open} onCancel={setFalse}>
        <div className="mt-[10px] p-[20px]">
          <BasicLine
            data={list}
            width={1320}
            xDataKey="time"
            height={420}
            coloumns={coloumns}
          />
        </div>
      </ProModal>
      <Card headerType={2} title="威胁态势" onMore={setTrue} loading={loading}>
        <BasicLine
          data={list}
          xDataKey="time"
          width={780}
          height={240}
          coloumns={coloumns}
        />
      </Card>
    </div>
  );
}
export default ThreatSituation;
