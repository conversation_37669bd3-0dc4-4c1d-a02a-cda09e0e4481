import Card from "@/components/Card";
import Comp from "./components/Comp";
import { useRequest } from "ahooks";
import { useTime } from "@/store/useTime";
import synergyService from "@/service/synergyService";
import { useMemo, useState } from "react";
import MoreComp from "./components/MoreComp";
import useVisible from "@/hooks/useVisible";

function AttackType() {
  const [open, { setFalse, setTrue, visibleKey }] = useVisible(false);

  const { startTime, endTime } = useTime();

  const [incidentType, setIncidentType] = useState<string | undefined>();

  const { data, loading } = useRequest(
    () =>
      synergyService.criticalAttackTypeDistribution({
        startTime,
        endTime,
      }),
    {
      refreshDeps: [startTime, endTime],
    }
  );

  const list = useMemo(
    () => data?.attackTypeDistributionList?.slice(0, 10) ?? [],
    [data?.attackTypeDistributionList]
  );

  const onMore = (value?: string) => {
    setIncidentType(value);
    setTrue();
  };

  return (
    <div className="absolute left-[46px] top-[130px]">
      <MoreComp
        open={open}
        onCancel={setFalse}
        incidentType={incidentType}
        key={visibleKey}
      />
      <Card
        headerType={1}
        title="告警类型分布"
        onMore={() => onMore()}
        loading={loading}
        bodyClass="py-[16px]"
      >
        <Comp data={list} onMore={onMore} />
        {/* <div className="size-[200px] rounded-full border-2 border-dashed border-[rgba(168,168,168,0.2)]"></div> */}
      </Card>
    </div>
  );
}
export default AttackType;
