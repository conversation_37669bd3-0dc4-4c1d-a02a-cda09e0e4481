import type { CustomModalProps } from "@/components/Modal";
import ProModal from "@/components/Modal";
import ProTable from "@/components/Table";
import synergyService from "@/service/synergyService";
import { useTime } from "@/store/useTime";
import type { ProColumns } from "@ant-design/pro-table";
import type { GetProp } from "antd";

interface IProps extends CustomModalProps {
  incidentType?: string;
}
function MoreComp(props: IProps) {
  const { open, onCancel, incidentType } = props;

  const columns: ProColumns[] = [
    {
      title: "排名",
      key: "dataIndex",
      dataIndex: "dataIndex",
      width: 60,
      align: "center",
    },
    {
      title: "最新告警时间",
      dataIndex: "discoveryTime",
      key: "discoveryTime",
    },
    {
      title: "事件类型",
      dataIndex: "eventType",
      key: "eventType",
    },
    {
      title: "被攻击单位",
      dataIndex: "beAttackedFirm",
      key: "beAttackedFirm",
    },
    {
      title: "受害者IP",
      dataIndex: "dstIp",
      key: "dstIp",
    },
    {
      title: "攻击者IP",
      dataIndex: "srcIp",
      key: "srcIp",
    },
    {
      title: "攻击者位置",
      dataIndex: "srcPosition",
      key: "srcPosition",
    },
  ];

  const { startTime, endTime } = useTime();

  const loadList: GetProp<typeof ProTable, "request"> = async (
    params: API.PageParams
  ) => {
    console.warn(incidentType,'incidentTypeincidentTypeincidentType')
    const res = await synergyService.securityEventList({
      startTime,
      endTime,
      ...params,
      incidentType,
    });
    return {
      data: res.securityEventListDtoList ?? [],
      total: res.total,
    };
  };

  return (
    <ProModal type={2} title="安全事件列表" open={open} onCancel={onCancel}>
      <ProTable
        columns={columns}
        request={loadList}
        scroll={{
          y: 540,
        }}
      />
    </ProModal>
  );
}

export default MoreComp;
