import { motion } from "framer-motion";
import useListSize from "@/hooks/useListSize";
import useListAnimate from "@/hooks/useListAnimate";
import {
  TableAnimationConnectModeEnum,
  TableAnimationModeEnum,
  TableAnimationTypeEnum,
} from "@/enum";
import ListItem from "../ListItem";
import type { LinearConfig } from "@/typings";

interface IProps {
  data: API.Synergy.AttackTypeDistributionDto &
    {
      colorConfig: LinearConfig;
    }[];

  onMore: (value?: string) => void;
}
function List(props: IProps) {
  const { data, onMore } = props;
  const headerHeight = 0;

  const height = 230;

  const size = 5;

  const rowGap = 0;

  const width = 240;

  const { itemHeight, listHeight } = useListSize({
    headerHeight,
    height,
    size,
    rowGap,
  });

  const animationConfig = {
    show: true, // 是否显示动画
    type: TableAnimationTypeEnum.Single, // 类型
    connectMode: TableAnimationConnectModeEnum.Continuous, // 衔接方式
    animateMode: TableAnimationModeEnum.Flip, // 动画形式
    interval: 3, //
    backgroundFixed: false,
  };

  const {
    controls,
    firstControls,
    dataIndexList,
    showAnimate,
    beforeIndexList,
  } = useListAnimate({
    data,
    size,
    rowGap,
    listHeight,
    itemHeight,
    animationConfig,
  });

  return (
    <div
      style={{
        width,
        height,
      }}
      className="relative -top-2"
    >
      {/* 列表 */}
      <div
        className="overflow-hidden relative"
        style={{
          height: listHeight,
        }}
      >
        <motion.div
          className="absolute"
          style={{
            width,
            height: listHeight,
          }}
          animate={controls}
        >
          {showAnimate && (
            <motion.div animate={firstControls}>
              {beforeIndexList?.map((val) => (
                <ListItem
                  key={`before${val}`}
                  index={val}
                  rowGap={rowGap}
                  item={data[val]}
                  showAnimate={false}
                  itemHeight={itemHeight}
                  onMore={onMore}
                />
              ))}
            </motion.div>
          )}
          {dataIndexList.map((val) => (
            <ListItem
              key={`listItem${val + 1}`}
              index={val}
              rowGap={rowGap}
              item={data[val]}
              itemHeight={itemHeight}
              onMore={onMore}
            />
          ))}
        </motion.div>
      </div>
    </div>
  );
}
export default List;
