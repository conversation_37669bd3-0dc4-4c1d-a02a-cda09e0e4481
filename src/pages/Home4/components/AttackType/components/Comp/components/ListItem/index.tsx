import type { LinearConfig } from "@/typings";
import { formatLinear } from "@/utils";

interface IProps {
  index: number;

  rowGap: number;

  itemHeight: number;

  // 是否动画
  showAnimate?: boolean;

  item: API.Synergy.AttackTypeDistributionDto & {
    colorConfig: LinearConfig;
  };

  onMore: (value?: string) => void;
}
function ListItem(props: IProps) {
  const { index, rowGap, itemHeight, item, onMore } = props;

  return (
    <div
      key={`item${index}`}
      className="flex items-center pl-4 pr-5 cursor-pointer"
      style={{
        marginBottom: rowGap,
        height: itemHeight,
      }}
      onClick={() => {
        onMore(item.incidentType);
      }}
    >
      <div className="flex items-center">
        <span
          className="size-[10px] min-w-[10px] rounded-full inline-block"
          style={{
            background: formatLinear(item.colorConfig),
          }}
        ></span>
        <span className="ml-3 color-text text-[17px] max-w-[160px] truncate">
          {item.attackTypeName}
        </span>
      </div>
      <div
        className="ml-2 text-[20px]"
        style={{
          backgroundImage: formatLinear(item.colorConfig),
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          // fontFamily: "YouSheBiaoTiHei-2",
        }}
      >
        {item.attackTypeNum}
      </div>
    </div>
  );
}
export default ListItem;
