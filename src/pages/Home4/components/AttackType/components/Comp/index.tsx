import bg6 from "@/assets/1/bg6.png";
import bg7 from "@/assets/1/bg7.png";
import BgImage from "@/components/BgImage";
import { nanoid } from "nanoid";
import type { ContentType } from "recharts/types/component/Tooltip";
import type {
  NameType,
  ValueType,
} from "recharts/types/component/DefaultTooltipContent";
import { PieChart, Pie, Cell, Tooltip } from "recharts";
import React, { useCallback, useMemo, useRef } from "react";
import SvgLinearGradient from "@/components/Comp/SvgLinearGradient";
import type { LinearConfig } from "@/typings";
import List from "./components/List";

interface IProps {
  data: API.Synergy.AttackTypeDistributionDto[];

  // 图表放大的倍数
  scale?: number;

  onMore: (value?: string) => void;
}
function Comp(props: IProps) {
  const { data, scale = 1,onMore } = props;

  const colorList: LinearConfig[] = useMemo(
    () => [
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(238, 111, 124, 1)",
          },
          {
            offset: 1,
            color: "rgba(238, 111, 124, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(211,173,247,1)",
          },
          {
            offset: 1,
            color: "rgba(211,173,247,0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(166, 207, 255, 1)",
          },
          {
            offset: 1,
            color: "rgba(166, 207, 255, 0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(44, 81, 250, 1)",
          },
          {
            offset: 1,
            color: "rgba(44, 81, 250, 0.75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(255, 207, 95, 1)",
          },
          {
            offset: 1,
            color: "rgba(255, 207, 95, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(239, 43, 103, 1)",
          },
          {
            offset: 1,
            color: "rgba(239, 43, 103, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(255, 177, 42, 1)",
          },
          {
            offset: 1,
            color: "rgba(255, 177, 42, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(255, 193, 7, 1)",
          },
          {
            offset: 1,
            color: "rgba(255, 193, 7, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(88, 199, 23, 1)",
          },
          {
            offset: 1,
            color: "rgba(88, 199, 23, .75)",
          },
        ],
      },
      {
        angle: 180,
        colorStops: [
          {
            offset: 0,
            color: "rgba(0, 232, 255,1)",
          },
          {
            offset: 1,
            color: "rgba(0, 232, 255,.75)",
          },
        ],
      },
    ],
    []
  );

  const linearIdRef = useRef(`${nanoid()}_linear_`);

  const getColor = useCallback(
    (index) => {
      return colorList[index] ?? colorList[0];
    },
    [colorList]
  );

  const CustomTooltip: ContentType<ValueType, NameType> = ({
    active,
    payload,
  }) => {
    if (active && payload && payload.length) {
      const value = payload[0].value;
      const label = payload[0].name;

      return (
        <div
          className="flex items-center w-fit justify-center h-[36px] color-text gap-x-2 bg-[rgba(50,91,174,0.4)] rounded-[4px] px-4"
          style={{
            boxShadow: "inset 0px 0 20px 0px #5FC1FF",
            backdropFilter: "blur(12px)",
          }}
        >
          <span className="text-[15px] w-auto">{label}</span>
          <span
            className="text-[20px]"
            style={{
              fontFamily: "DINCond-Bold",
            }}
          >
            {value}
          </span>
        </div>
      );
    }

    return null;
  };

  const dataList = useMemo(() => {
    return data.map((val, index) => ({
      ...val,
      colorConfig: getColor(index),
    }));
  }, [data, getColor]);

  return (
    <div className="flex items-center">
      <div
        className="size-[218px] relative flex justify-center items-center"
        style={{
          transform: `scale(${scale})`,
        }}
      >
        <div className="centered">
          <BgImage
            url={bg6}
            className="w-[218px] h-[212px] animate-[rotateCounterClockwise_16s_linear_infinite]"
          />
        </div>
        <div className="centered">
          <BgImage
            url={bg7}
            className="size-[112px] animate-[rotateClockwise_16s_linear_infinite]"
          ></BgImage>
        </div>
        <PieChart
          width={190}
          height={190}
          margin={{
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
          }}
        >
          {colorList.map((_, index) => {
            const linearId = `${linearIdRef.current}${index}`;
            const colorConfig = getColor(index);
            return colorConfig ? (
              <React.Fragment key={linearId}>
                <defs>
                  <SvgLinearGradient id={linearId} config={colorConfig} />
                </defs>
              </React.Fragment>
            ) : null;
          })}
          <Pie
            data={data}
            cx={95}
            cy={95}
            innerRadius={76}
            outerRadius={90}
            paddingAngle={5} // 饼图内间距
            dataKey="attackTypeNum"
            stroke="none"
            cornerRadius={0} // 圆角
            isAnimationActive={false}
          >
            {data.map((val, index) => {
              const linearId = `${linearIdRef.current}${index}`;
              const colorConfig = getColor(index);
              return (
                <React.Fragment key={linearId}>
                  <Cell
                    style={{
                      outline: "none",
                      overflow: "hidden",
                    }}
                    name={val.attackTypeName}
                    key={`cell-${index}`}
                    fill={colorConfig ? `url(#${linearId})` : "red"}
                  />
                </React.Fragment>
              );
            })}
          </Pie>
          <Tooltip
            content={<CustomTooltip />}
            wrapperStyle={{
              width: "max-content",
            }}
          />
        </PieChart>
      </div>
      <div className="overflow-hidden relative top-1">
        <List data={dataList} onMore={onMore} />
      </div>
    </div>
  );
}
export default Comp;
