import { request } from "umi";
const prefix = "/synergyServer";

class GuaranteeService {
  attackSourceListTop5(params: API.Guarantee.AttackSourceListTop5Params) {
    return request<API.Guarantee.AttackSourceListTop5Result>(
      `${prefix}/getAttackSourceListTop5`,
      {
        method: "get",
        params,
        name: "获取重保任务的攻击源列表",
      }
    );
  }

  cityIsOk(params: API.Guarantee.CityIsOkParams) {
    return request<API.Guarantee.CityIsOkResult>(`${prefix}/getCityIsOk`, {
      method: "get",
      params,
      name: "获取重保任务的地市报平安情况",
    });
  }

  convener(taskId: string) {
    return request<API.Guarantee.ConvenerResult>(`${prefix}/getConvener`, {
      method: "get",
      params: { taskId },
      name: "获取总召集人信息",
    });
  }


  // 新
  zbOverview(taskId: string) {
    return request<API.Guarantee.ZbOverviewResult>(`${prefix}/getZbOverview`, {
      method: "get",
      params: { taskId },
      name: "获取重保任务概览",
    });
  }

  zbUnitList(params: API.PageParams & { taskId: string }) {
    return request<API.Guarantee.ZbUnitListResult>(`${prefix}/getZbUnitList`, {
      method: "get",
      params,
      name: "重保单位列表",
    });
  }

  zbSynergyUnitList(params: API.PageParams & { taskId: string }) {
    return request<API.Guarantee.ZbSynergyUnitListResult>(
      `${prefix}/getZbSynergyUnitList`,
      {
        method: "get",
        params,
        name: "值班值守列表",
      }
    );
  }

  threatInfoList(params: API.ListParams & { taskId: string }) {
    return request<API.Guarantee.ThreatInfoListResult>(
      `${prefix}/getGuaranteeTaskThreatIpList`,
      {
        method: "get",
        params,
        name: "重保任务威胁IP列表",
      }
    );
  }

  vulInfoList(params: API.ListParams & { taskId: string }) {
    return request<API.Guarantee.VulInfoListResult>(
      `${prefix}/getGuaranteeTaskVulInfoList`,
      {
        method: "get",
        params,
        name: "重保任务漏洞列表",
      }
    );
  }

  eventInfoList(params: API.ListParams & { taskId: string }) {
    return request<API.Guarantee.EventInfoListResult>(
      `${prefix}/getEventUnitList`,
      {
        method: "get",
        params,
        name: "事件信息列表",
      }
    );
  }

  zbSecurityPosture(params: API.CommonParams & { taskId: string }) {
    return request<API.Guarantee.ZbSecurityPostureResult>(
      `${prefix}/getZbSecurityPosture`,
      {
        method: "get",
        params,
        name: "重保安全态势",
      }
    );
  }

  zbIndustryCityIsOk(params: API.Guarantee.ZbIndustryCityIsOkParams) {
    return request<API.Guarantee.ZbIndustryCityIsOkResult>(
      `${prefix}/getZbIndustryCityIsOk`,
      {
        method: "get",
        params,
        name: "获取重保行业地市报平安情况",
      }
    );
  }

  guaranteeTaskList() {
    return request<API.Guarantee.GuaranteeTaskListResult>(
      `${prefix}/getGuaranteeTaskList`,
      {
        method: "get",
        params:{},
        name: "获取重保任务列表",
      }
    );
  }
}
export default new GuaranteeService();
