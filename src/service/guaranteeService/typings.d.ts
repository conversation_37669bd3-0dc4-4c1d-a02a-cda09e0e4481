export namespace GuaranteeType {
  export interface AttackSourceListTop5Params extends API.ListParams {
    /**
     * 数据类型（1-境内，2-境外)
     */
    dataType: string;

    /**
     * 任务ID
     */
    taskId: string;
  }

  export interface AttackSourceListTop5Result {
    /**
     * 受攻击列表
     */
    attackedSourceDtoList?: AttackedSourceDto[];
    /**
     * 攻击来源列表
     */
    attackSourceList?: AttackSourceDto[];
    /**
     * 总记录数
     */
    total?: number;
  }

  /**
   * AttackSourceDto，攻击源DTO
   */
  export interface AttackSourceDto {
    /**
     * 事件数量
     */
    eventCount?: number;
    /**
     * 环比 安全态势大屏使用
     */
    ratio?: number;
    /**
     * 国家/地区
     */
    region?: string;
  }

  /**
   * AttackedSourceDto，攻击源DTO
   */
  export interface AttackedSourceDto {
    /**
     * 事件数量
     */
    eventCount?: number;
    /**
     * 城市/行业名称
     */
    name?: string;
  }

  export interface CityIsOkParams extends API.CommonParams {
    /**
     * 任务id
     */
    taskId: string;
  }

  /**
   * 地市报平安DTO
   */
  export interface CityIsOkDto {
    /**
     * 地市code
     */
    cityCode: number;
    /**
     * 地市名称
     */
    cityName?: string;
    /**
     * 报平安数量
     */
    isOkCount: number;
  }

  export interface CityIsOkResult {
    /**
     * 地市报平安热力图数据
     */
    cityIsOkList?: CityIsOkDto[];
  }

  export interface ConvenerResult {
    /**
     * 召集人名称
     */
    convener?: string;
    /**
     * 召集人单位
     */
    corporation?: string;
    /**
     * 移动电话
     */
    phone?: string;
    /**
     * 固定电话
     */
    tel?: string;
  }

  export interface EventUnitDto {
    /**
     * 事件数量
     */
    eventCount: number;

    /**
     * 单位名称
     */
    unitName: string;
  }

  export interface EventUnitListResult {
    /**
     * 事件单位列表
     */
    eventUnitList: EventUnitDto[];

    /**
     * 总数
     */
    total: number;
  }

  export interface ZbTaskInfoDto {
    /**
     * 任务结束时间
     */
    taskEndTime?: string;
    /**
     * 任务ID
     */
    taskId?: string;
    /**
     * 任务名称
     */
    taskName?: string;
    /**
     * 任务开始时间
     */
    taskStartTime?: string;
    /**
     * 地区编码列表
     */
    districtCodeList: string[]
  }

  export interface GuaranteeTaskListResult {
    /**
     * 任务列表
     */
    zbTaskInfoDtoList?: ZbTaskInfoDto[];
  }

  export interface ZbIndustryCityIsOkParams {
    /**
     * 任务id
     */
    taskId: string;

    /**
     * 数据类型(1-行业，2=地市)
     */
    dataType: string;
  }

  export interface IsOkDto {
    /**
     * 行业/地市名称
     */
    cityOrIndustryName: string;

    /**
     * 报平安数
     */
    isOkCount: string;
  }

  export interface ZbIndustryCityIsOkResult {
    /**
     * 重保行业地市报平安数据
     */
    isOkDtoList: IsOkDto[];
  }

  export interface ZbOverviewResult {
    /**
     * 事件数量
     */
    eventUnitCount?: number;
    /**
     * 资产数量
     */
    zbAssetsCount?: number;
    /**
     * 重保单位数量
     */
    zbUnitCount?: number;
  }

  export interface ZbSecurityPostureDto {
    /**
     *  日期时间，格式yyyy-MM-dd
     */
    dateTime: string;

    /**
     * 重保攻击次数
     */
    attackCount: number;
  }

  export interface ZbSecurityPostureResult {
    /**
     * 重保安全态势列表
     */
    zbSecurityPostureDtoList: ZbSecurityPostureDto[];
  }

  export interface ZbSynergyUnitListDto {
    /**
     * 职务
     */
    jobTitle?: string;

    /**
     * 值班人员姓名
     */
    nameOfDutyOfficer: string;

    /**
     * 办公电话
     */
    officeTelephone?: string;

    /**
     * 手机号码
     */
    phoneNumber: string;

    /**
     * 单位名称
     */
    unitName: string;
  }

  export interface ZbSynergyUnitListResult {
    /**
     * 总数
     */
    total: number;

    /**
     * 重保协同单位列表
     */
    unitList: ZbSynergyUnitListDto[];
  }

  export interface ZbUnitListDto {
    /**
     * 关联资产数
     */
    assetsCount: string;

    /**
     * 单位名称
     */
    unitName: string;

    /**
     * 事件数
     */
    eventCount: string;
  }

  export interface ZbUnitListResult {
    /**
     * 重保单位列表
     */
    zbUnitListDtoList: ZbUnitListDto[];

    /**
     * 总数
     */
    total: number;
  }

  export interface ThreatIpListDto {
    /**
     * 处理结果（deal-已处置 warn-已预警 null-未处置）
     */
    dealResult?: DealResultEnum;

    /**
     * 发现时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    findTime: string;

    /**
     * 威胁Ip
     */
    threatIp: string;

    /**
     * 威胁等级
     */
    threatLevel: string;

    /**
     * 威胁IPID
     */
    threatIpId?: string;

    /**
     * 威胁类型
     */
    threatType: string;

    /**
     * 攻击次数
     */
    attackNum?: number;
  }

  export interface ThreatInfoListResult {
    /**
     * 威胁IP列表
     */
    threatIpListDtoList: ThreatIpListDto[]

    /**
     * 总数
     */
    total: number;
  }

  export interface VulInfoListDto {
    /**
     * 发现时间（格式：yyyy/MM/dd HH:mm:ss）
     */
    discoveryTime: string;

    /**
     * 漏洞正式解决方案
     */
    formalSolution: string;

    /**
     * 漏洞临时解决方案（正式解决方案没有值时使用此字段）
     */
    temporarySolution: string;

    /**
     * 单位名称
     */
    unitName: string;

    /**
     * 漏洞类别（commonVul-通用型漏洞、eventVul-事件型漏洞）
     */
    vulCategory: 'commonVul' | 'eventVul';

    /**
     * 漏洞描述
     */
    vulDescription: string;

    /**
     * 漏洞Id
     */
    vulId: string;

    /**
     * 漏洞等级（1-低危 2-中危 3-高危 4-超危）
     */
    vulLevel: VulLevelEnum;

    /**
     * 漏洞名称
     */
    vulName: string;

    /**
     * 漏洞类型
     */
    vulTypeName: string;
  }

  export interface VulInfoListResult {
    /**
     * 漏洞列表信息
     */
    vulInfoListDtoList: VulInfoListDto[]

    /**
     * 总数
     */
    total: number;
  }

  // 告警信息
  export interface AlarmItem {
    /**
     * 攻击次数
     */
    attackNum?: number;

    /**
     * 事件名称
     */
    eventName: string;

    /**
     * 告警时间
     */
    time: string;

    /**
     * 攻击Ip
     */
    attackIp: string;

    /**
     * 攻击者国家
     */
    country: string;

    /**
     * 被攻击IP
     */
    victimIp: string;

    /**
     * 事件等级
     */
    level: string;
  }


  export interface EventInfoListDto {
    /**
     * 研判结果（success-确认攻击，fail-标记误报）
     */
    assessResult: 'success' | 'fail';

    /**
     * 研判状态 (true:已研判 false:未研判)
     */
    assessStatus: boolean;

    /**
     * 事件ID
     */
    eventId: string;

    /**
     * 事件等级
     */
    eventLevel: string;

    /**
     * 事件名称
     */
    eventName: string;

    /**
     * 事件类型
     */
    eventType: string;

    /**
     * 发现时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    foundTime: string;

    /**
     * 单位
     */
    uniName: string;
  }

  export interface EventInfoListResult {
    /** 
     * 事件信息列表
     */
    eventUnitList: EventInfoListDto[];

    /**
     * 总数
     */
    total: number;
  }

  export interface EventInfoListDtoItem {
    /**
     * 事件类型
     */
    eventType: string;

    /**
     * 事件详情
     */
    eventDetail: string;

    /**
     * 发现时间
     */
    foundTime: string;

    /**
     * 发现Ip
     */
    foundIp: string;

    /**
     * 单位名称
     */
    uniName: string;

    /**
     * 研判状态
     */
    status: string;
  }
}

declare global {
  namespace API {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export import Guarantee = GuaranteeType;
  }
}
