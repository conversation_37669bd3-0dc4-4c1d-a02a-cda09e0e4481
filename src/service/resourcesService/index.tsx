import { request } from "umi";
const prefix = "/synergyServer";

class ResourcesService {
  assetsInfoCount(params: API.Resources.AssetsInfoCountParams) {
    return request<API.Resources.AssetsInfoCountResult>(
      `${prefix}/getAssetsInfoCount`,
      {
        method: "get",
        params,
        name: "资产数据概况接口",
      }
    );
  }

  serviceTypeDistribution(params: API.CommonParams) {
    return request<API.Resources.TypeDistributionResult>(
      `${prefix}/getServiceTypeDistribution`,
      {
        method: "get",
        params,
        name: "获取业务类型分布接口",
      }
    );
  }

  assetsDistributionList(params: API.CommonParams) {
    return request<API.Resources.AssetsDistributionListResult>(
      `${prefix}/getAssetsDistributionList`,
      {
        method: "get",
        params,
        name: "获取资产分布列表接口",
      }
    );
  }

  unitAsset(params: API.CommonParams) {
    return request<API.Resources.UnitAssetResult>(`${prefix}/getUnitAsset`, {
      method: "get",
      params,
      name: "单位类型分布",
    });
  }

  industryAssetsCountList(params: API.ListParams) {
    return request<API.Resources.IndustryAssetsCountListResult>(
      `${prefix}/getIndustryAssetsCountList`,
      {
        method: "get",
        params,
        name: "行业资产分布接口",
      }
    );
  }

  cityTopList(params: API.ListParams) {
    return request<API.Resources.CityTopListResult>(
      `${prefix}/getCityTopList`,
      {
        method: "get",
        params,
        name: "获取城市TOP列表接口",
      }
    );
  }

  baseAssetsList(params: API.Resources.BaseAssetsListParams) {
    return request<API.Resources.BaseAssetsListResult>(
      `${prefix}/getBaseAssetsList`,
      {
        method: "get",
        params,
        name: "获取基础资产列表接口",
      }
    );
  }

  type() {
    return request<API.Resources.TypeResult>(`${prefix}/getType`, {
      method: "get",
      params: {},
      name: "获取通用类型接口",
    });
  }
}
export default new ResourcesService();
