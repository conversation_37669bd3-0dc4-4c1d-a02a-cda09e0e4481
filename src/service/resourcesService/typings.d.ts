export namespace ResourcesType {
  export interface AssetsDistributionDto {
    /**
     * 资产类型
     */
    assetsType?: string;
    /**
     * 资产类型名称
     */
    assetsTypeName?: string;
    /**
     * 数量
     */
    count?: string;
  }

  export interface AssetsDistributionListResult {
    /**
     * 资产分布列表
     */
    assetsDistributionList?: AssetsDistributionDto[];

    /**
     * 总资产数
     */
    assetsAllCount?: number;
  }


  export interface BaseAssetsListParams extends API.PageParams {
    /**
     * 资产类型
     */
    assetsType?: string;
    /**
     * 业务类型
     */
    businessType?: string;
    /**
     * 所属城市
     */
    homeCity?: string;
    /**
     * 行业名称
     */
    industryName?: string;
    /**
     * 单位名称
     */
    unitName?: string;
    /**
     * 单位类型
     */
    unitType?: string;
  }

  export interface BaseAssetsListResult {
    /**
     * 基础资产列表
     */
    baseAssetsList?: BaseAssetDto[];
    /**
     * 总数
     */
    totalCount?: number;
  }

  /**
   * 关基资产信息
   */
  export interface BaseAssetDto {
    /**
     * 所属地市
     */
    area?: string;
    /**
     * 域名
     */
    domain?: string;
    /**
     * 行业名称
     */
    industryName?: string;
    /**
     * ip地址
     */
    ipAddress?: string;
    /**
     * 单位名称
     */
    organizationName?: string;
    /**
     * 单位类型
     */
    organizationType?: string;
    /**
     * 设施名称
     */
    sysName?: string;
    /**
     * 业务类型
     */
    sysType?: string;
  }

  export interface CityTopDto {
    /**
     * 城市名称
     */
    cityName: string;

    /**
     * 数量
     */
    count: number;
  }

  export interface CityTopListResult {
    cityTopList: CityTopDto[];
  }

  export interface IndustryAssetsCountDto {
    /**
     * 资产数量
     */
    count: number;

    /**
     * 行业名称
     */
    industryName: string;
  }

  export interface IndustryAssetsCountListResult {
    /**
     * 反馈率排名列表
     */
    industryAssetsCountList: IndustryAssetsCountDto[];

    /**
     * 数量
     */
    total: number;
  }


  export interface UnitAssetResult {
    /**
     * 其他资产数
     */
    otherCount?: number;
    /**
     * 党政机关资产数
     */
    partyCommitteeOrgCount?: number;
    /**
     * 民营企业资产数
     */
    privateNonEnterpriseUnitCount?: number;
    /**
     * 事业单位资产数
     */
    publicInstitutionCount?: number;
    /**
     * 社会团体资产数
     */
    socialGroupCount?: number;
    /**
     * 国有企业资产数
     */
    stateEnterpriseCount?: number;
  }

  export interface TypeResult {
    /**
     * 业务类型列表
     */
    businessTypeList: string[];

    /**
     * 所属地市列表
     */
    cityList: string[];

    /**
     * 行业类型列表
     */
    industryTypeList: string[];

    /**
     * 单位类型列表
     */
    unitTypeList: string[];

    /**
     * 资产类型
     */
    equipmentTypeList: {
      /**
       * 唯一标识
       */
      assetsType: string;
      /**
       * 资产类型
       */
      assetsTypeName: string;
    }[];
  }

  // 新的

  export interface AssetsInfoCountParams extends API.CommonParams {
    /**
     * 资产类型（0-全量资产 1-省重点防护对象资产 2-其它省级资产 3-地市资产）
     */
    assetsType: number;
  }

  export interface AssetsInfoCountResult {
    /**
     * 党政机关网站总数
     */
    agencyWebsiteCount: number;
    /**
     * 关基总数
     */
    baseCount: number;
    /**
     * 域名资产总数
     */
    domainAssetsCount: number;
    /**
     * 政务系统总数
     */
    governmentAffairsSystemCount: number;
    /**
     * IP资产总数
     */
    ipAssetsCount: number;
    /**
     * 新闻网站总数
     */
    newsWebsiteCount: number;
    /**
     * 其他总数
     */
    otherCount: number;
    /**
     * 公共服务平台总数
     */
    publicServicePlatformCount: number;
    /**
     * 单位总数
     */
    unitCount: number;
  }

  export interface TypeDistributionResult {
    /**
     * 其他类数量
     */
    otherCount?: number;
    /**
     * 办公管理系统数量
     */
    platformCount?: number;
    /**
     * 业务信息系统数量
     */
    prodCount?: number;
    /**
     * 网站类数量
     */
    webSiteCount?: number;
    /**
     * 总数
     */
    allCount?: number;
  }
}

declare global {
  namespace API {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export import Resources = ResourcesType;
  }
}
