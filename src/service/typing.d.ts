declare namespace API {
  interface PageParams {
    size: number;
    page: number;
  }

  interface Result<T> {
    data: T;
  }

  type Success = null;

  // 与后端约定的响应数据格式
  interface Response {
    code: string | number;
    msg: string;
    data: any;
  }

  interface ListParams {
    /**
     * 结束时间
     */
    endTime: string;
    /**
     * 页码
     */
    page: number;
    /**
     * 每页大小
     */
    size: number;
    /**
     * 开始时间
     */
    startTime: string;
  }

  interface CommonParams {
    /**
     * 结束时间
     */
    endTime: string;

    /**
     * 开始时间
     */
    startTime: string;
  }
}

declare namespace FormConfig {
  type Path = string | number;
}
