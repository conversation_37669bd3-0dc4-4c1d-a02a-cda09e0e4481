import { request } from "umi";
const prefix = "/synergyServer";

class ManagementService {
  eventNotifyInfo(notifyId: string) {
    return request<API.Management.EventNotifyInfoResult>(
      `${prefix}/getEventNotifyInfo`,
      {
        method: "get",
        params: { notifyId },
        name: "快处置事件详情",
      }
    );
  }

  eventNotifyList(params: API.ListParams) {
    return request<API.Management.EventNotifyListResult>(
      `${prefix}/getEventNotifyList`,
      {
        method: "get",
        params,
        name: "快处置事件列表",
      }
    );
  }

  eventTypeRank(params: API.CommonParams) {
    return request<API.Management.EventTypeRankResult>(
      `${prefix}/getEventTypeRank`,
      {
        method: "get",
        params,
        name: "快处置事件类型排名",
      }
    );
  }

  // 从这里开始
  eventNotifyOverview(params: API.CommonParams) {
    return request<API.Management.EventNotifyOverviewResult>(
      `${prefix}/getEventNotifyOverview`,
      {
        method: "get",
        params,
        name: "事件通报概况接口",
      }
    );
  }

  notifyCountTop5(params: API.Management.NotifyCountTop5Params) {
    return request<API.Management.NotifyCountTop5Result>(
      `${prefix}/getNotifyCountTop5`,
      {
        method: "get",
        params,
        name: "快处置通报次数top5",
      }
    );
  }

  feedbackRateRank(params: API.ListParams & { type: string }) {
    return request<API.Management.FeedbackRateRankResult>(
      `${prefix}/getFeedbackRateRank`,
      {
        method: "get",
        params,
        name: "快处置反馈率排名",
      }
    );
  }

  realtimeEventList(params: API.Management.RealtimeEventListParams) {
    return request<API.Management.RealtimeEventListResult>(
      `${prefix}/getRealtimeEventList`,
      {
        method: "get",
        params,
        name: "快处置实时事件列表",
      }
    );
  }

  threeLevelInfo(params: API.CommonParams) {
    return request<API.Management.ThreeLevelInfoResult>(
      `${prefix}/getThreeLevelInfo`,
      {
        method: "get",
        params,
        name: "中央网信办、省网信办、地市网信办三级下发概况接口",
      }
    );
  }

  getMenuPermission(token: string) {
    return request<API.Management.MenuPermissionResult>(
      `synergyServer/getMenuPermission`,
      {
        method: "get",
        params: {
          token
        },
        name: "获取菜单权限",
      }
    );
  }
}
export default new ManagementService();
