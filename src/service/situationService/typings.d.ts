export namespace SituationType {
  export interface AttackChinaMapDto {
    /**
     * 攻击次数
     */
    attackCount: number;

    /**
     * 源地址code
     */
    srcCode: number;

    /**
     * 源地址名称
     */
    srcName: string;
  }

  export interface AttackChinaMapResult {
    /**
     * 中国攻击分布图
     */
    attackChinaMapDtoList: AttackChinaMapDto[];
  }

  export interface AttackGuangDongMapDto {
    /**
     * 攻击次数
     */
    attackCount: number;

    /**
     * 攻击地code
     */
    dstCode: number;

    /**
     * 攻击地名称
     */
    dstName: string;
  }

  export interface AttackGuangDongMapResult {
    /**
     * 广东攻击分布图
     */
    attackGuangDongMapDtoList: AttackGuangDongMapDto[];
  }

  export interface AttackSourceListParams extends API.ListParams {
    /**
     * 数据类型
     * 1-境内，2-境外
     */
    dataType: string;
  }

  export interface AttackSourceDto {
    /**
     * 事件数量
     */
    eventCount?: number;
    /**
     * 环比 安全态势大屏使用
     */
    ratio?: number;
    /**
     * 国家/地区
     */
    region?: string;
  }

  export interface AttackedSourceDto {
    /**
     * 事件数量
     */
    eventCount?: number;
    /**
     * 城市/行业名称
     */
    name?: string;
    /**
     * 格式化的事件数量
     */
    formatEventCount: string;
  }

  export interface AttackSourceListResult {
    /**
     * 受攻击列表
     */
    attackedSourceDtoList?: AttackedSourceDto[];
    /**
     * 攻击来源列表
     */
    attackSourceList?: AttackSourceDto[];
    /**
     * 总记录数
     */
    total?: number;
  }

  export interface AttackWorldMapDto {
    /**
     * 攻击次数
     */
    attackCount: number;

    /**
     * 源地址code
     */
    srcCode: string;

    /**
     * 源地址名称
     */
    srcName: string;
  }

  export interface AttackWorldMapResult {
    /**
     * 世界攻击分布图
     */
    attackWorldMapDtoList: AttackWorldMapDto[];
  }

  export interface AttackedSourceListParams extends API.ListParams {
    /**
     * 数据类型
     * 1-地市，2-行业
     */
    dataType: string;
  }

  export interface AttackedSourceListResult {
    /**
     * 受攻击列表
     */
    attackedSourceDtoList?: AttackedSourceDto[];
    /**
     * 总记录数
     */
    total?: number;
    /**
      * 单位
      */
    formatType: string;
  }

  export interface EventTypeSourceDto {
    /**
     * 事件数量
     */
    eventCount: string;
    /**
     * 威胁类型
     */
    eventType: string;
  }

  export interface EventTypeSourceListResult {
    /**
     * 威胁类型分布列表
     */
    eventTypeSourceDtoList: EventTypeSourceDto[];
  }

  export interface SecurePostureDto {
    /**
     * 安全评分
     */
    secureScore: string;

    /**
     * 时间
     */
    time: string;
  }

  export interface SecurePostureResult {
    /**
     * 安全趋势列表
     */
    securePostureDtoList: SecurePostureDto[];
  }

  export interface SecureScoreResult {
    /**
     * 安全评分
     */
    secureScore: string;
  }

  export interface NetworkTrafficTrendDto {
    /**
     * 关基流量
     */
    basicTraffic: string;

    /**
     * 日期，格式（yyyy-MM-dd）
     */
    dateTime: string;

    /**
     * 域名流量
     */
    domainTraffic: string;

    /**
     * 全网流量
     */
    networkTraffic: string;
  }

  export interface TrafficPostureResult {
    /**
     * 全网流量趋势列表
     */
    networkTrafficTrendDtoList: NetworkTrafficTrendDto[];
  }

  export interface SecurityPostureEventListParams extends API.ListParams {
    /**
     * 威胁类型
     */
    eventType?: string;

    /**
     * 攻击IP所属国家
     */
    srcCountry?: string;

    /**
     * 攻击IP所属省
     */
    srcProvince?: string;

    /**
     * 受害单位名称
     */
    unitName?: string;

    /**
     * 受害行业名称
     */
    industryName?: string;

    /**
     * 受害所属城市
     */
    dstCity?: string;
  }

  export interface SecurityPostureEventDto {
    /**
     * 告警数量
     */
    attackCount?: number;
    /**
     * 受害所属城市
     */
    city?: string;
    /**
     * 受害IP
     */
    dstIp?: string;
    /**
     * 受害端口
     */
    dstPort?: number;
    /**
     * 事件时间
     */
    eventTime?: string;
    /**
     * 事件类型
     */
    eventType?: string;
    /**
     * 受害行业名称
     */
    industryName?: string;
    /**
     * 受害是否关基 1-是 0-否
     */
    isCritical?: number;
    /**
     * 攻击所属国家
     */
    srcCountry?: string;
    /**
     * 攻击IP
     */
    srcIp?: string;
    /**
     * 攻击端口
     */
    srcPort?: number;
    /**
     * 攻击所属省
     */
    srcProvince?: string;
    /**
     * 受害单位名称
     */
    unitName?: string;
    /**
     * 受害单位类型
     */
    unitType?: string;
  }

  export interface SecurityPostureEventListResult {
    /**
     * 事件列表
     */
    securityPostureEventDtoList: SecurityPostureEventDto[];

    /**
     * 总数
     */
    total: number;
  }
}

declare global {
  namespace API {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export import Situation = SituationType;
  }
}


