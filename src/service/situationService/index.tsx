import { request } from "umi";
const prefix = "/synergyServer";

class SituationService {
  attackChinaMap(params: API.CommonParams) {
    return request<API.Situation.AttackChinaMapResult>(
      `${prefix}/getAttackChinaMap`,
      {
        method: "get",
        params,
        name: "中国地图",
      }
    );
  }

  attackGuangDongMap(params: API.CommonParams) {
    return request<API.Situation.AttackGuangDongMapResult>(
      `${prefix}/getAttackGuangDongMap`,
      {
        method: "get",
        params,
        name: "广东地图",
      }
    );
  }

  attackSourceList(params: API.Situation.AttackSourceListParams) {
    return request<API.Situation.AttackSourceListResult>(
      `${prefix}/getAttackSourceRespList`,
      {
        method: "get",
        params,
        name: "攻击源分布",
      }
    );
  }

  eventTypeRank(params: API.CommonParams) {
    return request<API.Management.EventTypeRankResult>(
      `${prefix}/getEventTypeRank`,
      {
        method: "get",
        params,
        name: "快处置事件类型排名",
      }
    );
  }

  attackWorldMap(params: API.CommonParams) {
    return request<API.Situation.AttackWorldMapResult>(
      `${prefix}/getAttackWorldMap`,
      {
        method: "get",
        params,
        name: "世界地图",
      }
    );
  }

  attackedSourceRespList(params: API.Situation.AttackedSourceListParams) {
    return request<API.Situation.AttackedSourceListResult>(
      `${prefix}/getAttackedSourceRespList`,
      {
        method: "get",
        params,
        name: "受攻击分布",
      }
    );
  }

  eventTypeSourceList(params: API.ListParams) {
    return request<API.Situation.EventTypeSourceListResult>(
      `${prefix}/getEventTypeSourceList`,
      {
        method: "get",
        params,
        name: "威胁类型分布",
      }
    );
  }

  securePosture(params: API.CommonParams) {
    return request<API.Situation.SecurePostureResult>(
      `${prefix}/getSecurePosture`,
      {
        method: "get",
        params,
        name: "安全趋势",
      }
    );
  }

  secureScore(params: API.CommonParams) {
    return request<API.Situation.SecureScoreResult>(
      `${prefix}/getSecureScore`,
      {
        method: "get",
        params,
        name: "安全评分",
      }
    );
  }

  trafficPosture(params: API.CommonParams) {
    return request<API.Situation.TrafficPostureResult>(
      `${prefix}/getTrafficPosture`,
      {
        method: "get",
        params,
        name: "网络流量趋势",
      }
    );
  }

  securityPostureEventList(params: API.Situation.SecurityPostureEventListParams) {
    return request<API.Situation.SecurityPostureEventListResult>(
      `${prefix}/getSecurityPostureEventList`,
      {
        method: "get",
        params,
        name: "安全事件详单列表",
      }
    );
  }
}
export default new SituationService();
