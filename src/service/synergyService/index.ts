import { request } from "umi";
const prefix = "/synergyServer";

class SynergyService {
  attackedIpList(params: API.ListParams) {
    return request<API.Synergy.AttackedIpListResult>(
      `${prefix}/getAttackedIpList`,
      {
        method: "get",
        params,
        name: "获取被攻击Ip列表接口",
      }
    );
  }

  attackedUniNameList(params: API.ListParams) {
    return request<API.Synergy.AttackedUniNameListResult>(
      `${prefix}/getAttackedUniNameList`,
      {
        method: "get",
        params,
        name: "获取被攻击单位列表接口",
      }
    );
  }

  criticalAttackTypeDistribution(params: API.CommonParams) {
    return request<API.Synergy.CriticalAttackTypeDistributionResult>(
      `${prefix}/getCriticalAttackTypeDistribution`,
      {
        method: "get",
        params,
        name: "攻击类型分布接口",
      }
    );
  }

  criticalEventList(params: API.ListParams) {
    return request<API.Synergy.CriticalEventListResult>(
      `${prefix}/getCriticalEventList`,
      {
        method: "get",
        params,
        name: "获取安全事件列表接口",
      }
    );
  }

  criticalOverview(params: API.CommonParams) {
    return request<API.Synergy.CriticalOverviewResult>(
      `${prefix}/getCriticalOverview`,
      {
        method: "get",
        params,
        name: "获取关基态势接口",
      }
    );
  }

  criticalPosture(params: API.CommonParams) {
    return request<API.Synergy.CriticalPostureResult>(
      `${prefix}/getCriticalPosture`,
      {
        method: "get",
        params,
        name: "威胁态势接口",
      }
    );
  }

  threatInfoList(params: API.ListParams) {
    return request<API.Synergy.ThreatInfoListResult>(
      `${prefix}/getThreatInfoList`,
      {
        method: "get",
        params,
        name: "威胁IP列表",
      }
    );
  }

  vulInfoList(params: API.ListParams) {
    return request<API.Synergy.VulInfoListResult>(
      `${prefix}/getVulInfoList`,
      {
        method: "get",
        params,
        name: "漏洞信息列表",
      }
    );
  }

  securityEventList(params: API.ListParams & { incidentType?: string }) {
    return request<API.Synergy.SecurityEventListResult>(
      `${prefix}/getSecurityEventList`,
      {
        method: "get",
        params,
        name: "安全事件列表",
      }
    );
  }

  eventInfoList(params: API.ListParams) {
    return request<API.Synergy.EventInfoListResult>(
      `${prefix}/getEventInfoList`,
      {
        method: "get",
        params,
        name: "事件信息列表",
      }
    );
  }
}
export default new SynergyService();
