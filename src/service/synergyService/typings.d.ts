import type { DealResultEnum, VulLevelEnum } from "@/enum";
export namespace SynergyType {
  export interface AttackedIpDto {
    /**
     * 受攻击Ip
     */
    attackedIp?: string;
    /**
     * 受攻击Ip所属区域
     */
    attackedIpArea?: string;
    /**
     * 攻击Ip
     */
    attackIp?: string;
    /**
     * 攻击Ip所属区域
     */
    attackIpArea?: string;
    /**
     * 告警次数
     */
    attackNum?: number;
    /**
     * 事件等级 (1-高危/2-中危/3-低危)
     */
    eventLevel?: '1' | '2' | '3';
    /**
     * 单位
     */
    unitName?: string;
  }

  export interface AttackedIpListResult {
    /**
     * 受攻击id列表
     */
    attackedIpDtoList?: AttackedIpDto[];
    /**
     * 总条数
     */
    total?: number;
  }

  export interface AttackedUniNameDto {
    /**
     * 攻击所属国家数
     */
    attackedCountryNum?: string;
    /**
     * 受攻击IP数
     */
    attackedIpNum?: number;
    /**
     * 攻击IP数
     */
    attackIpNum?: number;
    /**
     * 告警次数
     */
    attackNum?: number;
    /**
     * 单位
     */
    unitName?: string;
  }

  export interface AttackedUniNameListResult {
    /**
     * 受攻击列表
     */
    attackedUniNameDtoList?: AttackedUniNameDto[];
    /**
     * 总记录数
     */
    total?: number;
  }

  export interface AttackTypeDistributionDto {
    /**
     * 攻击类型名称
     */
    attackTypeName?: string;
    /**
     * 攻击类型数量
     */
    attackTypeNum?: number;
    /**
     * 攻击类型占比
     */
    attackTypeRate?: string;
    /**
     * 事件类型ID
     */
    incidentType?: string;
  }

  export interface CriticalAttackTypeDistributionResult {
    /**
     * 攻击类型分布列表
     */
    attackTypeDistributionList?: AttackTypeDistributionDto[];
  }

  export interface CriticalEventListResult {
    /**
     * 感威胁安全事件列表
     */
    criticalEventDtoList?: CriticalEventDto[];
    /**
     * 总条数
     */
    total?: number;
  }

  export interface CriticalEventDto {
    /**
     * 被攻击企业
     */
    attackedCompany?: string;
    /**
     * 攻击者ip
     */
    attackerIp?: string;
    /**
     * 攻击者位置
     */
    attackerLocation?: string;
    /**
     * 事件类型
     */
    eventType?: string;
    /**
     * 最新告警时间
     */
    lastAlarmTime?: string;
    /**
     * 受害者ip
     */
    victimIp?: string;
  }

  export interface CriticalOverviewResult {
    /**
     * 全网攻击数
     */
    allAttackNum?: number;
    /**
     * 全网事件数
     */
    allEventNum?: number;
    /**
     * 关基攻击数
     */
    criticalAttackNum?: number;
    /**
     * 关基事件数
     */
    criticalEventNum?: number;
  }

  export interface CriticalPostureResult {
    /**
     * 事件时间点列表
     */
    criticalPostureDtoList?: CriticalPostureDto[];
  }

  export interface CriticalPostureDto {
    /**
     * 攻击次数
     */
    attackNum?: number;
    /**
     * 事件数
     */
    eventNum?: number;
    /**
     * 时间
     */
    time?: string;
  }

  export interface ThreatIpListDto {
    /**
     * 处理结果（deal-已处置 warn-已预警 null-未处置）
     */
    dealResult?: DealResultEnum;

    /**
     * 发现时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    findTime: string;

    /**
     * 威胁Ip
     */
    threatIp: string;

    /**
     * 威胁等级
     */
    threatLevel: string;

    /**
     * 威胁IPID
     */
    threatIpId?: string;

    /**
     * 威胁类型
     */
    threatType: string;

    /**
     * 攻击次数
     */
    attackNum?:number;
  }

  export interface ThreatInfoListResult {
    /**
     * 威胁IP列表
     */
    threatIpListDtoList: ThreatIpListDto[]

    /**
     * 总数
     */
    total: number;
  }

  export interface VulInfoListDto {
    /**
     * 发现时间（格式：yyyy/MM/dd HH:mm:ss）
     */
    discoveryTime: string;

    /**
     * 漏洞正式解决方案
     */
    formalSolution: string;

    /**
     * 漏洞临时解决方案（正式解决方案没有值时使用此字段）
     */
    temporarySolution: string;

    /**
     * 单位名称
     */
    unitName: string;

    /**
     * 漏洞类别（commonVul-通用型漏洞、eventVul-事件型漏洞）
     */
    vulCategory: 'commonVul' | 'eventVul';

    /**
     * 漏洞描述
     */
    vulDescription: string;

    /**
     * 漏洞Id
     */
    vulId: string;

    /**
     * 漏洞等级（1-低危 2-中危 3-高危 4-超危）
     */
    vulLevel: VulLevelEnum;

    /**
     * 漏洞名称
     */
    vulName: string;

    /**
     * 漏洞类型
     */
    vulTypeName: string;
  }

  export interface VulInfoListResult {
    /**
     * 漏洞列表信息
     */
    vulInfoListDtoList: VulInfoListDto[]

    /**
     * 总数
     */
    total: number;
  }

  export interface SecurityEventListDto {
    /**
     * 被攻击企业
     */
    beAttackedFirm: string;

    /**
     * 告警时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    discoveryTime: string;

    /**
     * 受害者IP
     */
    dstIp: string;

    /**
     * 事件ID
     */
    eventId: string;

    /**
     * 事件类型
     */
    eventType: string;

    /**
     * 攻击者IP 
     */
    srcIp: string;

    /**
     * 攻击者位置
     */
    srcLocation: string;
  }

  export interface SecurityEventListResult {
    /**
     * 安全事件列表
     */
    securityEventListDtoList: SecurityEventListDto[];

    /**
     * 总数
     */
    total: number;
  }

  export interface EventInfoListDto {
    /**
     * 研判结果（success-确认攻击，fail-标记误报）
     */
    assessResult: 'success' | 'fail';

    /**
     * 研判状态 (true:已研判 false:未研判)
     */
    assessStatus: boolean;

    /**
     * 事件ID
     */
    eventId: string;

    /**
     * 事件等级
     */
    eventLevel: string;

    /**
     * 事件名称
     */
    eventName: string;

    /**
     * 事件类型
     */
    eventType: string;

    /**
     * 发现时间（格式：yyyy-MM-dd HH:mm:ss）
     */
    foundTime: string;

    /**
     * 单位
     */
    uniName: string;
  }

  export interface EventInfoListResult {
    /** 
     * 事件信息列表
     */
    eventInfoListDtoLit: EventInfoListDto[];

    /**
     * 总数
     */
    total: number;
  }
}

declare global {
  namespace API {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    export import Synergy = SynergyType;
  }
}
