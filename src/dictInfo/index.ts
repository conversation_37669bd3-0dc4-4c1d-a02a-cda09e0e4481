import { DealResultEnum, EventLevelEnum, MapTypeEnum, SearchTypeEnum } from "@/enum";

export default {
  pageType: [
    {
      label: "摸家底",
      value: "/home2",
      code: "3001003"
    },
    {
      label: "感威胁",
      value: "/home4",
      code: "3001001"
    },
    {
      label: "快处置",
      value: "/home1",
      code: "3001002"
    },
    {
      label: "稳保障",
      value: "/home3",
      code: "3001004"
    },
  ],
  mapType: [
    {
      label: "世界地图",
      value: MapTypeEnum.World,
    },
    {
      label: "中国地图",
      value: MapTypeEnum.China,
    },
    {
      label: "广东地图",
      value: MapTypeEnum.Guangdong,
    },
  ],
  eventLevel: [
    {
      label: "高危",
      value: EventLevelEnum.High,
    },
    {
      label: "中危",
      value: EventLevelEnum.Middle,
    },
    {
      label: "低危",
      value: EventLevelEnum.Low,
    },
  ],
  searchType: [
    {
      label: "地市",
      value: SearchTypeEnum.City,
      key: SearchTypeEnum.City,
    },
    {
      label: "行业",
      value: SearchTypeEnum.Industry,
      key: SearchTypeEnum.Industry,
    },
  ],
  realtimeEventStatus: [
    {
      label: "待下发",
      value: "0",
    },
    {
      label: "待审核",
      value: "1",
    },
    {
      label: "待签收",
      value: "2",
    },
    {
      label: "待反馈",
      value: "3",
    },
    {
      label: "待复核",
      value: "4",
    },
    {
      label: "待归档",
      value: "5",
    },
    {
      label: "已归档",
      value: "6",
    },
  ],
  realtimeEventLevel: [
    {
      label: "特别重大事件",
      value: "1",
    },
    {
      label: "重大事件",
      value: "2",
    },
    {
      label: "较大事件",
      value: "3",
    },
    {
      label: "一般事件",
      value: "4",
    },
  ],
  dealResult: [
    {
      label: "已处置",
      value: DealResultEnum.Deal,
    },
    {
      label: "已预警",
      value: DealResultEnum.Warn,
    },
    {
      label: "未处置",
      value: null
    },
  ],
};
