import type { MessageInstance } from "antd/es/message/interface";
import type { ModalStaticFunctions } from "antd/es/modal/confirm";
import dayjs from "dayjs";
import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { persist, createJSONStorage } from "zustand/middleware";

interface GlobalState {
  messageApi: MessageInstance;
  /**
   * antd Modal组件实例
   */
  modalApi: Omit<ModalStaticFunctions, "warn">;

  token: string | null;

  // 右上角筛选条件 开始时间
  startTime: string;

  // 右上角筛选条件 结束时间
  endTime: string;

  // 摸家底默认8年模拟全部
  defaultTime: string;

  defaultTime2: [string, string];
}

const initState: GlobalState = {
  messageApi: undefined!,
  modalApi: undefined!,
  token: null,
  // startTime: "2024-05-30",
  // endTime: "2024-12-18",
  defaultTime: dayjs().add(-8, "year").format("YYYY-MM-DD"),
  startTime: dayjs().add(-7, "day").format("YYYY-MM-DD"),
  endTime: dayjs().format("YYYY-MM-DD"),
  // defaultTime2:['2024-01-01','2025-12-18']
  defaultTime2: ['2024-06-15', '2024-09-23']
};

const useStore = create<GlobalState>()(
  persist(
    immer(() => initState),
    {
      name: "guangdong-state",
      storage: createJSONStorage(() => localStorage),
      partialize(state) {
        return {
          token: state.token,
        };
      },
    }
  )
);

export default useStore;
