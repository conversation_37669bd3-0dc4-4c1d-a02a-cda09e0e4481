import { useBoolean, useMemoizedFn } from "ahooks";
import type { Actions } from "ahooks/lib/useBoolean";
import { nanoid } from "nanoid";
import { useState } from "react";

function useVisible(defaultValue?: boolean): [
  boolean,
  Actions & {
    visibleKey: string;
  },
] {
  const [visible, actions] = useBoolean(defaultValue);
  const [visibleKey, setVisibleKey] = useState(nanoid());

  const setTrue = useMemoizedFn(() => {
    setVisibleKey(nanoid());
    actions.setTrue();
  });

  return [
    visible,
    {
      ...actions,
      setTrue,
      visibleKey,
    },
  ];
}
export default useVisible;
