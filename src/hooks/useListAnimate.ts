import { useEffect, useMemo, useRef, useState } from "react";
import type { AnimationProps } from "framer-motion";
import { useAnimationControls } from "framer-motion";
import { useDeepCompareEffect, useMemoizedFn, useUnmount } from "ahooks";
import { isNumber as _isNumber } from "lodash-es";
import {
  TableAnimationConnectModeEnum,
  TableAnimationModeEnum,
  TableAnimationTypeEnum,
} from "@/enum";

interface IProps {
  rowGap: number;

  size: number;

  listHeight: number;

  itemHeight: number;

  data: any[];

  animationConfig: {
    show: boolean;
    interval: number;
    connectMode: TableAnimationConnectModeEnum;
    animateMode: TableAnimationModeEnum;
    type: TableAnimationTypeEnum;
  };
}
function useListAnimate(props: IProps) {
  const { rowGap, size, listHeight, itemHeight, data, animationConfig } = props;

  const {
    show,
    interval,
    connectMode,
    animateMode,
    type: animationType,
  } = animationConfig;

  const [scrollIndex, setScrollIndex] = useState(-1);

  const dataTotal = useMemo(() => {
    return data?.length ?? 0;
  }, [data?.length]);

  // 动画时长
  const duration = useMemo(() => {
    // if (animationType === TableAnimationTypeEnum.Single) {
    //   return 1;
    // }
    // if (animationType === TableAnimationTypeEnum.All) {
    //   return 1;
    // }
    return 1;
  }, []);

  const getAnimateValue = useMemoizedFn(() => {
    const value: AnimationProps["animate"] = {
      transition: {
        duration,
        ease: "linear",
      },
      transitionEnd: {},
    };

    let callback = () => {};

    let prevCallback = () => {};

    // 动画类型 逐条
    if (animationType === TableAnimationTypeEnum.Single) {
      prevCallback = () => {
        setScrollIndex((val) => {
          const total = data?.length ?? 0;
          if (val >= total - 1) {
            return 0;
          } else {
            return val + 1;
          }
        });
      };
      // 衔接方式 从头开始
      const needFromStart =
        connectMode === TableAnimationConnectModeEnum.FromStart &&
        scrollIndex + size === dataTotal;
      if (needFromStart) {
        value.translateY = [0, -listHeight];
        value.transitionEnd = {
          translateY: -(itemHeight + rowGap),
        };
        callback = () => {
          setScrollIndex(0);
        };
      } else {
        value.translateY = [0, -(itemHeight + rowGap)];
      }
    }

    // 动画类型 全部
    if (animationType === TableAnimationTypeEnum.All) {
      prevCallback = () => {
        setScrollIndex((val) => {
          const total = data?.length ?? 0;
          if (total - val < size) {
            return 0;
          }
          if (val === -1) {
            return 0;
          } else {
            return val + size;
          }
        });
      };
      // 动画形式 从上而下
      if (animateMode === TableAnimationModeEnum.TopToBottom) {
        value.translateY = [0, -(listHeight + rowGap)];
      }
      if (animateMode === TableAnimationModeEnum.Flip) {
        value.opacity = [0.1, 1];
      }
    }

    return {
      value,
      callback,
      prevCallback,
    };
  });

  const controls = useAnimationControls();

  const firstControls = useAnimationControls();

  const timeoutRef = useRef<NodeJS.Timeout>();

  const isMount = useRef(false);

  const onStartAnimateScroll = useMemoizedFn(async () => {
    if (isMount.current || isMount.current === undefined) {
      // 解决卸载调用逻辑有问题的情况
      return;
    }
    if (!show) {
      return;
    }
    firstControls.start({
      opacity: [1, 0],
      transition: {
        duration,
      },
    });

    const { value, callback, prevCallback } = getAnimateValue();

    prevCallback();
    await controls.start(value);
    callback();
    if (_isNumber(interval)) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        onStartAnimateScroll();
      }, interval * 1000);
    }
  });

  const showAnimate = useMemo(() => {
    return show && dataTotal > size;
  }, [dataTotal, show, size]);

  useUnmount(() => {
    controls.mount();
    firstControls.mount();
    isMount.current = true;
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  });

  const dataKey = useMemo(() => JSON.stringify(data), [data]);

  useDeepCompareEffect(() => {
    setScrollIndex(-1);
    controls.stop();
    controls.set({});
    if (showAnimate) {
      onStartAnimateScroll();
    }
  }, [showAnimate]);

  useEffect(() => {
    // 当数据发生改变 重新从第一个开始渲染
    setScrollIndex(0);
    return () => {};
  }, [dataKey]);

  const dataIndexList = useMemo(() => {
    const list = data.map((_, index) => index) as number[];
    const sliceIndex = scrollIndex === -1 ? 0 : scrollIndex;
    let result = [] as number[];
    if (animationType === TableAnimationTypeEnum.Single) {
      if (connectMode === TableAnimationConnectModeEnum.FromStart) {
        result = list.slice(sliceIndex);
      }
      if (connectMode === TableAnimationConnectModeEnum.Continuous) {
        result = list?.slice(sliceIndex).concat(list?.slice(0, sliceIndex));
      }
    }
    if (animationType === TableAnimationTypeEnum.All) {
      result = list.slice(sliceIndex);
    }
    // return result.slice(0, size);
    return result;
  }, [animationType, connectMode, data, scrollIndex]);

  const beforeIndexList = useMemo(() => {
    const list = data.map((_, index) => index) as number[];
    const sliceIndex = scrollIndex === -1 ? 0 : scrollIndex;
    if (animationType === TableAnimationTypeEnum.Single) {
      return [scrollIndex - 1 >= 0 ? scrollIndex - 1 : 0];
    }
    if (animationType === TableAnimationTypeEnum.All) {
      if (animateMode === TableAnimationModeEnum.Flip) {
        return [];
      }
      if (sliceIndex === 0) {
        return list.slice(0, size);
      }
      return list.slice(0, sliceIndex).slice(0, size);
    }
  }, [animateMode, animationType, data, scrollIndex, size]);

  return {
    controls,
    firstControls,
    dataIndexList,
    showAnimate,
    beforeIndexList,
    scrollIndex,
  };
}
export default useListAnimate;
