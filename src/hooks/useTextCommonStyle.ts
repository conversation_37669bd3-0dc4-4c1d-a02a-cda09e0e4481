import { useMemo } from "react";
import { ColorTypeEnum } from "@/enum";
import type { TextCommonConfig } from "@/typings";
import { formatLinear } from "@/utils";

interface IProps {
  config: TextCommonConfig;
}
function useTextCommonStyle(props: IProps) {
  const { config } = props;

  const colorValue = useMemo(() => {
    const { pure } = config.color;
    switch (config.color.type) {
      case ColorTypeEnum.Linear:
        return {
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          backgroundImage: formatLinear(config.color.linear),
          opacity: config.color.linear.opacity,
        };

      default:
        return {
          color: pure,
        };
    }
  }, [config.color]);

  const value: React.CSSProperties = useMemo(() => {
    const { fontFamily, fontSize, lineHeight, letterSpacing, bold, italic } =
      config;

    return {
      fontFamily,
      fontSize: `${fontSize}px`,
      lineHeight: `${lineHeight}px`,
      letterSpacing,
      fontWeight: bold ? "bold" : "normal",
      fontStyle: italic ? "italic" : "normal",
      ...colorValue,
    };
  }, [config, colorValue]);

  return value;
}

export default useTextCommonStyle;
