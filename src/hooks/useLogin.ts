import { useSearchParams } from "umi";
import useStore from "@/store";
import { useEffect, useMemo } from "react";

function useLogin() {
  const token = useStore((state) => state.token);

  const [searchParams] = useSearchParams();

  // 页面参数
  const queryToken = searchParams.get("token");

  const tokenValue = useMemo(() => queryToken ?? token, [queryToken, token]);

  useEffect(() => {
    if (queryToken) {
      useStore.setState((draft) => {
        draft.token = queryToken;
      });
    }
  }, [queryToken]);

  useEffect(() => {
    if (!tokenValue && process.env.loginUrl) {
      window.location.href = process.env.loginUrl;
    }
  }, [tokenValue]);
}
export default useLogin;
