<svg id="受攻击单位" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="138" height="128" viewBox="0 0 138 128">
  <defs>
    <style>
      .cls-1 {
        fill: #3657c1;
        fill-opacity: 0.5;
        opacity: 0.4;
        filter: url(#filter);
      }

      .cls-1, .cls-10, .cls-11, .cls-12, .cls-14, .cls-15, .cls-16, .cls-17, .cls-18, .cls-19, .cls-2, .cls-20, .cls-21, .cls-22, .cls-23, .cls-24, .cls-25, .cls-26, .cls-3, .cls-4, .cls-7, .cls-8, .cls-9 {
        fill-rule: evenodd;
      }

      .cls-10, .cls-11, .cls-15, .cls-18, .cls-2, .cls-20, .cls-21, .cls-22, .cls-4 {
        fill: none;
      }

      .cls-2 {
        stroke: #2c7cc7;
        filter: url(#filter-2);
      }

      .cls-10, .cls-11, .cls-15, .cls-16, .cls-17, .cls-18, .cls-2, .cls-20, .cls-21, .cls-22, .cls-4 {
        stroke-width: 1px;
      }

      .cls-2, .cls-9 {
        opacity: 0.8;
      }

      .cls-12, .cls-3 {
        fill: #113867;
      }

      .cls-11, .cls-4 {
        stroke: #56dded;
      }

      .cls-19, .cls-20, .cls-4 {
        opacity: 0.2;
      }

      .cls-5 {
        fill: #2b73f0;
      }

      .cls-13, .cls-6 {
        fill: #1ee6ed;
      }

      .cls-26, .cls-7, .cls-8 {
        fill: #18ccf6;
      }

      .cls-8 {
        opacity: 0.6;
      }

      .cls-9 {
        fill: #071835;
        fill-opacity: 0.8;
      }

      .cls-10 {
        stroke: #1858c8;
      }

      .cls-10, .cls-17, .cls-18, .cls-21, .cls-23, .cls-25 {
        opacity: 0.3;
      }

      .cls-12 {
        fill-opacity: 0.2;
      }

      .cls-13, .cls-16 {
        opacity: 0.5;
      }

      .cls-14 {
        fill: #d46951;
        filter: url(#filter-3);
      }

      .cls-15 {
        stroke: url(#linear-gradient);
      }

      .cls-16 {
        fill: url(#linear-gradient-2);
        stroke: url(#linear-gradient-3);
      }

      .cls-17 {
        fill: url(#linear-gradient-4);
        stroke: url(#linear-gradient-5);
      }

      .cls-18 {
        stroke: url(#linear-gradient-6);
      }

      .cls-19 {
        fill: url(#linear-gradient-7);
      }

      .cls-20 {
        stroke: url(#linear-gradient-8);
      }

      .cls-21 {
        stroke-linecap: round;
        stroke-dasharray: 0.001 2;
        stroke: url(#linear-gradient-9);
      }

      .cls-22 {
        stroke: url(#linear-gradient-10);
      }

      .cls-23, .cls-24 {
        fill: #56dded;
      }

      .cls-23 {
        filter: url(#filter-4);
      }

      .cls-24 {
        filter: url(#filter-5);
      }

      .cls-25 {
        fill: #f08550;
        filter: url(#filter-6);
      }

      .cls-26 {
        filter: url(#filter-7);
      }
    </style>
    <filter id="filter" x="0" y="-0.156" width="138" height="128.156" filterUnits="userSpaceOnUse">
      <feGaussianBlur result="blur" stdDeviation="5.333" in="SourceAlpha"/>
      <feFlood result="flood" flood-color="#0c2c64"/>
      <feComposite result="composite-2" operator="out" in2="blur"/>
      <feComposite result="composite" operator="in" in2="SourceAlpha"/>
      <feBlend result="blend" in2="SourceGraphic"/>
      <feGaussianBlur result="blur-2" stdDeviation="5.196" in="SourceAlpha"/>
      <feFlood result="flood-2" flood-color="#2490ff" flood-opacity="0.35"/>
      <feComposite result="composite-3" operator="out" in2="blur-2"/>
      <feOffset result="offset" dy="14"/>
      <feComposite result="composite-4" operator="in" in2="SourceAlpha"/>
      <feBlend result="blend-2" in2="blend"/>
    </filter>
    <filter id="filter-2" x="0" y="-0.156" width="138" height="128.156" filterUnits="userSpaceOnUse">
      <feGaussianBlur result="blur" stdDeviation="5.333" in="SourceAlpha"/>
      <feFlood result="flood" flood-color="#0c2c64"/>
      <feComposite result="composite-2" operator="out" in2="blur"/>
      <feComposite result="composite" operator="in" in2="SourceAlpha"/>
      <feBlend result="blend" in2="SourceGraphic"/>
    </filter>
    <filter id="filter-3" x="422" y="54" width="181" height="25" filterUnits="userSpaceOnUse">
      <feGaussianBlur result="blur" stdDeviation="2.333" in="SourceAlpha"/>
      <feComposite result="composite"/>
      <feComposite result="composite-2"/>
      <feComposite result="composite-3"/>
      <feFlood result="flood" flood-color="#d46951" flood-opacity="0.3"/>
      <feComposite result="composite-4" operator="in" in2="composite-3"/>
      <feBlend result="blend" in2="SourceGraphic"/>
      <feBlend result="blend-2" in="SourceGraphic"/>
      <feImage preserveAspectRatio="none" x="428" y="60" width="167.969" height="11.594" result="image" xlink:href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB3aWR0aD0iMTY3Ljk2OSIgaGVpZ2h0PSIxMS41OTQiIHZpZXdCb3g9IjAgMCAxNjcuOTY5IDExLjU5NCI+CiAgPGRlZnM+CiAgICA8c3R5bGU+CiAgICAgIC5jbHMtMSB7CiAgICAgICAgb3BhY2l0eTogMC4xNTsKICAgICAgICBmaWxsOiB1cmwoI2xpbmVhci1ncmFkaWVudCk7CiAgICAgIH0KICAgIDwvc3R5bGU+CiAgICA8bGluZWFyR3JhZGllbnQgaWQ9ImxpbmVhci1ncmFkaWVudCIgeDE9Ijg0LjQ5MiIgeTE9IjExLjU5NCIgeDI9IjgzLjQ3NyIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAiIHN0b3AtY29sb3I9IiMwMzAwMDAiLz4KICAgICAgPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjZmZmIi8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCBjbGFzcz0iY2xzLTEiIHdpZHRoPSIxNjcuOTY5IiBoZWlnaHQ9IjExLjU5NCIvPgo8L3N2Zz4K"/>
      <feComposite result="composite-5" operator="in" in2="SourceGraphic"/>
      <feBlend result="blend-3" mode="overlay" in2="blend-2"/>
    </filter>
    <linearGradient id="linear-gradient" x1="356.839" y1="110" x2="355.161" y2="78" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#2c7cc7"/>
      <stop offset="1" stop-color="#2c7cc7" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="356" y1="110" x2="356" y2="78" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#45adf5" stop-opacity="0.2"/>
      <stop offset="1" stop-color="#358ae1" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="356.839" y1="110" x2="355.161" y2="78" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3657c1"/>
      <stop offset="1" stop-color="#3657c1" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" y1="117" y2="85" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-5" x1="356.839" y1="117" x2="355.161" y2="85" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-6" x1="210" y1="112" x2="210" y2="87" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#18ccf6"/>
      <stop offset="0.175" stop-color="#18ccf6"/>
      <stop offset="0.656" stop-color="#3398ff"/>
      <stop offset="1" stop-color="#3398ff" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="210" y1="112" x2="210" y2="42.969" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-8" y1="100.781" y2="74.219" xlink:href="#linear-gradient-6"/>
    <linearGradient id="linear-gradient-9" x1="209.5" y1="82" x2="209.5" y2="50" xlink:href="#linear-gradient-6"/>
    <linearGradient id="linear-gradient-10" x1="210" y1="104.781" x2="210" y2="78.219" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#37d9ff"/>
      <stop offset="0.175" stop-color="#18ccf6"/>
      <stop offset="0.656" stop-color="#3398ff"/>
      <stop offset="1" stop-color="#3398ff" stop-opacity="0"/>
    </linearGradient>
    <filter id="filter-4" x="193.969" y="61" width="30.25" height="30.25" filterUnits="userSpaceOnUse">
      <feFlood result="flood" flood-color="#30b7f7"/>
      <feComposite result="composite" operator="in" in2="SourceGraphic"/>
      <feBlend result="blend" in2="SourceGraphic"/>
    </filter>
    <filter id="filter-5" x="184" y="48" width="50" height="49" filterUnits="userSpaceOnUse">
      <feOffset result="offset" dy="1" in="SourceAlpha"/>
      <feGaussianBlur result="blur" stdDeviation="2.449"/>
      <feFlood result="flood" flood-color="#143c57" flood-opacity="0.49"/>
      <feComposite result="composite" operator="in" in2="blur"/>
      <feGaussianBlur result="blur-2" stdDeviation="3" in="SourceAlpha"/>
      <feComposite result="composite-2"/>
      <feComposite result="composite-3"/>
      <feComposite result="composite-4"/>
      <feFlood result="flood-2" flood-color="#1348d5" flood-opacity="0.61"/>
      <feComposite result="composite-5" operator="in" in2="composite-4"/>
      <feBlend result="blend" in2="composite"/>
      <feBlend result="blend-2" in="SourceGraphic"/>
      <feGaussianBlur result="blur-3" stdDeviation="1.333" in="SourceAlpha"/>
      <feFlood result="flood-3" flood-color="#65c9ff"/>
      <feComposite result="composite-7" operator="out" in2="blur-3"/>
      <feComposite result="composite-6" operator="in" in2="SourceAlpha"/>
      <feBlend result="blend-3" mode="overlay" in2="blend-2"/>
    </filter>
    <filter id="filter-6" x="802" y="58" width="31.5" height="31.5" filterUnits="userSpaceOnUse">
      <feFlood result="flood" flood-color="#30b7f7"/>
      <feComposite result="composite" operator="in" in2="SourceGraphic"/>
      <feBlend result="blend" in2="SourceGraphic"/>
    </filter>
    <filter id="filter-7" x="793" y="46" width="50" height="50" filterUnits="userSpaceOnUse">
      <feOffset result="offset" dy="1" in="SourceAlpha"/>
      <feGaussianBlur result="blur" stdDeviation="2.449"/>
      <feFlood result="flood" flood-color="#143c57" flood-opacity="0.49"/>
      <feComposite result="composite" operator="in" in2="blur"/>
      <feGaussianBlur result="blur-2" stdDeviation="3" in="SourceAlpha"/>
      <feComposite result="composite-2"/>
      <feComposite result="composite-3"/>
      <feComposite result="composite-4"/>
      <feFlood result="flood-2" flood-color="#1348d5" flood-opacity="0.61"/>
      <feComposite result="composite-5" operator="in" in2="composite-4"/>
      <feBlend result="blend" in2="composite"/>
      <feBlend result="blend-2" in="SourceGraphic"/>
      <feGaussianBlur result="blur-3" stdDeviation="1.333" in="SourceAlpha"/>
      <feFlood result="flood-3" flood-color="#65c9ff"/>
      <feComposite result="composite-7" operator="out" in2="blur-3"/>
      <feComposite result="composite-6" operator="in" in2="SourceAlpha"/>
      <feBlend result="blend-3" mode="overlay" in2="blend-2"/>
    </filter>
  </defs>
  <g id="_3" data-name="3">
    <path id="形状_683_拷贝_10" data-name="形状 683 拷贝 10" class="cls-1" d="M137.847,11.841L138,128H12L1,116,0,12,11.681-.159H125.87Z"/>
    <g style="fill: none; filter: url(#filter-2)">
      <path id="形状_683_拷贝_10-2" data-name="形状 683 拷贝 10" class="cls-2" d="M137.847,11.841L138,128H12L0,116V12L11.681-.159H125.87Z" style="stroke: inherit; filter: none; fill: inherit"/>
    </g>
    <use xlink:href="#形状_683_拷贝_10-2" style="stroke: #2c7cc7; filter: none; fill: none"/>
    <g id="命令">
      <path id="名称背景" class="cls-3" d="M138,33.752V12L127,0.973H12L1,12.893V33.752H138Z"/>
      <image id="图层_1323_拷贝_灰大素材" data-name="图层 1323 拷贝 灰大素材" width="138" height="29" xlink:href="data:img/png;base64,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"/>
      <path id="形状_683_拷贝_10-3" data-name="形状 683 拷贝 10" class="cls-4" d="M138,33.752V12L126.606-.02H11L0,11.9V33.752H138Z"/>
      <rect id="圆角矩形_686_拷贝_4" data-name="圆角矩形 686 拷贝 4" class="cls-5" x="0.563" y="33.156" width="136.438" height="0.844"/>
      <rect id="圆角矩形_686_拷贝_4-2" data-name="圆角矩形 686 拷贝 4" class="cls-6" x="11.563" y="33.156" width="136.438" height="0.844"/>
      <path id="形状_701_拷贝_4" data-name="形状 701 拷贝 4" class="cls-7" d="M15.3,21.162V16.093l3.6,2.535Z"/>
      <path id="形状_701_拷贝_4-2" data-name="形状 701 拷贝 4" class="cls-8" d="M10.476,21.162V16.093l3.6,2.535Z"/>
      <path id="形状_701_拷贝_4-3" data-name="形状 701 拷贝 4" class="cls-7" d="M124.268,16.1v5.069l-3.6-2.535Z"/>
      <path id="形状_701_拷贝_4-4" data-name="形状 701 拷贝 4" class="cls-8" d="M129.1,16.1v5.069l-3.6-2.535Z"/>
    </g>
    <path id="形状_683_拷贝_11" data-name="形状 683 拷贝 11" class="cls-9" d="M897.847,11.841v104.5L886,129l-746-1V13L151.681-.159H885.87Z"/>
    <g id="内容背景">
      <path id="形状_683_拷贝_12" data-name="形状 683 拷贝 12" class="cls-10" d="M897.847,11.841v104.5l-11.989,12.011L140,128V12L152,0,885.87-.159Z"/>
      <path id="形状_683_拷贝_13" data-name="形状 683 拷贝 13" class="cls-11" d="M897.847,11.841v114.5l-11.989,12.011L140,138V12L151.681-.159H885.87Z"/>
      <g id="组_6839_拷贝" data-name="组 6839 拷贝">
        <path id="名称背景-2" data-name="名称背景" class="cls-12" d="M141,34V12L152,1H886l11,12V34H141Z"/>
        <image id="图层_1323_拷贝_灰大素材-2" data-name="图层 1323 拷贝 灰大素材" y="4" width="138" height="33" xlink:href="data:img/png;base64,iVBORw0KGgoAAAANSUhEUgAAAvQAAAAhCAYAAAC2sD+jAAABWklEQVR4nO3dvUoDQRSG4S/GEFIEIjb+FCLiPXhHXrngPdjIyigh5GewSObA81Rb7HJO+RbLzOzm7T2d7pM89L4MAAD820eSz56PrzoniHkAADifxyS3PdN6gl7MAwDA+T0l2ZyaeiroxTwAAFzGLMlzkvWx6ceCXswDAMBlTb3+kmR1aItDQS/mAQBgDPMkr0mW+7bZF/RiHgAAxrJoUb/Y3Wo36MU8AACMadmifr693XbQi3kAABjbqv1T/9fxvw9iHgAAali302+mU3B+gl7MAwBALZt2Tn2uxTwAAJQ03ST71XNTLAAAMKY7QQ8AAIUJegAAKEzQAwBAYYIeAAAKE/QAAFCYoAcAgMIEPQAAFCboAQCgMEEPAACFCXoAAChM0AMAQGGCHgAAChP0AABQmKAHAIDCBD0AABQm6AEAoDBBDwAAhQl6AAAoTNADAEBhgh4AAKpK8g0E0ggFe3bK4AAAAABJRU5ErkJggg=="/>
        <rect id="圆角矩形_686_拷贝_5" data-name="圆角矩形 686 拷贝 5" class="cls-13" x="138" y="33" width="374" height="1"/>
      </g>
      <path id="攻击箭头" class="cls-14" d="M475.988,65.792h-15.9l-3.976-5.354Zm0,0h-15.9l-3.976,5.354Zm-19.877,0h-3.8L448.685,60Zm0,0h-3.8l-3.626,5.791Zm-12.392-.823h2.469v2.469h-2.469V64.969Zm-4.969,0h2.5v2.469h-2.5V64.969Zm-5.781,0h2.469v2.469h-2.469V64.969Zm-4.969,0h2.469v2.469H428V64.969Zm108.388,0.823h-15.9l-3.975-5.354Zm0,0h-15.9l-3.975,5.354Zm-19.877,0h-3.8L509.084,60Zm0,0h-3.8l-3.626,5.791Zm-12.386-.823h2.469v2.469h-2.469V64.969Zm-4.969,0h2.469v2.469h-2.469V64.969Zm-5.781,0h2.469v2.469h-2.469V64.969Zm-4.969,0h2.469v2.469h-2.469V64.969ZM595.96,65.792h-15.9l-3.976-5.354Zm0,0h-15.9l-3.976,5.354Zm-19.878,0h-3.8L568.656,60Zm0,0h-3.8l-3.626,5.791Zm-12.394-.823h2.5v2.469h-2.5V64.969Zm-4.969,0h2.5v2.469h-2.5V64.969Zm-5.781,0h2.468v2.469h-2.468V64.969Zm-4.969,0h2.5v2.469h-2.5V64.969Z"/>
    </g>
    <path id="形状_683_拷贝_10-4" data-name="形状 683 拷贝 10" class="cls-11" d="M-0.122,32.063L-0.128,11.986,11.837,0l14.2,0"/>
  </g>
  <g id="数据_拷贝_2" data-name="数据 拷贝 2">
    <g id="底座左">
      <path id="形状_1040" data-name="形状 1040" class="cls-15" d="M429,78l41,24-6,8H242l3-28"/>
      <path id="形状_1040_拷贝_3" data-name="形状 1040 拷贝 3" class="cls-16" d="M429,78l41,24-6,8H242l3-28"/>
      <path id="形状_1040_拷贝_2" data-name="形状 1040 拷贝 2" class="cls-17" d="M429,85l41,24-6,8H242l3-28"/>
      <path id="椭圆_1033" data-name="椭圆 1033" class="cls-18" d="M210,87c19.882,0,36,5.6,36,12.5S229.882,112,210,112s-36-5.6-36-12.5S190.118,87,210,87Z"/>
      <path id="椭圆_1033_拷贝_4" data-name="椭圆 1033 拷贝 4" class="cls-19" d="M209,43c7.445,0,58.3-1,55,9-5.519,16.751-16.047,42.612-18,47.5-2.562,6.411-16.118,12.5-36,12.5s-33.208-5.617-37-12.5c-2.41-4.374-12.41-30.216-17-46.5C152.861,41.865,200.81,43,209,43Z"/>
      <path id="椭圆_1033_拷贝_2" data-name="椭圆 1033 拷贝 2" class="cls-20" d="M210,74.219c21.125,0,38.25,5.946,38.25,13.281S231.125,100.781,210,100.781,171.75,94.835,171.75,87.5,188.875,74.219,210,74.219Z"/>
      <path id="椭圆_1033_拷贝_3" data-name="椭圆 1033 拷贝 3" class="cls-21" d="M209.5,50c26.786,0,48.5,7.163,48.5,16s-21.714,16-48.5,16S161,74.837,161,66,182.714,50,209.5,50Z"/>
      <path id="椭圆_1033_拷贝" data-name="椭圆 1033 拷贝" class="cls-22" d="M210,78.219c21.125,0,38.25,5.946,38.25,13.281S231.125,104.781,210,104.781,171.75,98.835,171.75,91.5,188.875,78.219,210,78.219Z"/>
    </g>
    <path id="ip地址" class="cls-23" d="M209.1,61a10.4,10.4,0,0,0-10.588,10.209c0,5.6,4.311,10.663,9.831,13.763l0.757,0.454,0.756-.454c5.52-3.176,9.831-8.167,9.831-13.763A10.4,10.4,0,0,0,209.1,61Zm0,15.125a4.537,4.537,0,1,1,4.537-4.537A4.551,4.551,0,0,1,209.1,76.125Zm-8.319,6.05H201a35.309,35.309,0,0,0,8.092,6.05,35.325,35.325,0,0,0,8.091-6.05h0.378a15.014,15.014,0,0,1,4.387,1.361c0.983,0.529,2.268,1.361,2.268,2.8a2.972,2.972,0,0,1-1.739,2.5,12.228,12.228,0,0,1-3.327,1.286A38.409,38.409,0,0,1,209.1,91.249a42.285,42.285,0,0,1-10.058-1.059,11.319,11.319,0,0,1-3.328-1.286,3.2,3.2,0,0,1-1.739-2.571c0-1.437,1.286-2.344,2.269-2.8a17.693,17.693,0,0,1,4.386-1.361h0.151Z"/>
    <path id="ip地址_拷贝" data-name="ip地址 拷贝" class="cls-24" d="M209.1,57a10.4,10.4,0,0,0-10.588,10.209c0,5.6,4.311,10.663,9.831,13.763l0.757,0.454,0.756-.454c5.52-3.176,9.831-8.167,9.831-13.763A10.4,10.4,0,0,0,209.1,57Zm0,15.125a4.537,4.537,0,1,1,4.537-4.537A4.551,4.551,0,0,1,209.1,72.125Zm-8.319,6.05H201a35.312,35.312,0,0,0,8.092,6.05,35.327,35.327,0,0,0,8.091-6.05h0.378a15,15,0,0,1,4.386,1.361c0.984,0.529,2.269,1.361,2.269,2.8a2.972,2.972,0,0,1-1.739,2.5,12.223,12.223,0,0,1-3.328,1.286A38.4,38.4,0,0,1,209.1,87.249a42.279,42.279,0,0,1-10.058-1.059,11.319,11.319,0,0,1-3.328-1.286,3.2,3.2,0,0,1-1.739-2.571c0-1.437,1.286-2.344,2.269-2.8a17.693,17.693,0,0,1,4.386-1.361h0.151Z"/>
  </g>
  <g id="数据_拷贝_3" data-name="数据 拷贝 3">
    <path id="受攻击" class="cls-25" d="M816.191,61.522a1.13,1.13,0,0,1,.973-0.556l1.4,0a1.127,1.127,0,0,1,.976.567l2.676,4.68,2.573-1.467-3.531-6.172a1.125,1.125,0,0,0-.973-0.567L815.441,58a1.132,1.132,0,0,0-.976.556L810.7,64.973l2.579,1.505ZM833.5,69.154l-3.144-1.991-0.167-3.726-7.443,8.091Zm-28.167-5.767-0.165,3.753-3.167,2,10.826,2.41ZM814.792,89.5l2.953-1.539,2.969,1.539-2.969-9.4Zm14.674-16.461-2.588,1.492,2.863,4.984a1.126,1.126,0,0,1,0,1.12l-0.7,1.213a1.131,1.131,0,0,1-.978.565h-5.4v2.959h7.114a1.128,1.128,0,0,0,.976-0.565l2.415-4.2a1.121,1.121,0,0,0,0-1.12l-3.706-6.451h0Zm-23.05,8.829-1.02-1.793,3.185-5.494-2.588-1.492-3.708,6.426a1.125,1.125,0,0,0,0,1.123l2.413,4.194a1.128,1.128,0,0,0,.977.565h7.213V82.437h-5.494A1.123,1.123,0,0,1,806.416,81.868Zm8.432-11a2.868,2.868,0,1,0,2.868-2.865,2.866,2.866,0,0,0-2.868,2.865h0Zm-2.816,8.462h11.5A5.75,5.75,0,0,0,812.032,79.335Z"/>
    <path id="受攻击_拷贝" data-name="受攻击 拷贝" class="cls-26" d="M816.191,58.522a1.13,1.13,0,0,1,.973-0.556l1.4,0a1.127,1.127,0,0,1,.976.567l2.676,4.68,2.573-1.467-3.53-6.172a1.128,1.128,0,0,0-.974-0.567L815.441,55a1.132,1.132,0,0,0-.976.556L810.7,61.973l2.579,1.505ZM833.5,66.154l-3.144-1.991-0.167-3.726-7.443,8.091Zm-28.167-5.767-0.165,3.753-3.167,2,10.826,2.41ZM814.792,86.5l2.953-1.539,2.969,1.539-2.969-9.4Zm14.674-16.461-2.588,1.492,2.863,4.984a1.126,1.126,0,0,1,0,1.12l-0.7,1.213a1.131,1.131,0,0,1-.978.565h-5.4v2.959h7.114a1.128,1.128,0,0,0,.976-0.565l2.415-4.2a1.121,1.121,0,0,0,0-1.12l-3.706-6.451h0Zm-23.05,8.829-1.02-1.793,3.185-5.495-2.588-1.492-3.708,6.426a1.125,1.125,0,0,0,0,1.123l2.413,4.194a1.128,1.128,0,0,0,.977.565h7.213V79.437h-5.494A1.123,1.123,0,0,1,806.416,78.868Zm8.432-11a2.868,2.868,0,1,0,2.869-2.865,2.866,2.866,0,0,0-2.869,2.865h0Zm-2.816,8.462h11.5A5.75,5.75,0,0,0,812.032,76.335Z"/>
  </g>
</svg>
