<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="35" viewBox="0 0 22 35">
  <defs>
    <style>
      .cls-1 {
        fill: #ffdf95;
        fill-rule: evenodd;
        filter: url(#filter);
      }
    </style>
    <filter id="filter" x="158" y="100" width="22" height="35" filterUnits="userSpaceOnUse">
      <feGaussianBlur result="blur" stdDeviation="2.333" in="SourceAlpha"/>
      <feComposite result="composite"/>
      <feComposite result="composite-2"/>
      <feComposite result="composite-3"/>
      <feFlood result="flood" flood-color="#1ea2f6" flood-opacity="0.35"/>
      <feComposite result="composite-4" operator="in" in2="composite-3"/>
      <feBlend result="blend" in2="SourceGraphic"/>
      <feBlend result="blend-2" in="SourceGraphic"/>
    </filter>
  </defs>
  <path id="形状_705_拷贝_3" data-name="形状 705 拷贝 3" class="cls-1" d="M171.053,122.148L165.7,127.1l6.477-4.089,0.274-.173V111.949l-0.274-.174-6.477-4.089,5.355,4.951v9.511Z" transform="translate(-158 -100)"/>
</svg>
