<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="22" height="35" viewBox="0 0 22 35">
  <defs>
    <style>
      .cls-1 {
        fill: #ffdf95;
        fill-rule: evenodd;
        filter: url(#filter);
      }
    </style>
    <filter id="filter" x="80" y="100" width="22" height="35" filterUnits="userSpaceOnUse">
      <feGaussianBlur result="blur" stdDeviation="2.333" in="SourceAlpha"/>
      <feComposite result="composite"/>
      <feComposite result="composite-2"/>
      <feComposite result="composite-3"/>
      <feFlood result="flood" flood-color="#1ea2f6" flood-opacity="0.35"/>
      <feComposite result="composite-4" operator="in" in2="composite-3"/>
      <feBlend result="blend" in2="SourceGraphic"/>
      <feBlend result="blend-2" in="SourceGraphic"/>
    </filter>
  </defs>
  <path id="形状_705_拷贝_3" data-name="形状 705 拷贝 3" class="cls-1" d="M89.363,112.64l5.354-4.952-6.476,4.089-0.275.174v10.888l0.275,0.173,6.476,4.089-5.354-4.951v-9.51Z" transform="translate(-80 -100)"/>
</svg>
