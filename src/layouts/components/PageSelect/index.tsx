import { Select } from "antd";
import dictInfo from "@/dictInfo";
import { history, useLocation } from "umi";
import BgImage from "@/components/BgImage";
import selectIcon from "@/assets/4/selectIcon.png";
import useStore from "@/store";
import dayjs from "dayjs";
import { useRequest } from "ahooks";
import managementService from "@/service/managementService";
import { useMemo } from "react";

function PageSelect() {
  const { pathname } = useLocation();

  const onChange = (value: string) => {
    if (value === pathname) {
      return;
    }

    useStore.setState((draft) => {
      if (value === "/home2") {
        // 摸底家页面默认时间变成近8年模拟全部
        draft.startTime = draft.defaultTime;
      } else if (value === "/home3") {
        // 稳保障
        const [startTime, endTime] = draft.defaultTime2;
        draft.startTime = startTime;
        draft.endTime = endTime;
      } else {
        if (
          (draft.startTime === draft.defaultTime && pathname === "/home2") ||
          pathname === "/home3"
        ) {
          // 判断当前时间选中的是全部并且是从摸家底切换到其它页面 则修改成默认7天
          draft.startTime = dayjs().add(-7, "day").format("YYYY-MM-DD");
          draft.endTime = dayjs().format("YYYY-MM-DD");
        }
      }
    });
    history.push(value);
  };

  const token = useStore((state) => state.token) ?? '1';

  const { data } = useRequest(async () => {
    return await managementService.getMenuPermission(token!)
  }, {
    ready: !!token
  })

  const menuList = useMemo(() => {
    const ids = data?.menuPermissionDtoList?.map((item) => item.menuCode);
    return dictInfo.pageType.filter((item) => ids?.includes(item.code));
  }, [data?.menuPermissionDtoList])

  return (
    <div className="absolute top-[14px] left-[84px] z-[100]">
      <Select
        className="w-[310px] h-[64px] !font-bold text-center"
        value={pathname}
        onChange={onChange}
        labelRender={(label) => (
          <div className="flex w-full text-[#B4E3FF] justify-center gap-x-2 items-center pl-2 font-bold">
            <span className="text-[36px] leading-[36px]">{label.label}</span>
            <BgImage className="size-[32px]" url={selectIcon} />
          </div>
        )}
        getPopupContainer={(triggerNode) => triggerNode.parentElement!}
        suffixIcon={false}
        variant="borderless"
        options={menuList}
      ></Select>
    </div>
  );
}
export default PageSelect;
