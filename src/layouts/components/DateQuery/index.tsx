import { DateTypeEnum } from "@/enum";
import dayjs from "dayjs";
import { useCallback, useMemo, useState } from "react";
import type { TabsProps } from "antd";
import { DatePicker, Tabs } from "antd";
import React from "react";
import Title from "@/components/Title";
import useStore from "@/store";

const { RangePicker } = DatePicker;

function DateQuery() {
  const startTime = useStore((state) => state.startTime);

  const endTime = useStore((state) => state.endTime);

  const defaultTime = useStore((state) => state.defaultTime);

  const setStartTime = useCallback((value: string) => {
    useStore.setState((draft) => {
      draft.startTime = value;
    });
  }, []);

  const setEndTime = useCallback((value: string) => {
    useStore.setState((draft) => {
      draft.endTime = value;
    });
  }, []);

  const [open, setOpen] = useState(false);

  const activeKey = useMemo(() => {
    const startDayJs = dayjs(startTime);
    const endDayJs = dayjs(endTime);
    const differenceInDays = endDayJs.diff(startDayJs, "day");
    if (startTime === defaultTime) {
      // 8年等于默认的全部
      return "";
    }
    switch (differenceInDays) {
      case 7:
        return DateTypeEnum.Week;
      case 30:
        return DateTypeEnum.Month;

      default:
        return DateTypeEnum.Custom;
    }
  }, [defaultTime, endTime, startTime]);

  const onSelect = (key: DateTypeEnum) => {
    if (key === DateTypeEnum.Week) {
      setStartTime(dayjs().add(-7, "day").format("YYYY-MM-DD"));
      setEndTime(dayjs().format("YYYY-MM-DD"));
    } else if (key === DateTypeEnum.Month) {
      setStartTime(dayjs().add(-30, "day").format("YYYY-MM-DD"));
      setEndTime(dayjs().format("YYYY-MM-DD"));
    } else {
      setOpen(true);
    }
  };

  const items: TabsProps["items"] = useMemo(() => {
    return [
      {
        key: DateTypeEnum.Week,
        label: "7天",
      },
      {
        key: DateTypeEnum.Month,
        label: "30天",
      },
      {
        key: DateTypeEnum.Custom,
        label: "自定义",
      },
    ];
  }, []);

  return (
    <React.Fragment>
      <div className="absolute h-[64px] top-[14px] left-[1510px] flex items-center z-[100]">
        <Tabs
          className="mt-[24px]"
          tabBarGutter={16}
          items={items}
          activeKey={activeKey}
          onTabClick={onSelect}
        />
        {activeKey && (
          <Title className="text-[14px] text-[#C5D0D4] cursor-pointer ml-4 mt-2">
            {startTime} ~ {endTime}
          </Title>
        )}
      </div>
      <RangePicker
        onOpenChange={setOpen}
        open={open}
        onChange={(_, dateString) => {
          setStartTime(dateString[0]);
          setEndTime(dateString[1]);
        }}
        className="absolute right-[26px] opacity-0 top-[50px] -z-10 w-[80px]"
      ></RangePicker>
    </React.Fragment>
  );
}
export default DateQuery;
