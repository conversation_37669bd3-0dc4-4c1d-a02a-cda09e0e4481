import { Outlet, useLocation } from "umi";
import { ConfigProvider, Empty, Modal, message, theme } from "antd";
import PageSelect from "./components/PageSelect";
import DateQuery from "./components/DateQuery";
import ReactScaleScreen from "@/components/ReactScaleScreen";
import bgUrl from "@/assets/titleBg.webp";
import zhCN from "antd/locale/zh_CN";
import "dayjs/locale/zh-cn";
import { useEffect, useMemo, useRef } from "react";
import emptyImage from "@/assets/empty.png";
import Title from "@/components/Title";
import useStore from "@/store";
import useLogin from "@/hooks/useLogin";
import home1Bg from "@/assets/1/pageBg.webp";
import { useMount } from "ahooks";

export default function Layout() {
  const domRef = useRef<HTMLDivElement>(null);

  const { pathname } = useLocation();

  const [messageApi, contextHolder] = message.useMessage();

  const [modalApi, contextModalHolder] = Modal.useModal();

  useLogin();

  useEffect(() => {
    useStore.setState((draft) => {
      draft.messageApi = messageApi;
      draft.modalApi = modalApi;
    });
  }, [messageApi, modalApi]);

  useMount(() => {
    if (pathname === "/home2") {
      // 默认值设置成
      useStore.setState((draft) => {
        draft.startTime = draft.defaultTime;
      });
    } else if (pathname === "/home3") {
      useStore.setState((draft) => {
        const [startTime, endTime] = draft.defaultTime2;
        draft.startTime = startTime;
        draft.endTime = endTime;
      });
    }
  });

  const bgColor = useMemo(() => {
    if (pathname === "/home4") {
      return "radial-gradient(circle, #123066 0%, #031831 35%)";
    } else if (pathname === "/home3") {
      return "radial-gradient(circle, #123066 0%, #031831 35%)";
    }
    return `url(${home1Bg}) no-repeat left top / cover`;
  }, [pathname]);

  return (
    <ConfigProvider
      theme={{
        token: {
          fontSize: 16,
          colorTextBase: "#daedff",
          colorPrimary: "#395ABF",
        },
        components: {
          Select: {
            selectorBg: "transparent",
            optionPadding: "8px 12px",
            optionSelectedBg: "rgba(57,90,191,.65)",
            colorBorder: "#00B4FF",
            colorPrimaryHover: "#00B4FF",
            colorPrimary: "#00B4FF",
            borderRadiusLG: 4,
          },
          DatePicker: {
            cellHeight: 34,
            cellWidth: 34,
            paddingXXS: 2,
            cellActiveWithRangeBg: "rgba(16, 55, 105, 0.65)",
            colorBgElevated:
              "linear-gradient(0deg, rgba(2,29,58,.96) 0%, rgba(2,48,84,.96) 100%)",
          },
          Tooltip: {
            colorBgSpotlight: "rgba(76,146,217,.9)",
          },
          Descriptions: {
            fontSize: 18,
            contentColor: "#C5D0D4",
            titleColor: "#8B969E",
            itemPaddingBottom: 20,
          },
          Input: {
            colorBgContainer: "transparent",
            colorBorder: "#00B4FF",
            colorPrimaryHover: "#00B4FF",
            colorPrimary: "#00B4FF",
          },
          Spin: {
            colorPrimary: "#28b8f2",
          },
          Popover: {
            sizePopupArrow: 0,
            colorBgElevated: "transparent",
          },
          Tabs: {
            colorBorderSecondary: "transparent",
            inkBarColor: "#FFFFFF",
            itemSelectedColor: "#FFFFFF",
            itemHoverColor: "#FFFFFF",
            itemColor: "#C5D0D4",
            fontSize: 14,
            paddingSM: 16,
          },
        },
        algorithm: theme.darkAlgorithm,
      }}
      renderEmpty={() => (
        <Empty
          image={emptyImage}
          imageStyle={{
            textAlign: "center",
            margin: "0 auto",
            height: 80,
            width: 80,
            display: "inline-block",
          }}
        />
      )}
      getPopupContainer={() => domRef.current!}
      locale={zhCN}
    >
      {contextHolder}
      {contextModalHolder}
      <div
        className="w-sreen h-screen relative"
        style={{
          background: bgColor,
        }}
      >
        <ReactScaleScreen>
          <div
            className="absolute h-[134px] w-full left-0 bg-cover pointer-events-none z-[10]"
            style={{
              backgroundImage: `url(${bgUrl})`,
            }}
          >
            <Title className="text-[54px] flex justify-center items-center mt-[18px]">
              广东省网络安全态势感知平台
            </Title>
          </div>
          <PageSelect />
          <DateQuery />
          <Outlet />
          <div className="relative w-0 h-0" ref={domRef}></div>
        </ReactScaleScreen>
      </div>
    </ConfigProvider>
  );
}
