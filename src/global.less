@import "../public/css/font.css";

body,
html {
  font-size: 16px;
  color: #c5d0d4;

  img {
    max-width: initial;
  }
}

.ant-table-wrapper .ant-table-thead > tr > th {
  &::before {
    display: none !important;
  }
}

.ant-picker-range-wrapper {
  border: 1px solid #00b4ff;
  border-radius: 8px;
  text-align: center;
}

.ant-picker-range {
  background: transparent !important;
  border: 1px solid #00B4FF;
}

.ant-select-dropdown {
  background: linear-gradient(
    0deg,
    rgba(2, 29, 58, 0.96) 0%,
    rgba(2, 48, 84, 0.96) 100%
  );
  border: 1px solid #00b4ff;
}

.recharts-wrapper {
  svg {
    overflow: visible;
  }
}

.ant-spin {
  max-height: 100% !important;
}

@keyframes moveOpacity {
  0% {
    opacity: 1;
    transform: translateY(0);
  }

  50% {
    opacity: 0.8;
    transform: translateY(6px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 逆时针旋转
@keyframes rotateCounterClockwise {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  to {
    -webkit-transform: rotate(-365deg);
    transform: rotate(-365deg);
  }
}

// 顺时针旋转
@keyframes rotateClockwise {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  to {
    -webkit-transform: rotate(365deg);
    transform: rotate(365deg);
  }
}

// 上下移动
@keyframes moveUpDown {
  0% {
    -webkit-transform: translate(0);
    transform: translate(0);
    opacity: 1;
  }

  25% {
    -webkit-transform: translateY(5px);
    transform: translateY(5px);
    opacity: 0.9;
  }

  50% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 0.8;
  }

  75% {
    -webkit-transform: translateY(-5px);
    transform: translateY(-5px);
    opacity: 0.9;
  }

  to {
    -webkit-transform: translateY(0);
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes floorKeyframes1 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 1;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 0;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes2 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 0;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 1;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes3 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 0;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 0;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes4 {
  0% {
    opacity: 0;
  }
  33% {
    opacity: 1;
  }
  60% {
    opacity: 0;
  }
  66% {
    opacity: 0;
  }
  72% {
    opacity: 0;
  }
  95% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes5 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 0;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 1;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes floorKeyframes6 {
  0% {
    opacity: 0;
  }
  26% {
    opacity: 0;
  }
  46% {
    opacity: 0;
  }
  53% {
    opacity: 0;
  }
  73% {
    opacity: 0;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes breath {
  0% {
    opacity: 1;
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }

  50% {
    opacity: 0.3;
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  to {
    opacity: 1;
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }
}

@keyframes breathL {
  0% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    opacity: 0.2;
    -webkit-transform: translateX(-25%);
    transform: translateX(-25%);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes breathR {
  0% {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  50% {
    opacity: 0.2;
    -webkit-transform: translateX(25%);
    transform: translateX(25%);
  }

  to {
    opacity: 1;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

@keyframes breathMoveOpacity {
  0% {
    opacity: 0;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  65% {
    opacity: 1;
    -webkit-transform: translateX(50%);
    transform: translateX(50%);
  }

  to {
    opacity: 0;
    -webkit-transform: translateX(60%);
    transform: translateX(60%);
  }
}

@keyframes rotationL {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  to {
    -webkit-transform: rotate(365deg);
    transform: rotate(365deg);
  }
}

@keyframes rotationR {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  to {
    -webkit-transform: rotate(-365deg);
    transform: rotate(-365deg);
  }
}

@keyframes breath4 {
  0% {
    opacity: 0.3;
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  50% {
    opacity: 1;
    -webkit-transform: scale(1.1);
    transform: scale(1.1);
  }

  to {
    opacity: 0.3;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes flexAble {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }

  to {
    -webkit-transform: translateX(200%);
    transform: translateX(200%);
  }
}

@keyframes globalCircle {
  0% {
    -webkit-transform: rotateX(-90deg) rotateY(0deg);
    transform: rotateX(-90deg) rotateY(0deg);
  }

  to {
    -webkit-transform: rotateX(-90deg) rotateY(1turn);
    transform: rotateX(-90deg) rotateY(1turn);
  }
}

.floor-hover {
  cursor: pointer;
  z-index: 10;
  position: relative;

  &:hover {
    opacity: 1 !important;
    animation-play-state: paused !important;
  }
}

.recharts-layer {
  border: none;
  outline: none;
}

.custom-recharts-brush {
  .recharts-brush-traveller {
    &:nth-of-type(3) {
      image {
        transform: scaleX(-1);
      }
    }
  }

  .recharts-brush-slide {
    fill: #005daa;
    fill-opacity: 0.7;
  }

  .recharts-brush-texts {
    .recharts-text {
      font-size: 11px;
      fill: rgba(255, 255, 255, 0.45);

      &:nth-of-type(1) {
        transform: translate(32px, 20px);
      }

      &:nth-of-type(2) {
        transform: translate(-43px, 20px);
      }
    }
  }
}
